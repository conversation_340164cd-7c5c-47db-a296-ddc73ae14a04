# For use with Easy-RSA 3.0+ and OpenSSL or LibreSSL

####################################################################
[ ca ]
default_ca	= CA_default		# The default ca section

####################################################################
[ CA_default ]

dir		= $ENV::EASYRSA_PKI	# Where everything is kept
certs		= $dir			# Where the issued certs are kept
crl_dir		= $dir			# Where the issued crl are kept
database	= $dir/index.txt	# database index file.
new_certs_dir	= $dir/certs_by_serial	# default place for new certs.

certificate	= $dir/ca.crt		# The CA certificate
serial		= $dir/serial		# The current serial number
crl		= $dir/crl.pem		# The current CRL
private_key	= $dir/private/ca.key	# The private key
RANDFILE	= $dir/.rand		# private random number file

x509_extensions	= basic_exts		# The extensions to add to the cert

# A placeholder to handle the --copy-ext feature:
#%COPY_EXTS%	# Do NOT remove or change this line as --copy-ext support requires it

# This allows a V2 CRL. Ancient browsers don't like it, but anything Easy-RSA
# is designed for will. In return, we get the Issuer attached to CRLs.
crl_extensions	= crl_ext

default_days	= $ENV::EASYRSA_CERT_EXPIRE	# how long to certify for
default_crl_days	= $ENV::EASYRSA_CRL_DAYS	# how long before next CRL
default_md	= $ENV::EASYRSA_DIGEST		# use public key default MD

# Note: preserve=no|yes, does nothing for EasyRSA.
# Use sign-req command option 'preserve' instead.
preserve	= no			# keep passed DN ordering

# This allows to renew certificates which have not been revoked
unique_subject	= no

# A few different ways of specifying how similar the request should look
# For type CA, the listed attributes must be the same, and the optional
# and supplied fields are just that :-)
policy		= policy_anything

# For the 'anything' policy, which defines allowed DN fields
[ policy_anything ]
countryName		= optional
stateOrProvinceName	= optional
localityName		= optional
organizationName	= optional
organizationalUnitName	= optional
commonName		= supplied
emailAddress		= optional
serialNumber	= optional

####################################################################
# Easy-RSA request handling
# We key off $DN_MODE to determine how to format the DN
[ req ]
default_bits		= $ENV::EASYRSA_KEY_SIZE
default_keyfile	= privkey.pem
default_md		= $ENV::EASYRSA_DIGEST
distinguished_name	= $ENV::EASYRSA_DN
x509_extensions		= easyrsa_ca	# The extensions to add to the self signed cert

# A placeholder to handle the $EXTRA_EXTS feature:
#%EXTRA_EXTS%	# Do NOT remove or change this line as $EXTRA_EXTS support requires it

####################################################################
# Easy-RSA DN (Subject) handling

# Easy-RSA DN for cn_only support:
[ cn_only ]
commonName		= Common Name (eg: your user, host, or server name)
commonName_max		= 64
commonName_default	= $ENV::EASYRSA_REQ_CN

# Easy-RSA DN for org support:
[ org ]
countryName			= Country Name (2 letter code)
countryName_default		= $ENV::EASYRSA_REQ_COUNTRY
countryName_min			= 2
countryName_max			= 2

stateOrProvinceName		= State or Province Name (full name)
stateOrProvinceName_default	= $ENV::EASYRSA_REQ_PROVINCE

localityName			= Locality Name (eg, city)
localityName_default		= $ENV::EASYRSA_REQ_CITY

0.organizationName		= Organization Name (eg, company)
0.organizationName_default	= $ENV::EASYRSA_REQ_ORG

organizationalUnitName		= Organizational Unit Name (eg, section)
organizationalUnitName_default	= $ENV::EASYRSA_REQ_OU

commonName			= Common Name (eg: your user, host, or server name)
commonName_max			= 64
commonName_default		= $ENV::EASYRSA_REQ_CN

emailAddress			= Email Address
emailAddress_default		= $ENV::EASYRSA_REQ_EMAIL
emailAddress_max		= 64

serialNumber		= Serial-number (eg, device serial-number)
serialNumber_default	= $ENV::EASYRSA_REQ_SERIAL

####################################################################
# Easy-RSA cert extension handling

# This section is effectively unused as the main script sets extensions
# dynamically. This core section is left to support the odd usecase where
# a user calls openssl directly.
[ basic_exts ]
basicConstraints	= CA:FALSE
subjectKeyIdentifier	= hash
authorityKeyIdentifier	= keyid,issuer:always

# The Easy-RSA CA extensions
[ easyrsa_ca ]

# PKIX recommendations:

subjectKeyIdentifier=hash
authorityKeyIdentifier=keyid:always,issuer:always

# This could be marked critical, but it's nice to support reading by any
# broken clients who attempt to do so.
basicConstraints = CA:true

# Limit key usage to CA tasks. If you really want to use the generated pair as
# a self-signed cert, comment this out.
keyUsage = cRLSign, keyCertSign

# nsCertType omitted by default. Let's try to let the deprecated stuff die.
# nsCertType = sslCA

# A placeholder to handle the $X509_TYPES and CA extra extensions $EXTRA_EXTS:
#%CA_X509_TYPES_EXTRA_EXTS%	# Do NOT remove or change this line as $X509_TYPES and EXTRA_EXTS demands it

# CRL extensions.
[ crl_ext ]

# Only issuerAltName and authorityKeyIdentifier make any sense in a CRL.

# issuerAltName=issuer:copy
authorityKeyIdentifier=keyid:always,issuer:always
