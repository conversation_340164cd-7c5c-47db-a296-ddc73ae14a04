# X509 extensions added to every signed cert

# This file is included for every cert signed, and by default does nothing.
# It could be used to add values every cert should have, such as a CDP as
# demonstrated in the following example:

#crlDistributionPoints = URI:http://example.net/pki/my_ca.crl

# The authority information access extension gives details about how to access
# certain information relating to the CA.

#authorityInfoAccess = caIssuers;URI:http://example.net/pki/my_ca.crt
