# Easy-RSA 3 parameter settings
# Configuração para VPN Fase 1 - Rede 10.12.0.0/24

# Configurações de PKI
set_var EASYRSA_REQ_COUNTRY    "BR"
set_var EASYRSA_REQ_PROVINCE   "SP"
set_var EASYRSA_REQ_CITY       "Sao Paulo"
set_var EASYRSA_REQ_ORG        "EVO-EDEN VPN"
set_var EASYRSA_REQ_EMAIL      "<EMAIL>"
set_var EASYRSA_REQ_OU         "VPN Department"

# Configurações de chaves
set_var EASYRSA_KEY_SIZE       2048
set_var EASYRSA_ALGO           rsa
set_var EASYRSA_CA_EXPIRE      3650
set_var EASYRSA_CERT_EXPIRE    3650
set_var EASYRSA_CRL_DAYS       180

# Configurações de diretório
set_var EASYRSA_PKI            "/root/VPN/OpenVPN/easy-rsa/pki"
set_var EASYRSA_DN             "cn_only"
set_var EASYRSA_REQ_CN         "EVO-EDEN-VPN-CA"

# Configurações de segurança
set_var EASYRSA_DIGEST         "sha256"
set_var EASYRSA_BATCH          "1"

# Configurações específicas para OpenVPN
set_var EASYRSA_NS_SUPPORT     "no"
set_var EASYRSA_NS_COMMENT     "EVO-EDEN VPN Certificate"
set_var EASYRSA_EXT_DIR        "/root/VPN/OpenVPN/easy-rsa/x509-types"
