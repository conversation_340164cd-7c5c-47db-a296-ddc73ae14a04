# 📚 DOCUMENTAÇÃO COMPLETA PARA CRIAÇÃO DE VPN AUTOMATIZADA

## 🎯 Objetivo
Este documento fornece instruções detalhadas para uma IA criar um sistema VPN OpenVPN completo com automação, baseado nas dificuldades e soluções encontradas durante a implementação real.

---

## ⚠️ AVISOS CRÍTICOS - LEIA ANTES DE COMEÇAR

### 🚨 Problemas Comuns e Como Evitá-los:

1. **NUNCA edite arquivos de configuração diretamente sem verificar sintaxe**
2. **SEMPRE verifique permissões de arquivos antes de executar**
3. **JAMAIS use `comp-lzo` em versões modernas do OpenVPN (deprecated)**
4. **OBRIGATÓRIO verificar se portas estão livres antes de configurar**
5. **ESSENCIAL criar diretórios de logs antes de iniciar serviços**

---

## 🏗️ ETAPA 1: PREPARAÇÃO DO AMBIENTE

### 1.1 Estrutura de Diretórios
```bash
# CRÍTICO: Criar estrutura EXATA - qualquer desvio causará erros
mkdir -p /root/VPN/OpenVPN/{configs,scripts,clients,logs}
cd /root/VPN/OpenVPN
```

**⚠️ ATENÇÃO**: Se não criar a estrutura exata, os scripts falharão ao tentar acessar diretórios inexistentes.

### 1.2 Instalação de Dependências
```bash
# OBRIGATÓRIO: Instalar TODAS as dependências
apt update
apt install -y openvpn easy-rsa iptables-persistent curl jq

# CRÍTICO: Verificar se instalação foi bem-sucedida
which openvpn || echo "ERRO: OpenVPN não instalado"
which easy-rsa || echo "ERRO: Easy-RSA não instalado"
```

**🔥 PROBLEMA COMUM**: Pular verificação de instalação causa falhas silenciosas depois.

---

## 🔐 ETAPA 2: CONFIGURAÇÃO DE CERTIFICADOS (EASY-RSA)

### 2.1 Inicialização do Easy-RSA
```bash
# CRÍTICO: Copiar easy-rsa para diretório de trabalho
cp -r /usr/share/easy-rsa /root/VPN/OpenVPN/easy-rsa
cd /root/VPN/OpenVPN/easy-rsa

# OBRIGATÓRIO: Inicializar PKI
./easyrsa init-pki
```

**⚠️ ERRO COMUM**: Tentar usar easy-rsa sem inicializar PKI resulta em "PKI not found".

### 2.2 Configuração do arquivo vars
```bash
# CRÍTICO: Criar arquivo vars com configurações corretas
cat > vars << 'EOF'
set_var EASYRSA_REQ_COUNTRY    "BR"
set_var EASYRSA_REQ_PROVINCE   "SP"
set_var EASYRSA_REQ_CITY       "Sao Paulo"
set_var EASYRSA_REQ_ORG        "VPN-Server"
set_var EASYRSA_REQ_EMAIL      "<EMAIL>"
set_var EASYRSA_REQ_OU         "IT Department"
set_var EASYRSA_KEY_SIZE       2048
set_var EASYRSA_ALGO           rsa
set_var EASYRSA_CA_EXPIRE      3650
set_var EASYRSA_CERT_EXPIRE    365
EOF
```

**🚨 PROBLEMA CRÍTICO**: Sem o arquivo vars, o easy-rsa usará valores padrão que podem causar problemas de validação.

### 2.3 Geração de Certificados
```bash
# SEQUÊNCIA OBRIGATÓRIA - NÃO ALTERAR ORDEM:

# 1. CA (Certificate Authority)
./easyrsa build-ca nopass

# 2. Certificado do servidor
./easyrsa gen-req server nopass
./easyrsa sign-req server server

# 3. Parâmetros Diffie-Hellman (DEMORADO - pode levar 10+ minutos)
./easyrsa gen-dh

# 4. Chave TLS-Auth
openvpn --genkey secret pki/ta.key
```

**⚠️ ERRO FATAL**: Alterar a ordem ou pular etapas resulta em certificados inválidos.

**🔥 PROBLEMA COMUM**: O comando `gen-dh` é MUITO lento. Para acelerar em ambiente de teste:
```bash
# ALTERNATIVA RÁPIDA (apenas para testes):
openssl dhparam -out pki/dh.pem 2048
```

---

## ⚙️ ETAPA 3: CONFIGURAÇÃO DO SERVIDOR OPENVPN

### 3.1 Arquivo de Configuração Principal
```bash
# CRÍTICO: Configuração EXATA - qualquer erro impede inicialização
cat > /root/VPN/OpenVPN/configs/server.conf << 'EOF'
# Configuração do Servidor OpenVPN
port 1194
proto udp
dev tun

# Certificados e chaves - CAMINHOS ABSOLUTOS OBRIGATÓRIOS
ca /root/VPN/OpenVPN/easy-rsa/pki/ca.crt
cert /root/VPN/OpenVPN/easy-rsa/pki/issued/server.crt
key /root/VPN/OpenVPN/easy-rsa/pki/private/server.key
dh /root/VPN/OpenVPN/easy-rsa/pki/dh.pem

# Configuração de rede VPN
topology subnet
server ********* *************
ifconfig-pool-persist /root/VPN/OpenVPN/logs/ipp.txt

# Roteamento - ESSENCIAL para acesso à internet
push "redirect-gateway def1 bypass-dhcp"
push "dhcp-option DNS *******"
push "dhcp-option DNS *******"

# Configurações de segurança
tls-auth /root/VPN/OpenVPN/easy-rsa/pki/ta.key 0
cipher AES-256-GCM
auth SHA256

# Configurações de conexão
keepalive 10 120
persist-key
persist-tun

# NUNCA usar comp-lzo (deprecated)
# comp-lzo  # ← REMOVER ESTA LINHA

# Logs - DIRETÓRIO DEVE EXISTIR
status /root/VPN/OpenVPN/logs/openvpn-status.log
log-append /root/VPN/OpenVPN/logs/openvpn.log
verb 3

# Configurações de usuário (comentar se der problema)
#user nobody
#group nogroup
EOF
```

**🚨 ERROS CRÍTICOS A EVITAR**:
1. **Caminhos relativos**: Sempre usar caminhos absolutos
2. **comp-lzo**: Deprecated, causa warnings
3. **Diretórios inexistentes**: Criar `/logs/` antes
4. **user/group**: Pode causar problemas de permissão

### 3.2 Criação de Logs
```bash
# OBRIGATÓRIO: Criar arquivos de log antes de iniciar
mkdir -p /root/VPN/OpenVPN/logs
touch /root/VPN/OpenVPN/logs/openvpn.log
touch /root/VPN/OpenVPN/logs/openvpn-status.log
touch /root/VPN/OpenVPN/logs/ipp.txt
chmod 644 /root/VPN/OpenVPN/logs/*
```

**⚠️ PROBLEMA COMUM**: OpenVPN falha se não conseguir criar arquivos de log.

### 3.3 Configuração de Firewall
```bash
# CRÍTICO: Configurar iptables ANTES de iniciar OpenVPN
# 1. Habilitar IP forwarding
echo 'net.ipv4.ip_forward=1' >> /etc/sysctl.conf
sysctl -p

# 2. Configurar NAT (ADAPTAR INTERFACE DE REDE)
INTERFACE=$(ip route | grep default | awk '{print $5}' | head -1)
iptables -t nat -A POSTROUTING -s *********/24 -o $INTERFACE -j MASQUERADE
iptables -A INPUT -p udp --dport 1194 -j ACCEPT
iptables -A FORWARD -m state --state RELATED,ESTABLISHED -j ACCEPT
iptables -A FORWARD -s *********/24 -j ACCEPT

# 3. Salvar regras
iptables-save > /etc/iptables/rules.v4
```

**🔥 ERRO FATAL**: Sem configuração de firewall, clientes conectam mas não acessam internet.

---

## 🚀 ETAPA 4: INICIALIZAÇÃO DO OPENVPN

### 4.1 Método Recomendado (Daemon)
```bash
# COPIAR configuração para local padrão
cp /root/VPN/OpenVPN/configs/server.conf /etc/openvpn/server.conf

# INICIAR como daemon (MÉTODO MAIS ESTÁVEL)
openvpn --daemon --config /etc/openvpn/server.conf --writepid /var/run/openvpn/server.pid
```

**⚠️ PROBLEMA COM SYSTEMD**: O systemd pode ter problemas com reinicializações rápidas. Use daemon manual.

### 4.2 Verificação de Funcionamento
```bash
# VERIFICAÇÕES OBRIGATÓRIAS:
# 1. Processo rodando
ps aux | grep openvpn

# 2. Interface tun0 criada
ip addr show tun0

# 3. Porta aberta
netstat -tulpn | grep 1194

# 4. Logs sem erros críticos
tail /root/VPN/OpenVPN/logs/openvpn.log
```

**🚨 SE ALGUMA VERIFICAÇÃO FALHAR**: Parar tudo e revisar configuração.

---

## 🐳 ETAPA 5: API DE AUTOMAÇÃO (DOCKER)

### 5.1 Estrutura da API
```bash
mkdir -p /root/VPN/OpenVPN/api
cd /root/VPN/OpenVPN/api
```

### 5.2 Dockerfile
```dockerfile
FROM node:18-alpine

# CRÍTICO: Instalar dependências do sistema
RUN apk add --no-cache bash curl openssh-client openssl easy-rsa

WORKDIR /app

COPY package*.json ./
RUN npm install

COPY . .

EXPOSE 8080

CMD ["npm", "start"]
```

**⚠️ PROBLEMA COMUM**: Esquecer `openssl` e `easy-rsa` no container causa falhas na geração de certificados.

### 5.3 package.json
```json
{
  "name": "vpn-api",
  "version": "1.0.0",
  "description": "API para automação de criação de clientes VPN",
  "main": "server.js",
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "morgan": "^1.10.0",
    "multer": "^1.4.4"
  },
  "devDependencies": {
    "nodemon": "^3.0.1"
  }
}
```

**🔥 VERSÃO CRÍTICA**: `multer: "^1.4.4"` - versões mais novas podem ter problemas de compatibilidade.

### 5.4 server.js (Versão Simplificada)
```javascript
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 8080;

// Middlewares
app.use(helmet({
    contentSecurityPolicy: false,
    crossOriginEmbedderPolicy: false
}));
app.use(cors({
    origin: ['https://vpn.evo-eden.site', 'http://vpn.evo-eden.site'],
    credentials: true
}));
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Função para criar cliente VPN
function createVPNClient(password, clientName, platform) {
    return new Promise((resolve, reject) => {
        // Verificar senha
        if (password !== 'etenopenvpn') {
            return reject(new Error('Senha incorreta'));
        }

        // Validar nome do cliente
        if (!/^[a-zA-Z0-9_-]+$/.test(clientName)) {
            return reject(new Error('Nome do cliente inválido'));
        }

        // Verificar se cliente já existe
        const certPath = `/vpn-data/easy-rsa/pki/issued/${clientName}.crt`;
        if (fs.existsSync(certPath)) {
            return reject(new Error('Cliente já existe'));
        }

        // Por enquanto, apenas registrar a solicitação
        console.log(`Solicitação para criar cliente: ${clientName} (${platform})`);
        
        // Registrar no log
        const logEntry = `${new Date().toISOString()},${clientName},${platform},requested_via_api\n`;
        fs.appendFileSync('/vpn-data/logs/clients-registry.log', logEntry);
        
        // Simular sucesso
        setTimeout(() => {
            console.log(`Cliente ${clientName} registrado com sucesso`);
            resolve({
                success: true,
                message: 'Solicitação de cliente registrada com sucesso',
                clientName: clientName,
                platform: platform,
                note: 'Certificado será gerado manualmente pelo administrador'
            });
        }, 1000);
    });
}

// Rota para criar cliente
app.post('/api/create-client', async (req, res) => {
    try {
        const { password, clientName, platform } = req.body;

        console.log(`Requisição para criar cliente: ${clientName} (${platform})`);

        if (!password || !clientName || !platform) {
            return res.status(400).json({
                success: false,
                error: 'Parâmetros obrigatórios: password, clientName, platform'
            });
        }

        const result = await createVPNClient(password, clientName, platform);
        
        res.json(result);
    } catch (error) {
        console.error('Erro na API:', error.message);
        res.status(400).json({
            success: false,
            error: error.message
        });
    }
});

// Rota para download do arquivo .ovpn
app.get('/api/download/:clientName', (req, res) => {
    const { clientName } = req.params;
    const filePath = `/vpn-data/clients/${clientName}.ovpn`;

    if (!fs.existsSync(filePath)) {
        return res.status(404).json({
            success: false,
            error: 'Arquivo não encontrado'
        });
    }

    res.download(filePath, `${clientName}.ovpn`);
});

// Rota de health check
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        message: 'API VPN funcionando',
        timestamp: new Date().toISOString()
    });
});

// Iniciar servidor
app.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 API VPN rodando na porta ${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
});
```

**⚠️ VERSÃO SIMPLIFICADA**: Esta versão apenas registra solicitações. Para implementação completa de geração de certificados, é necessário integração mais complexa com easy-rsa.

### 5.5 docker-compose.yml
```yaml
version: '3.8'

services:
  api-vpn:
    build: .
    container_name: API-VPN
    ports:
      - "8080:8080"
    volumes:
      - /root/VPN/OpenVPN:/vpn-data
    environment:
      - NODE_ENV=production
      - PORT=8080
    restart: unless-stopped
    networks:
      - vpn-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  vpn-network:
    driver: bridge
```

**🔥 VOLUME CRÍTICO**: `/root/VPN/OpenVPN:/vpn-data` - permite acesso aos certificados do host.

### 5.6 Construção e Execução
```bash
cd /root/VPN/OpenVPN/api

# INSTALAR Docker se necessário
curl -fsSL https://get.docker.com | sh

# CONSTRUIR e INICIAR
docker-compose up -d --build

# VERIFICAR funcionamento
curl http://localhost:8080/api/health
```

---

## 📜 ETAPA 6: SCRIPTS DE AUTOMAÇÃO

### 6.1 Script para Administradores
```bash
# Arquivo: /root/VPN/OpenVPN/scripts/create-client-interactive.sh
# FUNÇÃO: Criar certificados de clientes manualmente
# CRÍTICO: Deve ter permissão de execução (chmod +x)
```

**⚠️ DETALHES IMPORTANTES**:
- Usar `EASYRSA_BATCH=1` para modo não-interativo
- Validar entrada do usuário (regex: `^[a-zA-Z0-9_-]+$`)
- Criar arquivo .ovpn com template correto
- Registrar em log com timestamp

### 6.2 Script de Monitoramento
```bash
# Arquivo: /root/VPN/OpenVPN/scripts/monitor-vpn.sh
# FUNÇÃO: Verificar status de todos os componentes
# VERIFICAÇÕES OBRIGATÓRIAS:
# - Processo OpenVPN rodando
# - Interface tun0 ativa
# - Porta 1194 aberta
# - API respondendo
# - Container Docker ativo
# - Logs sem erros críticos
```

### 6.3 Scripts para Clientes
```powershell
# Windows: windows-setup.ps1
# REQUISITOS:
# - Executar como Administrador
# - Verificar se OpenVPN está instalado
# - Baixar/instalar se necessário
# - Testar conectividade com API
# - Solicitar certificado via API
```

```bash
# Raspberry Pi: raspberry-setup.sh
# REQUISITOS:
# - Executar como root (sudo)
# - Instalar dependências (openvpn, curl, jq)
# - Criar scripts de conexão/desconexão
# - Configurar serviço systemd opcional
```

---

## 🔧 ETAPA 7: RESOLUÇÃO DE PROBLEMAS COMUNS

### 7.1 OpenVPN não inicia
```bash
# DIAGNÓSTICO:
journalctl -xeu <EMAIL>
tail /root/VPN/OpenVPN/logs/openvpn.log

# SOLUÇÕES COMUNS:
# 1. Verificar permissões dos certificados
chmod 644 /root/VPN/OpenVPN/easy-rsa/pki/ca.crt
chmod 644 /root/VPN/OpenVPN/easy-rsa/pki/issued/server.crt
chmod 600 /root/VPN/OpenVPN/easy-rsa/pki/private/server.key

# 2. Verificar se porta está livre
netstat -tulpn | grep 1194

# 3. Remover linhas problemáticas da configuração
# - comp-lzo (deprecated)
# - user/group (pode causar problemas)
```

### 7.2 Interface tun0 não criada
```bash
# CAUSA: Módulo tun não carregado
modprobe tun
echo 'tun' >> /etc/modules

# VERIFICAR:
lsmod | grep tun
```

### 7.3 Clientes conectam mas não acessam internet
```bash
# CAUSA: Firewall/NAT não configurado
# SOLUÇÃO: Reconfigurar iptables (ver Etapa 3.3)

# VERIFICAR IP forwarding:
cat /proc/sys/net/ipv4/ip_forward  # deve retornar 1
```

### 7.4 API Docker não responde
```bash
# DIAGNÓSTICO:
docker-compose logs api-vpn

# SOLUÇÕES:
# 1. Verificar se porta 8080 está livre
# 2. Reconstruir container: docker-compose up -d --build
# 3. Verificar volumes montados corretamente
```

---

## ✅ CHECKLIST FINAL DE VERIFICAÇÃO

### Antes de considerar concluído:
- [ ] OpenVPN rodando (ps aux | grep openvpn)
- [ ] Interface tun0 ativa (ip addr show tun0)
- [ ] Porta 1194 aberta (netstat -tulpn | grep 1194)
- [ ] API respondendo (curl http://localhost:8080/api/health)
- [ ] Container Docker saudável (docker ps)
- [ ] Firewall configurado (iptables -L)
- [ ] IP forwarding habilitado (cat /proc/sys/net/ipv4/ip_forward)
- [ ] Logs sem erros críticos (tail /root/VPN/OpenVPN/logs/openvpn.log)
- [ ] Scripts executáveis (ls -la /root/VPN/OpenVPN/scripts/)
- [ ] Estrutura de diretórios completa

### Teste final:
```bash
# Executar script de monitoramento
/root/VPN/OpenVPN/scripts/monitor-vpn.sh

# Deve mostrar todos os componentes funcionando
```

---

## 🎯 CONSIDERAÇÕES FINAIS

### Pontos Críticos Aprendidos:
1. **Ordem das operações é fundamental** - não pular etapas
2. **Permissões de arquivos causam 80% dos problemas**
3. **Logs são essenciais** - sempre verificar antes de prosseguir
4. **Firewall é obrigatório** - sem ele, VPN não funciona completamente
5. **Docker simplifica API** - mas volumes devem estar corretos
6. **Systemd pode ser problemático** - daemon manual é mais estável

### Próximos Passos Recomendados:
1. Implementar geração automática de certificados na API
2. Adicionar autenticação mais robusta
3. Criar dashboard web para administração
4. Implementar backup automático de certificados
5. Adicionar monitoramento com alertas

---

## 📋 APÊNDICE A: SCRIPTS COMPLETOS

### A.1 Script de Criação de Cliente (create-client-interactive.sh)
```bash
#!/bin/bash
# CRÍTICO: Este script deve ser executado no diretório easy-rsa
# LOCALIZAÇÃO: /root/VPN/OpenVPN/scripts/create-client-interactive.sh

# Configurações
VPN_DIR="/root/VPN/OpenVPN"
EASY_RSA_DIR="$VPN_DIR/easy-rsa"
CLIENTS_DIR="$VPN_DIR/clients"
LOGS_DIR="$VPN_DIR/logs"

# VALIDAÇÃO CRÍTICA: Verificar se diretórios existem
if [ ! -d "$EASY_RSA_DIR" ]; then
    echo "ERRO: Diretório easy-rsa não encontrado: $EASY_RSA_DIR"
    exit 1
fi

# Função para validar nome do cliente
validate_client_name() {
    local name="$1"
    if [[ ! "$name" =~ ^[a-zA-Z0-9_-]+$ ]]; then
        echo "ERRO: Nome inválido. Use apenas letras, números, _ e -"
        return 1
    fi
    return 0
}

# Função principal
create_client() {
    local client_name="$1"
    local platform="$2"

    cd "$EASY_RSA_DIR" || exit 1

    # CRÍTICO: Verificar se cliente já existe
    if [ -f "pki/issued/${client_name}.crt" ]; then
        echo "ERRO: Cliente $client_name já existe!"
        return 1
    fi

    # Gerar certificado (MODO BATCH OBRIGATÓRIO)
    EASYRSA_BATCH=1 ./easyrsa gen-req "$client_name" nopass
    EASYRSA_BATCH=1 ./easyrsa sign-req client "$client_name"

    # Verificar se geração foi bem-sucedida
    if [ ! -f "pki/issued/${client_name}.crt" ]; then
        echo "ERRO: Falha na geração do certificado"
        return 1
    fi

    # Criar arquivo .ovpn (TEMPLATE EXATO)
    cat > "$CLIENTS_DIR/${client_name}.ovpn" << EOF
# Cliente OpenVPN - $client_name
# Plataforma: $platform
# Criado em: $(date)

client
dev tun
proto udp
remote vpn.evo-eden.site 1194
resolv-retry infinite
nobind
persist-key
persist-tun
verb 3

<ca>
$(cat pki/ca.crt)
</ca>

<cert>
$(cat pki/issued/${client_name}.crt)
</cert>

<key>
$(cat pki/private/${client_name}.key)
</key>

<tls-auth>
$(cat pki/ta.key)
</tls-auth>
key-direction 1
EOF

    # Registrar no log
    echo "$(date '+%Y-%m-%d %H:%M:%S'),$client_name,$platform,created_manual" >> "$LOGS_DIR/clients-registry.log"

    echo "✅ Cliente $client_name criado com sucesso!"
    echo "📁 Arquivo: $CLIENTS_DIR/${client_name}.ovpn"
}

# Execução principal
if [ "$3" = "auto" ]; then
    # Modo automático (chamado pela API)
    create_client "$1" "$2"
else
    # Modo interativo
    echo "=== Criação de Cliente VPN ==="
    read -p "Nome do cliente: " CLIENT_NAME
    read -p "Plataforma (windows/android/ios/linux): " PLATFORM

    validate_client_name "$CLIENT_NAME" || exit 1
    create_client "$CLIENT_NAME" "$PLATFORM"
fi
```

**🚨 ERROS CRÍTICOS A EVITAR**:
- Não executar no diretório correto
- Esquecer modo BATCH (EASYRSA_BATCH=1)
- Template .ovpn incorreto
- Não verificar se certificado foi gerado

### A.2 Script de Monitoramento Detalhado
```bash
#!/bin/bash
# FUNÇÃO: Monitoramento completo do sistema VPN
# LOCALIZAÇÃO: /root/VPN/OpenVPN/scripts/monitor-vpn.sh

# CONFIGURAÇÕES CRÍTICAS
VPN_DIR="/root/VPN/OpenVPN"
LOG_DIR="$VPN_DIR/logs"
REPORT_FILE="$LOG_DIR/vpn-report-$(date +%Y%m%d-%H%M%S).txt"

# Função para verificar processo OpenVPN
check_openvpn_process() {
    if pgrep openvpn >/dev/null; then
        local pid=$(pgrep openvpn)
        echo "✅ OpenVPN: RODANDO (PID: $pid)"

        # Verificar há quanto tempo está rodando
        local uptime=$(ps -o etime= -p $pid | tr -d ' ')
        echo "   Uptime: $uptime"

        return 0
    else
        echo "❌ OpenVPN: NÃO ESTÁ RODANDO"
        return 1
    fi
}

# Função para verificar interface tun0
check_tun_interface() {
    if ip addr show tun0 >/dev/null 2>&1; then
        local ip=$(ip addr show tun0 | grep 'inet ' | awk '{print $2}')
        echo "✅ Interface tun0: ATIVA ($ip)"

        # Verificar estatísticas da interface
        local stats=$(cat /proc/net/dev | grep tun0)
        if [ -n "$stats" ]; then
            local rx_bytes=$(echo $stats | awk '{print $2}')
            local tx_bytes=$(echo $stats | awk '{print $10}')
            echo "   RX: $rx_bytes bytes, TX: $tx_bytes bytes"
        fi

        return 0
    else
        echo "❌ Interface tun0: INATIVA"
        return 1
    fi
}

# Função para verificar clientes conectados
check_connected_clients() {
    local status_file="$LOG_DIR/openvpn-status.log"

    if [ -f "$status_file" ]; then
        # Contar clientes ativos
        local count=$(grep "^CLIENT_LIST" "$status_file" 2>/dev/null | wc -l)
        echo "📊 Clientes conectados: $count"

        if [ "$count" -gt 0 ]; then
            echo "👥 Lista de clientes:"
            grep "^CLIENT_LIST" "$status_file" | while IFS=',' read -r prefix name real_ip virtual_ip bytes_rx bytes_tx connected_since; do
                echo "   - $name: $real_ip → $virtual_ip (conectado desde: $connected_since)"
            done
        fi
    else
        echo "⚠️ Arquivo de status não encontrado: $status_file"
    fi
}

# Função para verificar logs de erro
check_error_logs() {
    local openvpn_log="$LOG_DIR/openvpn.log"

    if [ -f "$openvpn_log" ]; then
        # Procurar por erros nas últimas 100 linhas
        local errors=$(tail -100 "$openvpn_log" | grep -i "error\|failed\|fatal" | wc -l)
        local warnings=$(tail -100 "$openvpn_log" | grep -i "warning" | wc -l)

        echo "📋 Análise de logs:"
        echo "   Erros recentes: $errors"
        echo "   Avisos recentes: $warnings"

        if [ "$errors" -gt 0 ]; then
            echo "🚨 Últimos erros encontrados:"
            tail -100 "$openvpn_log" | grep -i "error\|failed\|fatal" | tail -3 | while read line; do
                echo "   ❌ $line"
            done
        fi
    else
        echo "⚠️ Log do OpenVPN não encontrado: $openvpn_log"
    fi
}

# Função para verificar recursos do sistema
check_system_resources() {
    echo "💻 Recursos do sistema:"

    # CPU
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    echo "   CPU: ${cpu_usage}%"

    # Memória
    local mem_total=$(free -m | grep Mem | awk '{print $2}')
    local mem_used=$(free -m | grep Mem | awk '{print $3}')
    local mem_percent=$(echo "scale=1; $mem_used * 100 / $mem_total" | bc)
    echo "   Memória: ${mem_used}MB/${mem_total}MB (${mem_percent}%)"

    # Disco
    local disk_usage=$(df / | tail -1 | awk '{print $5}')
    echo "   Disco: $disk_usage"

    # Load average
    local load=$(uptime | awk -F'load average:' '{print $2}')
    echo "   Load average:$load"
}

# Função para verificar conectividade de rede
check_network_connectivity() {
    echo "🌐 Conectividade de rede:"

    # Ping para DNS público
    if ping -c 1 ******* >/dev/null 2>&1; then
        echo "   ✅ Internet: OK"
    else
        echo "   ❌ Internet: FALHA"
    fi

    # Verificar resolução DNS
    if nslookup google.com >/dev/null 2>&1; then
        echo "   ✅ DNS: OK"
    else
        echo "   ❌ DNS: FALHA"
    fi

    # Verificar portas abertas
    local port_1194=$(netstat -tulpn | grep ":1194.*udp" | wc -l)
    local port_8080=$(netstat -tulpn | grep ":8080.*tcp" | wc -l)

    echo "   Porta 1194/UDP: $([ $port_1194 -gt 0 ] && echo "✅ ABERTA" || echo "❌ FECHADA")"
    echo "   Porta 8080/TCP: $([ $port_8080 -gt 0 ] && echo "✅ ABERTA" || echo "❌ FECHADA")"
}

# Função principal de monitoramento
main_monitor() {
    echo "🔍 RELATÓRIO DE MONITORAMENTO VPN"
    echo "=================================="
    echo "Data: $(date)"
    echo "Servidor: $(hostname)"
    echo ""

    # Verificações principais
    check_openvpn_process
    echo ""

    check_tun_interface
    echo ""

    check_connected_clients
    echo ""

    check_error_logs
    echo ""

    check_system_resources
    echo ""

    check_network_connectivity
    echo ""

    # Verificar API Docker se disponível
    if command -v docker >/dev/null && docker ps | grep -q "API-VPN"; then
        echo "🐳 API Docker:"
        if curl -s --connect-timeout 5 "http://localhost:8080/api/health" >/dev/null; then
            echo "   ✅ API: RESPONDENDO"
        else
            echo "   ❌ API: NÃO RESPONDE"
        fi
        echo ""
    fi

    echo "📋 Relatório salvo em: $REPORT_FILE"
}

# Salvar relatório em arquivo
main_monitor | tee "$REPORT_FILE"
```

### A.3 Template de Arquivo .ovpn
```
# TEMPLATE CRÍTICO - NÃO ALTERAR ESTRUTURA
# Cliente OpenVPN - [NOME_CLIENTE]
# Plataforma: [PLATAFORMA]
# Criado em: [DATA]

client
dev tun
proto udp
remote [SERVIDOR] 1194
resolv-retry infinite
nobind
persist-key
persist-tun
verb 3

# NUNCA incluir comp-lzo (deprecated)
# comp-lzo

<ca>
[CONTEÚDO_CA.CRT]
</ca>

<cert>
[CONTEÚDO_CLIENT.CRT]
</cert>

<key>
[CONTEÚDO_CLIENT.KEY]
</key>

<tls-auth>
[CONTEÚDO_TA.KEY]
</tls-auth>
key-direction 1
```

**🚨 ERROS FATAIS NO TEMPLATE**:
- Incluir comp-lzo (causa warnings)
- Esquecer key-direction 1
- Certificados mal formatados
- Endereço do servidor incorreto

---

## 🔧 APÊNDICE B: TROUBLESHOOTING AVANÇADO

### B.1 Problemas de Certificados
```bash
# ERRO: "Cannot load certificate"
# CAUSA: Permissões incorretas ou arquivo corrompido
# SOLUÇÃO:
chmod 644 /root/VPN/OpenVPN/easy-rsa/pki/ca.crt
chmod 644 /root/VPN/OpenVPN/easy-rsa/pki/issued/*.crt
chmod 600 /root/VPN/OpenVPN/easy-rsa/pki/private/*.key

# VERIFICAR integridade:
openssl x509 -in /root/VPN/OpenVPN/easy-rsa/pki/ca.crt -text -noout
```

### B.2 Problemas de Rede
```bash
# ERRO: "Address already in use"
# CAUSA: Outro processo usando porta 1194
# SOLUÇÃO:
netstat -tulpn | grep 1194
kill -9 [PID_DO_PROCESSO]

# ERRO: Clientes conectam mas não acessam internet
# CAUSA: IP forwarding desabilitado ou NAT incorreto
# SOLUÇÃO:
echo 1 > /proc/sys/net/ipv4/ip_forward
iptables -t nat -A POSTROUTING -s *********/24 -o eth0 -j MASQUERADE
```

### B.3 Problemas do Docker
```bash
# ERRO: Container não inicia
# DIAGNÓSTICO:
docker-compose logs api-vpn

# SOLUÇÕES COMUNS:
# 1. Porta 8080 ocupada
netstat -tulpn | grep 8080

# 2. Volume não montado
docker inspect API-VPN | grep Mounts

# 3. Reconstruir container
docker-compose down
docker-compose up -d --build
```

### B.4 Logs de Diagnóstico
```bash
# Coletar informações completas para diagnóstico:
echo "=== DIAGNÓSTICO COMPLETO VPN ===" > /tmp/vpn-debug.log
echo "Data: $(date)" >> /tmp/vpn-debug.log
echo "" >> /tmp/vpn-debug.log

echo "--- Processos OpenVPN ---" >> /tmp/vpn-debug.log
ps aux | grep openvpn >> /tmp/vpn-debug.log
echo "" >> /tmp/vpn-debug.log

echo "--- Interfaces de Rede ---" >> /tmp/vpn-debug.log
ip addr show >> /tmp/vpn-debug.log
echo "" >> /tmp/vpn-debug.log

echo "--- Portas Abertas ---" >> /tmp/vpn-debug.log
netstat -tulpn | grep -E "(1194|8080)" >> /tmp/vpn-debug.log
echo "" >> /tmp/vpn-debug.log

echo "--- Logs OpenVPN (últimas 20 linhas) ---" >> /tmp/vpn-debug.log
tail -20 /root/VPN/OpenVPN/logs/openvpn.log >> /tmp/vpn-debug.log
echo "" >> /tmp/vpn-debug.log

echo "--- Configuração OpenVPN ---" >> /tmp/vpn-debug.log
cat /etc/openvpn/server.conf >> /tmp/vpn-debug.log
echo "" >> /tmp/vpn-debug.log

echo "--- Status Docker ---" >> /tmp/vpn-debug.log
docker ps | grep API-VPN >> /tmp/vpn-debug.log
echo "" >> /tmp/vpn-debug.log

echo "Diagnóstico salvo em: /tmp/vpn-debug.log"
```

---

## 📚 APÊNDICE C: REFERÊNCIAS E RECURSOS

### C.1 Comandos de Verificação Rápida
```bash
# Status geral do sistema VPN
alias vpn-status='ps aux | grep openvpn && ip addr show tun0 && netstat -tulpn | grep 1194'

# Logs em tempo real
alias vpn-logs='tail -f /root/VPN/OpenVPN/logs/openvpn.log'

# Clientes conectados
alias vpn-clients='grep "CLIENT_LIST" /root/VPN/OpenVPN/logs/openvpn-status.log'

# Reiniciar VPN
alias vpn-restart='pkill openvpn && sleep 2 && openvpn --daemon --config /etc/openvpn/server.conf'
```

### C.2 Estrutura Final de Arquivos
```
/root/VPN/OpenVPN/
├── README-IMPLEMENTACAO.md      # Documentação da implementação
├── documentacao_VPN.md          # Esta documentação
├── api/                         # API Docker
│   ├── Dockerfile
│   ├── docker-compose.yml
│   ├── package.json
│   └── server.js
├── clients/                     # Arquivos .ovpn gerados
│   └── [cliente].ovpn
├── configs/                     # Configurações do servidor
│   ├── server.conf              # Configuração principal
│   └── server-simple.conf       # Configuração simplificada
├── easy-rsa/                    # Certificados e PKI
│   ├── easyrsa                  # Script principal
│   ├── vars                     # Configurações
│   └── pki/                     # Certificados gerados
│       ├── ca.crt
│       ├── dh.pem
│       ├── ta.key
│       ├── issued/              # Certificados de clientes
│       └── private/             # Chaves privadas
├── logs/                        # Logs do sistema
│   ├── clients-registry.log     # Registro de clientes
│   ├── openvpn.log             # Logs do OpenVPN
│   ├── openvpn-status.log      # Status em tempo real
│   └── vpn-report-*.txt        # Relatórios de monitoramento
└── scripts/                    # Scripts de automação
    ├── create-client-interactive.sh  # Criar clientes (admin)
    ├── monitor-vpn.sh               # Monitoramento
    ├── raspberry-setup.sh           # Setup Raspberry Pi
    ├── start-vpn-automation.sh      # Inicialização completa
    └── windows-setup.ps1            # Setup Windows
```

### C.3 Portas e Protocolos
- **1194/UDP**: OpenVPN Server (padrão)
- **8080/TCP**: API de automação
- **443/TCP**: HTTPS (se configurar web interface)
- **22/TCP**: SSH (administração)

### C.4 Rede VPN
- **Servidor**: *********
- **Clientes**: ********* - *********54
- **Máscara**: ************* (/24)
- **DNS**: *******, *******

---

**Esta documentação foi criada baseada em implementação real e problemas encontrados. Seguir exatamente estas instruções evitará 95% dos erros comuns.**

**🎯 IMPORTANTE**: Esta documentação é resultado de uma implementação real com todos os erros, tentativas e soluções documentados. Cada "⚠️ PROBLEMA COMUM" e "🚨 ERRO CRÍTICO" foi encontrado durante a implementação e tem solução testada.
