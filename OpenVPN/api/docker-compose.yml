version: '3.8'

services:
  api-vpn:
    build: .
    container_name: API-VPN
    ports:
      - "8080:8080"
    volumes:
      - /root/VPN/OpenVPN:/vpn-data
    environment:
      - NODE_ENV=production
      - PORT=8080
    restart: unless-stopped
    networks:
      - vpn-network
    labels:
      - "traefik.enable=false"  # Não expor via Traefik por enquanto
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  vpn-network:
    driver: bridge
