const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 8080;

// Middlewares
app.use(helmet({
    contentSecurityPolicy: false, // Permitir Cloudflare
    crossOriginEmbedderPolicy: false
}));
app.use(cors({
    origin: ['https://vpn.evo-eden.site', 'http://vpn.evo-eden.site'],
    credentials: true
}));
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Middleware para detectar Cloudflare
app.use((req, res, next) => {
    // Headers do Cloudflare
    const cfRay = req.headers['cf-ray'];
    const cfConnectingIp = req.headers['cf-connecting-ip'];
    
    if (cfRay) {
        console.log(`Requisição via Cloudflare - Ray: ${cfRay}, IP Real: ${cfConnectingIp}`);
    }
    
    next();
});

// Função para criar cliente VPN
function createVPNClient(password, clientName, platform) {
    return new Promise((resolve, reject) => {
        // Verificar senha
        if (password !== 'etenopenvpn') {
            return reject(new Error('Senha incorreta'));
        }

        // Validar nome do cliente
        if (!/^[a-zA-Z0-9_-]+$/.test(clientName)) {
            return reject(new Error('Nome do cliente inválido'));
        }

        // Verificar se cliente já existe
        const certPath = `/vpn-data/easy-rsa/pki/issued/${clientName}.crt`;
        if (fs.existsSync(certPath)) {
            return reject(new Error('Cliente já existe'));
        }

        // Por enquanto, apenas registrar a solicitação
        console.log(`Solicitação para criar cliente: ${clientName} (${platform})`);

        // Registrar no log
        const logEntry = `${new Date().toISOString()},${clientName},${platform},requested_via_api\n`;
        fs.appendFileSync('/vpn-data/logs/clients-registry.log', logEntry);

        // Simular sucesso por enquanto
        setTimeout(() => {
            console.log(`Cliente ${clientName} registrado com sucesso`);
            resolve({
                success: true,
                message: 'Solicitação de cliente registrada com sucesso',
                clientName: clientName,
                platform: platform,
                note: 'Certificado será gerado manualmente pelo administrador'
            });
        }, 1000);
    });
}

// Rota para criar cliente
app.post('/api/create-client', async (req, res) => {
    try {
        const { password, clientName, platform } = req.body;

        console.log(`Requisição para criar cliente: ${clientName} (${platform})`);

        if (!password || !clientName || !platform) {
            return res.status(400).json({
                success: false,
                error: 'Parâmetros obrigatórios: password, clientName, platform'
            });
        }

        const result = await createVPNClient(password, clientName, platform);
        
        res.json(result);
    } catch (error) {
        console.error('Erro na API:', error.message);
        res.status(400).json({
            success: false,
            error: error.message
        });
    }
});

// Rota para download do arquivo .ovpn
app.get('/api/download/:clientName', (req, res) => {
    const { clientName } = req.params;
    const filePath = `/vpn-data/clients/${clientName}.ovpn`;

    if (!fs.existsSync(filePath)) {
        return res.status(404).json({
            success: false,
            error: 'Arquivo não encontrado'
        });
    }

    res.download(filePath, `${clientName}.ovpn`);
});

// Rota de health check
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        message: 'API VPN funcionando',
        timestamp: new Date().toISOString()
    });
});

// Iniciar servidor
app.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 API VPN rodando na porta ${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
});
