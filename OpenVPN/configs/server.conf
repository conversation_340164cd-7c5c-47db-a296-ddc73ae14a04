# Configuração do Servidor OpenVPN - Fase 1
# Rede VPN: *********/24 (sem conflitos)
# Domínio: vpn.evo-eden.site

# Configurações básicas
port 1194
proto udp
dev tun

# Certificados e chaves
ca /root/VPN/OpenVPN/easy-rsa/pki/ca.crt
cert /root/VPN/OpenVPN/easy-rsa/pki/issued/server.crt
key /root/VPN/OpenVPN/easy-rsa/pki/private/server.key
dh /root/VPN/OpenVPN/easy-rsa/pki/dh.pem

# Configuração de rede VPN
topology subnet
server ********* *************
ifconfig-pool-persist /root/VPN/OpenVPN/logs/ipp.txt

# Configurações de roteamento
push "redirect-gateway def1 bypass-dhcp"
push "dhcp-option DNS *******"
push "dhcp-option DNS *******"

# Configurações de segurança
tls-auth /root/VPN/OpenVPN/easy-rsa/pki/ta.key 0
cipher AES-256-GCM
auth SHA256

# Configurações de cliente
client-to-client
duplicate-cn

# Configurações de conexão
keepalive 10 120
persist-key
persist-tun

# Configurações de usuário e grupo (desabilitado temporariamente)
#user nobody
#group nogroup

# Logs e status
status /root/VPN/OpenVPN/logs/openvpn-status.log
log-append /root/VPN/OpenVPN/logs/openvpn.log
verb 3
explicit-exit-notify 1

# Configurações de performance
fast-io
sndbuf 0
rcvbuf 0

# === CONFIGURAÇÕES FASE 5 - INTEGRAÇÃO ===
# Scripts de eventos para integração
client-connect /root/VPN/OpenVPN/scripts/client-connect.sh
client-disconnect /root/VPN/OpenVPN/scripts/client-disconnect.sh

# Status detalhado
status-version 3

# Logs verbosos
verb 4

# Script security (permitir execução de scripts)
script-security 3

# Management interface melhorada
management localhost 7505
management-log-cache 100
management-hold
