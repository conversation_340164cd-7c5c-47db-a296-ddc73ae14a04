#!/bin/bash

# Monitoramento de Volume de Dados VPN - Fase 2

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}📊 MONITORAMENTO DE VOLUME DE DADOS VPN${NC}"
echo -e "${CYAN}======================================${NC}"
echo ""

# Dados de conexão
DB_HOST="************"
DB_PORT="5432"
DB_USER="postgres"
DB_PASSWORD="ab3780bd73ee4e2804d566ce6fd96209"
DB_NAME="vpnetens"

echo -e "${BLUE}📋 Verificando volume de dados por tabela...${NC}"

PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
-- Verificar tamanho das tabelas em MB
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as tamanho_total,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as tamanho_dados,
    pg_total_relation_size(schemaname||'.'||tablename) / 1024 / 1024 as tamanho_mb
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Estatísticas de registros por tabela
SELECT 'vpn_sessions' as tabela, COUNT(*) as registros FROM vpn_sessions
UNION ALL
SELECT 'client_status_log' as tabela, COUNT(*) as registros FROM client_status_log
UNION ALL
SELECT 'daily_metrics' as tabela, COUNT(*) as registros FROM daily_metrics
UNION ALL
SELECT 'network_events' as tabela, COUNT(*) as registros FROM network_events
UNION ALL
SELECT 'system_events' as tabela, COUNT(*) as registros FROM system_events
UNION ALL
SELECT 'vpn_clients' as tabela, COUNT(*) as registros FROM vpn_clients
ORDER BY registros DESC;

-- Tamanho total do banco
SELECT
    pg_database.datname as banco,
    pg_size_pretty(pg_database_size(pg_database.datname)) as tamanho_total,
    pg_database_size(pg_database.datname) / 1024 / 1024 as tamanho_mb
FROM pg_database
WHERE datname = 'vpnetens';
"

echo ""
echo -e "${GREEN}✅ Verificação de volume concluída${NC}"
