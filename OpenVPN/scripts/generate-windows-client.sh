#!/bin/bash

# Script para gerar certificado completo para cliente Windows - Fase 5
# Uso: ./generate-windows-client.sh <nome-do-cliente>

set -e

CLIENT_NAME="$1"
SERVER_DOMAIN="vpn.evo-eden.site"

if [ -z "$CLIENT_NAME" ]; then
    echo "❌ Erro: Nome do cliente é obrigatório"
    echo "💡 Uso: $0 <nome-do-cliente>"
    echo "📝 Exemplo: $0 joao-notebook"
    exit 1
fi

# Validar nome do cliente
if [[ ! "$CLIENT_NAME" =~ ^[a-zA-Z0-9_-]+$ ]]; then
    echo "❌ Erro: Nome inválido! Use apenas letras, números, _ e -"
    exit 1
fi

echo "🔐 Gerando certificado completo para: $CLIENT_NAME"
echo "🌐 Servidor: $SERVER_DOMAIN"

# Diretórios
EASY_RSA_DIR="/root/VPN/OpenVPN/easy-rsa"
OUTPUT_DIR="/root/VPN/OpenVPN/clients"
CLIENT_FILE="$OUTPUT_DIR/${CLIENT_NAME}.ovpn"

# Criar diretório de saída
mkdir -p "$OUTPUT_DIR"

# Verificar se easy-rsa existe
if [ ! -d "$EASY_RSA_DIR" ]; then
    echo "❌ Erro: Diretório easy-rsa não encontrado: $EASY_RSA_DIR"
    exit 1
fi

cd "$EASY_RSA_DIR"

# Verificar se CA existe
if [ ! -f "pki/ca.crt" ]; then
    echo "❌ Erro: CA não encontrada. Execute primeiro a configuração inicial."
    exit 1
fi

# Verificar se cliente já existe
if [ -f "pki/issued/${CLIENT_NAME}.crt" ]; then
    echo "⚠️ Aviso: Certificado já existe para $CLIENT_NAME"
    read -p "🔄 Deseja recriar? (s/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Ss]$ ]]; then
        echo "ℹ️ Usando certificado existente..."
    else
        echo "🗑️ Removendo certificado existente..."
        rm -f "pki/issued/${CLIENT_NAME}.crt"
        rm -f "pki/private/${CLIENT_NAME}.key"
        rm -f "pki/reqs/${CLIENT_NAME}.req"
    fi
fi

# Gerar certificado se não existir
if [ ! -f "pki/issued/${CLIENT_NAME}.crt" ]; then
    echo "📝 Gerando requisição de certificado..."
    ./easyrsa gen-req "$CLIENT_NAME" nopass

    echo "✍️ Assinando certificado..."
    ./easyrsa sign-req client "$CLIENT_NAME"
fi

# Verificar se todos os arquivos necessários existem
REQUIRED_FILES=(
    "pki/ca.crt"
    "pki/issued/${CLIENT_NAME}.crt"
    "pki/private/${CLIENT_NAME}.key"
    "pki/ta.key"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ Erro: Arquivo necessário não encontrado: $file"
        exit 1
    fi
done

echo "📄 Criando arquivo .ovpn completo..."

# Criar arquivo .ovpn completo
cat > "$CLIENT_FILE" << EOF
# Configuração OpenVPN - Cliente: $CLIENT_NAME
# Gerado em: $(date '+%Y-%m-%d %H:%M:%S')
# Servidor: $SERVER_DOMAIN
# Versão: Fase 5 - Integração Completa

client
dev tun
proto udp
remote $SERVER_DOMAIN 1194
resolv-retry infinite
nobind
persist-key
persist-tun
cipher AES-256-GCM
auth SHA256
verb 3
key-direction 1

<ca>
$(cat pki/ca.crt)
</ca>

<cert>
$(cat pki/issued/${CLIENT_NAME}.crt)
</cert>

<key>
$(cat pki/private/${CLIENT_NAME}.key)
</key>

<tls-auth>
$(cat pki/ta.key)
</tls-auth>
EOF

echo "✅ Certificado completo gerado com sucesso!"
echo "📁 Arquivo: $CLIENT_FILE"
echo "📊 Tamanho: $(du -h "$CLIENT_FILE" | cut -f1)"

# Verificar integridade do arquivo
if grep -q "BEGIN CERTIFICATE" "$CLIENT_FILE" && grep -q "BEGIN PRIVATE KEY" "$CLIENT_FILE"; then
    echo "🔍 Verificação: Certificados presentes ✅"
else
    echo "❌ Erro: Certificados não encontrados no arquivo!"
    exit 1
fi

echo ""
echo "📋 PRÓXIMOS PASSOS:"
echo "   1. 📧 Envie o arquivo para o cliente: $CLIENT_FILE"
echo "   2. 🔒 Use canal seguro (email criptografado, etc.)"
echo "   3. 📱 Instrua o cliente a importar no OpenVPN"
echo ""
echo "💡 INSTRUÇÕES PARA O CLIENTE:"
echo "   1. Baixe e instale o OpenVPN Connect"
echo "   2. Importe o arquivo $CLIENT_NAME.ovpn"
echo "   3. Conecte à VPN"
echo "   4. Acesse https://$SERVER_DOMAIN"
echo ""
echo "🎉 Configuração completa!"
