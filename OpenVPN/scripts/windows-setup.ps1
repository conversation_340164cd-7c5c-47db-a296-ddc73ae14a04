# Script de Automação VPN para Windows - Fase 5
# Versão: 2.0 - Integração Completa
# Compatível com: Windows 10/11
# Atualizado: 2025-08-14

param(
    [string]$ClientName,
    [string]$ServerUrl = "vpn.evo-eden.site",
    [switch]$SkipInstall,
    [switch]$GenerateOnly
)

# Configurações da Fase 5
$VPN_SERVER = $ServerUrl
$API_URL = "https://$ServerUrl/api/v1"
$DOWNLOAD_PATH = "$env:USERPROFILE\Downloads\VPN"
$OPENVPN_PATH = "${env:ProgramFiles}\OpenVPN"
$CONFIG_PATH = "$env:USERPROFILE\OpenVPN\config"

# Cores para output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Success { Write-ColorOutput Green $args }
function Write-Error { Write-ColorOutput Red $args }
function Write-Warning { Write-ColorOutput Yellow $args }
function Write-Info { Write-ColorOutput Cyan $args }

Write-Info "🚀 Iniciando configuração automática da VPN - Fase 5..."
Write-Info "📋 Servidor: $VPN_SERVER"
Write-Info "🔧 Modo: $(if($GenerateOnly){'Apenas gerar certificado'}elseif($SkipInstall){'Pular instalação'}else{'Instalação completa'})"

# Verificar se está executando como administrador (apenas se não for GenerateOnly)
if (-not $GenerateOnly -and -NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "❌ Este script precisa ser executado como Administrador para instalação!"
    Write-Warning "💡 Clique com botão direito no PowerShell e selecione 'Executar como administrador'"
    Write-Warning "💡 Ou use o parâmetro -GenerateOnly para apenas gerar o certificado"
    Read-Host "Pressione Enter para sair"
    exit 1
}

# Solicitar nome do cliente se não fornecido
if (-not $ClientName) {
    Write-Info "📝 Digite o nome do cliente (sem espaços, apenas letras, números, _ e -):"
    $ClientName = Read-Host "Nome do cliente"

    if (-not $ClientName -or $ClientName -notmatch '^[a-zA-Z0-9_-]+$') {
        Write-Error "❌ Nome inválido! Use apenas letras, números, _ e -"
        Read-Host "Pressione Enter para sair"
        exit 1
    }
}

Write-Info "👤 Cliente: $ClientName"

# Criar diretórios necessários
if (-not (Test-Path $DOWNLOAD_PATH)) {
    New-Item -ItemType Directory -Path $DOWNLOAD_PATH -Force | Out-Null
    Write-Info "📁 Diretório de download criado: $DOWNLOAD_PATH"
}

if (-not (Test-Path $CONFIG_PATH)) {
    New-Item -ItemType Directory -Path $CONFIG_PATH -Force | Out-Null
    Write-Info "📁 Diretório de configuração criado: $CONFIG_PATH"
}

# Verificar/Instalar OpenVPN (apenas se não for SkipInstall ou GenerateOnly)
if (-not $SkipInstall -and -not $GenerateOnly) {
    if (-not (Test-Path $OPENVPN_PATH)) {
        Write-Warning "⚠️ OpenVPN não encontrado!"
        Write-Info "📥 Baixando OpenVPN 2.6.8..."

        $OpenVPNUrl = "https://swupdate.openvpn.org/community/releases/OpenVPN-2.6.8-I001-amd64.msi"
        $OpenVPNInstaller = "$DOWNLOAD_PATH\OpenVPN-installer.msi"

        try {
            Invoke-WebRequest -Uri $OpenVPNUrl -OutFile $OpenVPNInstaller -UseBasicParsing
            Write-Success "✅ OpenVPN baixado!"

            Write-Info "🔧 Instalando OpenVPN..."
            Start-Process msiexec.exe -Wait -ArgumentList "/i `"$OpenVPNInstaller`" /quiet /norestart"

            # Aguardar instalação
            Start-Sleep -Seconds 10

            if (Test-Path $OPENVPN_PATH) {
                Write-Success "✅ OpenVPN instalado com sucesso!"
            } else {
                Write-Error "❌ Falha na instalação do OpenVPN"
                Write-Warning "💡 Tente instalar manualmente: $OpenVPNInstaller"
                Read-Host "Pressione Enter para continuar"
            }
        } catch {
            Write-Error "❌ Erro ao baixar/instalar OpenVPN: $_"
            Write-Warning "💡 Baixe manualmente de: https://openvpn.net/community-downloads/"
            Read-Host "Pressione Enter para continuar"
        }
    } else {
        Write-Success "✅ OpenVPN já está instalado!"
    }
} elseif ($SkipInstall) {
    Write-Info "⏭️ Pulando instalação do OpenVPN (parâmetro -SkipInstall)"
} else {
    Write-Info "⏭️ Modo apenas geração de certificado"
}

# Testar conectividade com a API
Write-Info "🔍 Testando conectividade com o servidor..."
try {
    # Tentar conectar via HTTPS primeiro
    $healthCheck = Invoke-RestMethod -Uri "$API_URL/health" -Method GET -TimeoutSec 10 -UseBasicParsing
    if ($healthCheck.status -eq "healthy") {
        Write-Success "✅ Servidor VPN respondendo via HTTPS!"
        Write-Info "📊 Versão do servidor: $($healthCheck.version)"
    } else {
        throw "Servidor não está funcionando corretamente"
    }
} catch {
    Write-Warning "⚠️ Não foi possível conectar ao servidor VPN via HTTPS!"
    Write-Warning "   Servidor: $VPN_SERVER"
    Write-Warning "   Isso é esperado se você ainda não estiver conectado à VPN"
    Write-Info "🔄 Continuando com a geração do certificado..."
}

# Gerar certificado automaticamente
Write-Info "🔐 Gerando certificado para cliente: $ClientName"

# Criar certificado usando easy-rsa (simulação local)
$ovpnFile = "$DOWNLOAD_PATH\$ClientName.ovpn"

# Template do arquivo .ovpn para a Fase 5
$ovpnTemplate = @"
# Configuração OpenVPN - Cliente: $ClientName
# Gerado em: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
# Servidor: $VPN_SERVER

client
dev tun
proto udp
remote $VPN_SERVER 1194
resolv-retry infinite
nobind
persist-key
persist-tun
cipher AES-256-GCM
auth SHA256
verb 3
key-direction 1

# IMPORTANTE: Este arquivo precisa ser completado com os certificados
# Solicite ao administrador para gerar os certificados completos

# Estrutura necessária:
# <ca>
# [Certificado CA aqui]
# </ca>
#
# <cert>
# [Certificado do cliente aqui]
# </cert>
#
# <key>
# [Chave privada do cliente aqui]
# </key>
#
# <tls-auth>
# [Chave TLS-Auth aqui]
# </tls-auth>

"@

try {
    $ovpnTemplate | Out-File -FilePath $ovpnFile -Encoding UTF8
    Write-Success "✅ Arquivo de configuração criado: $ovpnFile"
} catch {
    Write-Error "❌ Erro ao criar arquivo de configuração: $_"
    Read-Host "Pressione Enter para sair"
    exit 1
}

# Copiar para diretório do OpenVPN se instalado
if ((Test-Path $OPENVPN_PATH) -and (Test-Path $CONFIG_PATH)) {
    try {
        Copy-Item $ovpnFile -Destination $CONFIG_PATH -Force
        Write-Success "✅ Configuração copiada para: $CONFIG_PATH"
    } catch {
        Write-Warning "⚠️ Não foi possível copiar para o diretório do OpenVPN"
    }
}

Write-Success "🎉 Configuração concluída!"
Write-Info ""
Write-Info "📋 PRÓXIMOS PASSOS OBRIGATÓRIOS:"
Write-Info "   1. 🔐 SOLICITE ao administrador para gerar os certificados completos"
Write-Info "   2. 📧 Envie este arquivo para o administrador: $ovpnFile"
Write-Info "   3. 📥 Aguarde receber o arquivo .ovpn completo com certificados"
Write-Info "   4. 🔄 Substitua o arquivo atual pelo arquivo completo"
Write-Info ""
Write-Info "📱 COMO CONECTAR (após receber certificados):"
if (Test-Path $OPENVPN_PATH) {
    Write-Info "   - Abra o OpenVPN GUI (ícone na bandeja do sistema)"
    Write-Info "   - Clique com botão direito > Import file"
    Write-Info "   - Selecione seu arquivo .ovpn completo"
    Write-Info "   - Clique em Connect"
} else {
    Write-Info "   - Instale o OpenVPN Connect"
    Write-Info "   - Importe o arquivo .ovpn completo"
    Write-Info "   - Conecte à VPN"
}
Write-Info ""
Write-Info "🌐 APÓS CONECTAR:"
Write-Info "   - Acesse: https://$VPN_SERVER"
Write-Info "   - Faça login no dashboard VPN"
Write-Info "   - Monitore suas conexões e estatísticas"
Write-Info ""
Write-Info "📁 Arquivos criados:"
Write-Info "   - Configuração: $ovpnFile"
if (Test-Path "$CONFIG_PATH\$ClientName.ovpn") {
    Write-Info "   - Cópia OpenVPN: $CONFIG_PATH\$ClientName.ovpn"
}

Write-Warning "⚠️ IMPORTANTE: O arquivo atual é apenas um template!"
Write-Warning "   Você DEVE solicitar os certificados ao administrador!"

Read-Host "Pressione Enter para finalizar"
