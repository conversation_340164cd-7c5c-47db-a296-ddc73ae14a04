#!/bin/bash

# Criação das Tabelas do Banco de Dados - Fase 2

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}📊 CRIAÇÃO DAS TABELAS - FASE 2${NC}"
echo -e "${CYAN}===============================${NC}"
echo ""

# Dados de conexão
DB_HOST="************"
DB_PORT="5432"
DB_USER="postgres"
DB_PASSWORD="ab3780bd73ee4e2804d566ce6fd96209"
DB_NAME="vpnetens"

# Verificar se Fase 1 está instalada
if [ ! -f "/root/VPN/OpenVPN/configs/server.conf" ]; then
    echo -e "${RED}❌ Fase 1 não encontrada!${NC}"
    echo -e "${YELLOW}💡 Execute primeiro a implementação da Fase 1${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Fase 1 detectada${NC}"

# Testar conexão
echo -e "${BLUE}🧪 Testando conexão...${NC}"
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Erro na conexão com o banco!${NC}"
    echo -e "${YELLOW}💡 Execute primeiro: /root/VPN/OpenVPN/scripts/verify-postgresql.sh${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Conexão com banco estabelecida${NC}"

# Criar tabelas
echo -e "${BLUE}📊 Criando estrutura de tabelas...${NC}"

PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << 'SQLEOF'

-- ============================================================================
-- CRIAÇÃO DAS TABELAS SIMPLIFICADAS PARA MONITORAMENTO VPN - FASE 2
-- ============================================================================

-- Habilitar extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Configurar timezone para Brasil (UTC-3)
SET timezone = 'America/Sao_Paulo';

-- ============================================================================
-- 1. TABELA DE CLIENTES VPN (SIMPLIFICADA)
-- ============================================================================
CREATE TABLE IF NOT EXISTS vpn_clients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_name VARCHAR(100) NOT NULL UNIQUE,
    common_name VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255),
    department VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'revoked')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() AT TIME ZONE 'America/Sao_Paulo'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() AT TIME ZONE 'America/Sao_Paulo'),
    last_connection TIMESTAMP WITH TIME ZONE,
    certificate_expires_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,

    -- Constraints essenciais
    CONSTRAINT vpn_clients_name_check CHECK (length(client_name) >= 3),
    CONSTRAINT vpn_clients_cn_check CHECK (length(common_name) >= 3)
);

-- ============================================================================
-- 2. TABELA DE SESSÕES VPN (SIMPLIFICADA)
-- ============================================================================
CREATE TABLE IF NOT EXISTS vpn_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID REFERENCES vpn_clients(id) ON DELETE CASCADE,
    client_name VARCHAR(100) NOT NULL,
    real_address INET NOT NULL,
    virtual_address INET,

    -- TIMESTAMPS ESSENCIAIS (TIMEZONE BRASIL -3)
    connected_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() AT TIME ZONE 'America/Sao_Paulo'),
    disconnected_at TIMESTAMP WITH TIME ZONE,

    -- DURAÇÃO E TRÁFEGO
    duration_seconds INTEGER,
    bytes_received BIGINT DEFAULT 0,
    bytes_sent BIGINT DEFAULT 0,

    -- STATUS E METADADOS
    disconnect_reason VARCHAR(50) DEFAULT 'unknown',
    server_port INTEGER,
    protocol VARCHAR(10) DEFAULT 'udp',
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'disconnected', 'timeout', 'error')),

    -- CAMPOS ESSENCIAIS PARA ANÁLISE
    session_date DATE GENERATED ALWAYS AS (connected_at::date) STORED,
    is_active BOOLEAN GENERATED ALWAYS AS (disconnected_at IS NULL) STORED,

    -- AUDITORIA
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() AT TIME ZONE 'America/Sao_Paulo'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() AT TIME ZONE 'America/Sao_Paulo'),

    -- CONSTRAINTS
    CONSTRAINT vpn_sessions_duration_check CHECK (duration_seconds >= 0),
    CONSTRAINT vpn_sessions_bytes_check CHECK (bytes_received >= 0 AND bytes_sent >= 0),
    CONSTRAINT vpn_sessions_timestamps_check CHECK (disconnected_at IS NULL OR disconnected_at >= connected_at)
);

\echo '✅ Tabelas principais criadas com sucesso!'

SQLEOF

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Tabelas principais criadas${NC}"
else
    echo -e "${RED}❌ Erro ao criar tabelas principais${NC}"
    exit 1
fi

# Continuar criando as demais tabelas
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << 'SQLEOF2'

-- ============================================================================
-- 3. TABELA DE EVENTOS DE REDE (SIMPLIFICADA)
-- ============================================================================
CREATE TABLE IF NOT EXISTS network_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES vpn_sessions(id) ON DELETE CASCADE,
    source_client VARCHAR(100) NOT NULL,
    destination_client VARCHAR(100) NOT NULL,
    source_ip INET NOT NULL,
    destination_ip INET NOT NULL,
    source_port INTEGER,
    destination_port INTEGER,
    protocol VARCHAR(10) NOT NULL,
    event_type VARCHAR(50) NOT NULL, -- 'ssh', 'vnc', 'ping', 'file_transfer', 'other'
    bytes_transferred BIGINT DEFAULT 0,
    duration_seconds INTEGER,
    detected_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() AT TIME ZONE 'America/Sao_Paulo'),
    event_date DATE GENERATED ALWAYS AS (detected_at::date) STORED,
    success BOOLEAN DEFAULT true,

    -- Constraints essenciais
    CONSTRAINT network_events_port_check CHECK (source_port > 0 AND destination_port > 0),
    CONSTRAINT network_events_bytes_check CHECK (bytes_transferred >= 0)
);

-- ============================================================================
-- 4. TABELA DE STATUS DE CLIENTES (SIMPLIFICADA)
-- ============================================================================
CREATE TABLE IF NOT EXISTS client_status_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_name VARCHAR(100) NOT NULL,
    client_id UUID REFERENCES vpn_clients(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL CHECK (status IN ('online', 'offline', 'connecting', 'disconnecting')),
    status_changed_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() AT TIME ZONE 'America/Sao_Paulo'),
    session_id UUID REFERENCES vpn_sessions(id) ON DELETE SET NULL,
    real_address INET,
    virtual_address INET,

    -- Constraint para performance
    CONSTRAINT client_status_log_name_check CHECK (length(client_name) >= 3)
);

-- ============================================================================
-- 5. TABELA DE EVENTOS DO SISTEMA (UNIFICADA)
-- ============================================================================
CREATE TABLE IF NOT EXISTS system_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL, -- 'alert', 'reboot', 'config_change', 'maintenance', 'error'
    event_subtype VARCHAR(50), -- 'connection_failure', 'high_traffic', 'server_restart', etc.
    severity VARCHAR(20) DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    event_data JSONB, -- Dados específicos do evento em formato JSON
    occurred_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() AT TIME ZONE 'America/Sao_Paulo'),
    resolved_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'resolved', 'dismissed')),

    -- Constraint essencial
    CONSTRAINT system_events_title_check CHECK (length(title) >= 5)
);

\echo '✅ Tabelas de eventos criadas com sucesso!'

SQLEOF2

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Tabelas de eventos criadas${NC}"
else
    echo -e "${RED}❌ Erro ao criar tabelas de eventos${NC}"
    exit 1
fi

# Criar tabela de métricas e índices
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << 'SQLEOF3'

-- ============================================================================
-- 6. TABELA DE MÉTRICAS DIÁRIAS (SIMPLIFICADA - PODE SER VIEW)
-- ============================================================================
CREATE TABLE IF NOT EXISTS daily_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_date DATE NOT NULL,
    client_name VARCHAR(100),

    -- MÉTRICAS ESSENCIAIS
    total_connections INTEGER DEFAULT 0,
    total_online_seconds INTEGER DEFAULT 0,
    total_bytes_sent BIGINT DEFAULT 0,
    total_bytes_received BIGINT DEFAULT 0,
    total_network_events INTEGER DEFAULT 0,

    -- EVENTOS ESPECÍFICOS
    ssh_connections INTEGER DEFAULT 0,
    vnc_connections INTEGER DEFAULT 0,

    -- AUDITORIA
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() AT TIME ZONE 'America/Sao_Paulo'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() AT TIME ZONE 'America/Sao_Paulo'),

    -- Constraint para evitar duplicatas
    UNIQUE(metric_date, client_name)
);

-- ============================================================================
-- ÍNDICES ESTRATÉGICOS PARA PERFORMANCE
-- ============================================================================

-- Índices para vpn_sessions
CREATE INDEX IF NOT EXISTS idx_vpn_sessions_client_date
ON vpn_sessions(client_name, session_date DESC);

CREATE INDEX IF NOT EXISTS idx_vpn_sessions_active
ON vpn_sessions(is_active, connected_at DESC);

-- Índices para client_status_log
CREATE INDEX IF NOT EXISTS idx_client_status_log_client_date
ON client_status_log(client_name, status_changed_at DESC);

-- Índices para network_events
CREATE INDEX IF NOT EXISTS idx_network_events_date_type
ON network_events(event_date, event_type);

-- Índices para system_events
CREATE INDEX IF NOT EXISTS idx_system_events_type_date
ON system_events(event_type, occurred_at DESC);

\echo '✅ Métricas e índices criados com sucesso!'

SQLEOF3

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Métricas e índices criados${NC}"
else
    echo -e "${RED}❌ Erro ao criar métricas e índices${NC}"
    exit 1
fi

# Criar funções e triggers
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << 'SQLEOF4'

-- ============================================================================
-- FUNÇÕES SQL PARA CÁLCULOS DE DISPONIBILIDADE E AUTOMAÇÃO
-- ============================================================================

-- Função para calcular disponibilidade de um cliente em um período
CREATE OR REPLACE FUNCTION calculate_client_availability(
    p_client_name VARCHAR(100),
    p_start_date DATE,
    p_end_date DATE
) RETURNS DECIMAL(5,2) AS $$
DECLARE
    total_seconds INTEGER;
    online_seconds INTEGER;
    availability DECIMAL(5,2);
BEGIN
    -- Calcular total de segundos no período
    total_seconds := EXTRACT(EPOCH FROM (p_end_date + INTERVAL '1 day' - p_start_date));

    -- Calcular segundos online
    SELECT COALESCE(SUM(duration_seconds), 0) INTO online_seconds
    FROM vpn_sessions
    WHERE client_name = p_client_name
    AND session_date BETWEEN p_start_date AND p_end_date
    AND status = 'disconnected'; -- Apenas sessões finalizadas

    -- Calcular percentual
    IF total_seconds > 0 THEN
        availability := (online_seconds::DECIMAL / total_seconds) * 100;
    ELSE
        availability := 0;
    END IF;

    RETURN LEAST(availability, 100.00); -- Máximo 100%
END;
$$ LANGUAGE plpgsql;

-- Função simplificada para atualizar métricas diárias
CREATE OR REPLACE FUNCTION update_daily_metrics(p_date DATE, p_client_name VARCHAR(100))
RETURNS VOID AS $$
DECLARE
    v_total_online_seconds INTEGER;
    v_total_connections INTEGER;
    v_total_bytes_sent BIGINT;
    v_total_bytes_received BIGINT;
    v_ssh_connections INTEGER;
    v_vnc_connections INTEGER;
BEGIN
    -- Calcular métricas do dia
    SELECT
        COALESCE(SUM(duration_seconds), 0),
        COUNT(*),
        COALESCE(SUM(bytes_sent), 0),
        COALESCE(SUM(bytes_received), 0)
    INTO v_total_online_seconds, v_total_connections, v_total_bytes_sent, v_total_bytes_received
    FROM vpn_sessions
    WHERE client_name = p_client_name
    AND session_date = p_date;

    -- Calcular eventos de rede
    SELECT
        COUNT(*) FILTER (WHERE event_type = 'ssh'),
        COUNT(*) FILTER (WHERE event_type = 'vnc')
    INTO v_ssh_connections, v_vnc_connections
    FROM network_events ne
    JOIN vpn_sessions vs ON ne.session_id = vs.id
    WHERE vs.client_name = p_client_name
    AND ne.event_date = p_date;

    -- Inserir ou atualizar métricas
    INSERT INTO daily_metrics (
        metric_date, client_name, total_connections, total_online_seconds,
        total_bytes_sent, total_bytes_received, ssh_connections, vnc_connections
    ) VALUES (
        p_date, p_client_name, v_total_connections, v_total_online_seconds,
        v_total_bytes_sent, v_total_bytes_received, v_ssh_connections, v_vnc_connections
    )
    ON CONFLICT (metric_date, client_name)
    DO UPDATE SET
        total_connections = EXCLUDED.total_connections,
        total_online_seconds = EXCLUDED.total_online_seconds,
        total_bytes_sent = EXCLUDED.total_bytes_sent,
        total_bytes_received = EXCLUDED.total_bytes_received,
        ssh_connections = EXCLUDED.ssh_connections,
        vnc_connections = EXCLUDED.vnc_connections,
        updated_at = (NOW() AT TIME ZONE 'America/Sao_Paulo');
END;
$$ LANGUAGE plpgsql;

\echo '✅ Funções criadas com sucesso!'

SQLEOF4

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Funções criadas${NC}"
else
    echo -e "${RED}❌ Erro ao criar funções${NC}"
    exit 1
fi

# Criar triggers finais
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << 'SQLEOF5'

-- ============================================================================
-- TRIGGERS ESSENCIAIS
-- ============================================================================

-- Trigger para atualizar timestamp de updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = (NOW() AT TIME ZONE 'America/Sao_Paulo');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Aplicar trigger apenas nas tabelas principais
CREATE TRIGGER update_vpn_sessions_updated_at
    BEFORE UPDATE ON vpn_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_daily_metrics_updated_at
    BEFORE UPDATE ON daily_metrics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger simplificado para log de status
CREATE OR REPLACE FUNCTION log_client_status_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Registrar conexão
    IF TG_OP = 'INSERT' THEN
        INSERT INTO client_status_log (
            client_name, client_id, status, session_id, real_address, virtual_address
        ) VALUES (
            NEW.client_name, NEW.client_id, 'online', NEW.id, NEW.real_address, NEW.virtual_address
        );
    END IF;

    -- Registrar desconexão
    IF TG_OP = 'UPDATE' AND OLD.disconnected_at IS NULL AND NEW.disconnected_at IS NOT NULL THEN
        INSERT INTO client_status_log (
            client_name, client_id, status, session_id
        ) VALUES (
            NEW.client_name, NEW.client_id, 'offline', NEW.id
        );
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER vpn_sessions_status_log
    AFTER INSERT OR UPDATE ON vpn_sessions
    FOR EACH ROW EXECUTE FUNCTION log_client_status_change();

\echo '✅ Triggers criados com sucesso!'

SQLEOF5

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Triggers criados${NC}"
else
    echo -e "${RED}❌ Erro ao criar triggers${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 ESTRUTURA DO BANCO CRIADA COM SUCESSO!${NC}"
echo -e "${GREEN}=======================================${NC}"
echo ""
echo -e "${YELLOW}📊 ESTRUTURA SIMPLIFICADA CRIADA NO BANCO 'vpnetens':${NC}"
echo -e "   1. vpn_clients - Cadastro de clientes (simplificado)"
echo -e "   2. vpn_sessions - Sessões de conexão (campos essenciais)"
echo -e "   3. network_events - Eventos de rede (simplificado)"
echo -e "   4. client_status_log - Status online/offline (simplificado)"
echo -e "   5. system_events - Eventos do sistema (unificado)"
echo -e "   6. daily_metrics - Métricas agregadas (pode ser view)"
echo ""
echo -e "${YELLOW}🔧 FUNÇÕES E AUTOMAÇÃO ESSENCIAIS:${NC}"
echo -e "   ✅ calculate_client_availability() - Cálculo de disponibilidade"
echo -e "   ✅ update_daily_metrics() - Atualização simplificada de métricas"
echo -e "   ✅ Triggers essenciais para timestamps e logs"
echo -e "   ✅ Índices estratégicos para performance"
echo -e "   ⚠️ Limpeza de dados: Manual (conforme solicitado)"
echo ""
echo -e "${YELLOW}📋 DADOS DE CONEXÃO:${NC}"
echo -e "   🏠 Host: $DB_HOST"
echo -e "   🔌 Porta: $DB_PORT"
echo -e "   🗄️ Banco: $DB_NAME"
echo -e "   👤 Usuário: $DB_USER"
echo ""
echo -e "${CYAN}✨ Banco de dados pronto para uso!${NC}"
