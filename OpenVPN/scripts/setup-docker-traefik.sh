#!/bin/bash

# Script de Configuração Docker + Traefik para vpn.evo-eden.site

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}🐳 CONFIGURAÇÃO DOCKER + TRAEFIK - VPN.EVO-EDEN.SITE${NC}"
echo -e "${CYAN}===================================================${NC}"
echo ""

# 1. Verificar se Docker está rodando
echo -e "${BLUE}🐳 Verificando Docker...${NC}"
if ! systemctl is-active --quiet docker; then
    echo -e "${RED}❌ Docker não está rodando!${NC}"
    echo -e "${YELLOW}💡 Inicie o Docker: systemctl start docker${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Docker está rodando${NC}"

# 2. Verificar se Traefik está rodando
echo -e "${BLUE}🔀 Verificando Traefik...${NC}"
if ! docker ps | grep -q traefik; then
    echo -e "${RED}❌ Container Traefik não encontrado!${NC}"
    echo -e "${YELLOW}💡 Verifique se o Traefik está configurado e rodando${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Traefik está rodando${NC}"

# 3. Verificar rede do Traefik
echo -e "${BLUE}🌐 Verificando rede do Traefik...${NC}"
if ! docker network ls | grep -q "redeinterna"; then
    echo -e "${RED}❌ Rede 'redeinterna' não encontrada!${NC}"
    echo -e "${YELLOW}💡 Criando rede redeinterna...${NC}"
    docker network create --driver overlay --attachable redeinterna >/dev/null 2>&1
fi
echo -e "${GREEN}✅ Rede redeinterna: OK${NC}"

# 4. Criar diretório do projeto
echo -e "${BLUE}📁 Criando estrutura do projeto...${NC}"
PROJECT_DIR="/root/VPN/FrontEnd"
mkdir -p $PROJECT_DIR
cd $PROJECT_DIR

# 5. Criar docker-compose.yml para o frontend
echo -e "${BLUE}🐳 Criando docker-compose.yml...${NC}"
cat > docker-compose.yml << 'DOCKEREOF'
version: '3.8'

services:
  vpn-frontend:
    build:
      context: ./vpn-dashboard
      dockerfile: Dockerfile
    container_name: vpn-frontend
    restart: unless-stopped
    networks:
      - redeinterna
    deploy:
      labels:
        # Habilitar Traefik
        - "traefik.enable=true"
        
        # Configuração HTTP
        - "traefik.http.routers.vpn-frontend.rule=Host(`vpn.evo-eden.site`)"
        - "traefik.http.routers.vpn-frontend.entrypoints=web"
        - "traefik.http.routers.vpn-frontend.middlewares=vpn-frontend-redirect"
        
        # Configuração HTTPS
        - "traefik.http.routers.vpn-frontend-secure.rule=Host(`vpn.evo-eden.site`)"
        - "traefik.http.routers.vpn-frontend-secure.entrypoints=websecure"
        - "traefik.http.routers.vpn-frontend-secure.tls=true"
        - "traefik.http.routers.vpn-frontend-secure.tls.certresolver=letsencryptresolver"
        
        # Middleware para redirecionamento HTTPS
        - "traefik.http.middlewares.vpn-frontend-redirect.redirectscheme.scheme=https"
        - "traefik.http.middlewares.vpn-frontend-redirect.redirectscheme.permanent=true"
        
        # Headers de segurança
        - "traefik.http.middlewares.vpn-frontend-headers.headers.frameDeny=true"
        - "traefik.http.middlewares.vpn-frontend-headers.headers.sslRedirect=true"
        - "traefik.http.middlewares.vpn-frontend-headers.headers.browserXssFilter=true"
        - "traefik.http.middlewares.vpn-frontend-headers.headers.contentTypeNosniff=true"
        - "traefik.http.middlewares.vpn-frontend-headers.headers.forceSTSHeader=true"
        - "traefik.http.middlewares.vpn-frontend-headers.headers.stsSeconds=31536000"
        - "traefik.http.middlewares.vpn-frontend-headers.headers.stsIncludeSubdomains=true"
        - "traefik.http.middlewares.vpn-frontend-headers.headers.stsPreload=true"
        
        # Aplicar headers de segurança
        - "traefik.http.routers.vpn-frontend-secure.middlewares=vpn-frontend-headers"
        
        # Configuração do serviço
        - "traefik.http.services.vpn-frontend.loadbalancer.server.port=80"
        
        # Healthcheck
        - "traefik.http.services.vpn-frontend.loadbalancer.healthcheck.path=/"
        - "traefik.http.services.vpn-frontend.loadbalancer.healthcheck.interval=30s"
        - "traefik.http.services.vpn-frontend.loadbalancer.healthcheck.timeout=5s"
    environment:
      - NODE_ENV=production
      - VITE_API_URL=https://vpn.evo-eden.site/api
      - VITE_WS_URL=wss://vpn.evo-eden.site/ws
    volumes:
      # Volume para assets (logo da empresa)
      - /root/VPN/assets:/usr/share/nginx/html/assets:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  redeinterna:
    external: true
DOCKEREOF

echo ""
echo -e "${GREEN}🎉 CONFIGURAÇÃO DOCKER + TRAEFIK CONCLUÍDA!${NC}"
echo -e "${GREEN}===========================================${NC}"
echo ""
echo -e "${YELLOW}🐳 DOCKER COMPOSE CRIADO:${NC}"
echo -e "   Arquivo: /root/VPN/FrontEnd/docker-compose.yml"
echo -e "   Dockerfile: /root/VPN/FrontEnd/vpn-dashboard/Dockerfile"
echo -e "   Nginx Config: /root/VPN/FrontEnd/vpn-dashboard/nginx.conf"
echo ""
echo -e "${YELLOW}🌐 ACESSO:${NC}"
echo -e "   URL: https://vpn.evo-eden.site"
echo -e "   Proxy: Traefik + Let's Encrypt"
echo -e "   SSL: Let's Encrypt via Traefik"
echo -e "   Acesso: Apenas usuários VPN"
echo ""
echo -e "${CYAN}⚠️ PRÓXIMOS PASSOS:${NC}"
echo -e "   1. Construir e iniciar: docker-compose up -d --build"
echo -e "   2. Verificar logs: docker-compose logs -f vpn-frontend"
echo -e "   3. Verificar Traefik: docker logs traefik"
