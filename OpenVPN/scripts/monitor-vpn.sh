#!/bin/bash

# Script de Monitoramento VPN
# Versão: 1.0
# Monitora OpenVPN, API Docker e gera relatórios

# Configurações
VPN_DIR="/root/VPN/OpenVPN"
LOG_DIR="$VPN_DIR/logs"
REPORT_FILE="$LOG_DIR/vpn-report-$(date +%Y%m%d-%H%M%S).txt"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Função para log com timestamp
log_with_time() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$REPORT_FILE"
}

# Função para verificar status de serviço
check_service() {
    local service_name="$1"
    local display_name="$2"
    
    if systemctl is-active --quiet "$service_name"; then
        echo -e "${GREEN}✅ $display_name: ATIVO${NC}"
        log_with_time "$display_name: ATIVO"
        return 0
    else
        echo -e "${RED}❌ $display_name: INATIVO${NC}"
        log_with_time "$display_name: INATIVO"
        return 1
    fi
}

# Função para verificar processo
check_process() {
    local process_name="$1"
    local display_name="$2"
    
    if pgrep "$process_name" >/dev/null; then
        local pid=$(pgrep "$process_name")
        echo -e "${GREEN}✅ $display_name: RODANDO (PID: $pid)${NC}"
        log_with_time "$display_name: RODANDO (PID: $pid)"
        return 0
    else
        echo -e "${RED}❌ $display_name: NÃO ENCONTRADO${NC}"
        log_with_time "$display_name: NÃO ENCONTRADO"
        return 1
    fi
}

# Função para verificar porta
check_port() {
    local port="$1"
    local protocol="$2"
    local service_name="$3"
    
    if netstat -tulpn | grep -q ":$port.*$protocol"; then
        echo -e "${GREEN}✅ $service_name: Porta $port/$protocol ABERTA${NC}"
        log_with_time "$service_name: Porta $port/$protocol ABERTA"
        return 0
    else
        echo -e "${RED}❌ $service_name: Porta $port/$protocol FECHADA${NC}"
        log_with_time "$service_name: Porta $port/$protocol FECHADA"
        return 1
    fi
}

# Função para verificar API
check_api() {
    if curl -s --connect-timeout 5 "http://localhost:8080/api/health" >/dev/null; then
        echo -e "${GREEN}✅ API VPN: RESPONDENDO${NC}"
        log_with_time "API VPN: RESPONDENDO"
        return 0
    else
        echo -e "${RED}❌ API VPN: NÃO RESPONDE${NC}"
        log_with_time "API VPN: NÃO RESPONDE"
        return 1
    fi
}

# Função para verificar interface de rede
check_interface() {
    local interface="$1"
    
    if ip addr show "$interface" >/dev/null 2>&1; then
        local ip=$(ip addr show "$interface" | grep 'inet ' | awk '{print $2}' | head -1)
        echo -e "${GREEN}✅ Interface $interface: ATIVA ($ip)${NC}"
        log_with_time "Interface $interface: ATIVA ($ip)"
        return 0
    else
        echo -e "${RED}❌ Interface $interface: INATIVA${NC}"
        log_with_time "Interface $interface: INATIVA"
        return 1
    fi
}

# Função para contar clientes conectados
count_connected_clients() {
    local status_file="$LOG_DIR/openvpn-status.log"
    
    if [ -f "$status_file" ]; then
        local count=$(grep "^CLIENT_LIST" "$status_file" | wc -l)
        echo -e "${CYAN}📊 Clientes conectados: $count${NC}"
        log_with_time "Clientes conectados: $count"
        
        if [ "$count" -gt 0 ]; then
            echo -e "${CYAN}👥 Lista de clientes:${NC}"
            grep "^CLIENT_LIST" "$status_file" | while read line; do
                local client_info=$(echo "$line" | cut -d',' -f2,3,4)
                echo -e "   ${YELLOW}$client_info${NC}"
                log_with_time "Cliente conectado: $client_info"
            done
        fi
    else
        echo -e "${YELLOW}⚠️ Arquivo de status não encontrado${NC}"
        log_with_time "Arquivo de status não encontrado"
    fi
}

# Função para verificar logs de erro
check_error_logs() {
    local openvpn_log="$LOG_DIR/openvpn.log"
    
    if [ -f "$openvpn_log" ]; then
        local errors=$(tail -100 "$openvpn_log" | grep -i "error\|failed\|warning" | wc -l)
        if [ "$errors" -gt 0 ]; then
            echo -e "${YELLOW}⚠️ Encontrados $errors erros/avisos nos logs recentes${NC}"
            log_with_time "Encontrados $errors erros/avisos nos logs recentes"
            
            echo -e "${CYAN}📋 Últimos erros:${NC}"
            tail -100 "$openvpn_log" | grep -i "error\|failed\|warning" | tail -5 | while read line; do
                echo -e "   ${RED}$line${NC}"
                log_with_time "Erro: $line"
            done
        else
            echo -e "${GREEN}✅ Nenhum erro recente nos logs${NC}"
            log_with_time "Nenhum erro recente nos logs"
        fi
    fi
}

# Função para verificar uso de recursos
check_resources() {
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    local mem_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    local disk_usage=$(df / | tail -1 | awk '{print $5}' | cut -d'%' -f1)
    
    echo -e "${CYAN}📊 Uso de recursos:${NC}"
    echo -e "   CPU: ${YELLOW}$cpu_usage%${NC}"
    echo -e "   Memória: ${YELLOW}$mem_usage%${NC}"
    echo -e "   Disco: ${YELLOW}$disk_usage%${NC}"
    
    log_with_time "Uso de recursos - CPU: $cpu_usage%, Memória: $mem_usage%, Disco: $disk_usage%"
}

# Função principal de monitoramento
main_monitor() {
    echo -e "${BLUE}🔍 Iniciando monitoramento VPN...${NC}"
    echo -e "${CYAN}📅 $(date)${NC}"
    echo -e "${CYAN}📋 Relatório será salvo em: $REPORT_FILE${NC}"
    echo ""
    
    log_with_time "=== INÍCIO DO MONITORAMENTO VPN ==="
    
    # Verificar serviços
    echo -e "${BLUE}🔧 Verificando serviços...${NC}"
    check_process "openvpn" "OpenVPN Server"
    
    # Verificar Docker
    if command -v docker >/dev/null; then
        check_service "docker" "Docker Service"
        
        # Verificar container da API
        if docker ps | grep -q "API-VPN"; then
            echo -e "${GREEN}✅ Container API-VPN: RODANDO${NC}"
            log_with_time "Container API-VPN: RODANDO"
        else
            echo -e "${RED}❌ Container API-VPN: PARADO${NC}"
            log_with_time "Container API-VPN: PARADO"
        fi
    fi
    
    echo ""
    
    # Verificar portas
    echo -e "${BLUE}🌐 Verificando portas...${NC}"
    check_port "1194" "udp" "OpenVPN"
    check_port "8080" "tcp" "API VPN"
    echo ""
    
    # Verificar API
    echo -e "${BLUE}🔌 Verificando API...${NC}"
    check_api
    echo ""
    
    # Verificar interfaces de rede
    echo -e "${BLUE}🌐 Verificando interfaces...${NC}"
    check_interface "tun0"
    echo ""
    
    # Contar clientes
    echo -e "${BLUE}👥 Verificando clientes...${NC}"
    count_connected_clients
    echo ""
    
    # Verificar logs de erro
    echo -e "${BLUE}📋 Verificando logs...${NC}"
    check_error_logs
    echo ""
    
    # Verificar recursos
    echo -e "${BLUE}📊 Verificando recursos...${NC}"
    check_resources
    echo ""
    
    log_with_time "=== FIM DO MONITORAMENTO VPN ==="
    
    echo -e "${GREEN}✅ Monitoramento concluído!${NC}"
    echo -e "${CYAN}📋 Relatório salvo em: $REPORT_FILE${NC}"
}

# Função para modo contínuo
continuous_monitor() {
    echo -e "${BLUE}🔄 Iniciando monitoramento contínuo (a cada 60 segundos)...${NC}"
    echo -e "${YELLOW}💡 Pressione Ctrl+C para parar${NC}"
    echo ""
    
    while true; do
        main_monitor
        echo ""
        echo -e "${CYAN}⏰ Aguardando 60 segundos...${NC}"
        sleep 60
        clear
    done
}

# Menu principal
case "$1" in
    "continuous"|"c")
        continuous_monitor
        ;;
    "help"|"h"|"--help")
        echo "Uso: $0 [opção]"
        echo ""
        echo "Opções:"
        echo "  (nenhuma)    - Executar monitoramento único"
        echo "  continuous   - Monitoramento contínuo"
        echo "  help         - Mostrar esta ajuda"
        ;;
    *)
        main_monitor
        ;;
esac
