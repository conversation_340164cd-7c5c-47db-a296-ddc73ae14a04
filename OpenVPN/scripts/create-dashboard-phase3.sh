#!/bin/bash

# Script para criar o projeto React moderno

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}⚡ CRIANDO PROJETO REACT - DASHBOARD VPN${NC}"
echo -e "${CYAN}=======================================${NC}"
echo ""

# Verificar se Node.js está instalado
echo -e "${BLUE}📦 Verificando Node.js...${NC}"
if ! command -v node &> /dev/null; then
    echo -e "${BLUE}📦 Instalando Node.js 18...${NC}"
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - >/dev/null 2>&1
    sudo apt-get install -y nodejs >/dev/null 2>&1
fi

NODE_VERSION=$(node --version)
echo -e "${GREEN}✅ Node.js: $NODE_VERSION${NC}"

# Criar diretório do projeto
PROJECT_DIR="/root/VPN/FrontEnd"
mkdir -p $PROJECT_DIR
cd $PROJECT_DIR

# Criar projeto React com Vite
echo -e "${BLUE}⚡ Criando projeto React com Vite...${NC}"
if [ ! -d "vpn-dashboard" ]; then
    npm create vite@latest vpn-dashboard -- --template react-ts >/dev/null 2>&1
    cd vpn-dashboard
    
    # Instalar dependências básicas
    echo -e "${BLUE}📦 Instalando dependências...${NC}"
    npm install >/dev/null 2>&1
    
    echo -e "${GREEN}✅ Projeto React criado${NC}"
else
    echo -e "${YELLOW}⚠️ Projeto já existe, pulando criação${NC}"
    cd vpn-dashboard
fi

echo ""
echo -e "${GREEN}🎉 PROJETO REACT CRIADO COM SUCESSO!${NC}"
echo -e "${GREEN}===================================${NC}"
echo ""
echo -e "${YELLOW}📁 ESTRUTURA CRIADA:${NC}"
echo -e "   Diretório: /root/VPN/FrontEnd/vpn-dashboard"
echo -e "   Template: React + TypeScript + Vite"
echo -e "   Dependências: Instaladas"
echo ""
echo -e "${CYAN}⚠️ PRÓXIMOS PASSOS:${NC}"
echo -e "   1. Configurar Tailwind CSS e Shadcn/UI"
echo -e "   2. Criar componentes do dashboard"
echo -e "   3. Configurar Docker"
