#!/bin/bash

# Script Interativo para Criação de Clientes VPN - Administrador
# Versão: Fase 1 (VPN Básica)

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Função para exibir mensagens coloridas
print_message() {
    local color="$1"
    local message="$2"
    echo -e "${color}${message}${NC}"
}

# Função para verificar pré-requisitos
check_prerequisites() {
    print_message "$BLUE" "🔍 Verificando pré-requisitos..."
    
    # Verificar se está executando como root
    if [ "$EUID" -ne 0 ]; then
        print_message "$RED" "❌ Este script deve ser executado como root!"
        print_message "$YELLOW" "💡 Execute: sudo $0"
        exit 1
    fi
    
    # Verificar se easy-rsa existe
    if [ ! -d "/root/VPN/OpenVPN/easy-rsa" ]; then
        print_message "$RED" "❌ Diretório easy-rsa não encontrado!"
        print_message "$YELLOW" "💡 Execute primeiro as etapas 1 e 2 da implementação"
        exit 1
    fi
    
    # Verificar se CA existe
    if [ ! -f "/root/VPN/OpenVPN/easy-rsa/pki/ca.crt" ]; then
        print_message "$RED" "❌ Certificado CA não encontrado!"
        print_message "$YELLOW" "💡 Execute primeiro a configuração PKI (Etapa 2)"
        exit 1
    fi
    
    # Verificar se OpenVPN está rodando
    if ! systemctl is-active --quiet openvpn@server; then
        print_message "$YELLOW" "⚠️ Servidor OpenVPN não está rodando"
        print_message "$BLUE" "🔄 Iniciando servidor OpenVPN..."
        systemctl start openvpn@server
        sleep 2
        
        if systemctl is-active --quiet openvpn@server; then
            print_message "$GREEN" "✅ Servidor OpenVPN iniciado!"
        else
            print_message "$RED" "❌ Falha ao iniciar servidor OpenVPN"
            print_message "$YELLOW" "💡 Verifique a configuração do servidor"
            exit 1
        fi
    else
        print_message "$GREEN" "✅ Servidor OpenVPN está rodando"
    fi
    
    print_message "$GREEN" "✅ Todos os pré-requisitos atendidos!"
}

# Função para listar clientes existentes
list_existing_clients() {
    print_message "$BLUE" "📋 Clientes existentes:"
    
    if ls /root/VPN/OpenVPN/easy-rsa/pki/issued/*.crt 2>/dev/null | grep -v server.crt >/dev/null; then
        ls /root/VPN/OpenVPN/easy-rsa/pki/issued/*.crt 2>/dev/null | grep -v server.crt | while read cert; do
            client_name=$(basename "$cert" .crt)
            creation_date=$(stat -c %y "$cert" | cut -d' ' -f1)
            print_message "$NC" "  • $client_name (criado em: $creation_date)"
        done
    else
        print_message "$YELLOW" "  Nenhum cliente criado ainda"
    fi
    echo ""
}

# INÍCIO DO SCRIPT PRINCIPAL
clear
print_message "$CYAN" "🔐 CRIADOR DE CLIENTE OPENVPN - ADMINISTRADOR"
print_message "$CYAN" "=============================================="
print_message "$YELLOW" "Rede VPN: *********/24 (sem conflitos)"
print_message "$YELLOW" "Fase: VPN Básica (sem banco de dados)"
echo ""

# Verificar pré-requisitos
check_prerequisites

echo ""

# Listar clientes existentes
list_existing_clients

# Solicitar senha de administração
print_message "$YELLOW" "🔐 AUTENTICAÇÃO"
echo -n "Digite a senha de administração: "
read -s ADMIN_PASSWORD
echo ""

if [ "$ADMIN_PASSWORD" != "etenopenvpn" ]; then
    print_message "$RED" "❌ Senha incorreta!"
    exit 1
fi

print_message "$GREEN" "✅ Senha correta!"

# Solicitar nome do dispositivo
print_message "$YELLOW" "📝 IDENTIFICAÇÃO DO DISPOSITIVO"
while true; do
    echo -n "Digite o nome único do dispositivo (ex: notebook-mauricio): "
    read CLIENT_NAME
    
    # Validar nome (apenas letras, números, hífen e underscore)
    if [[ ! "$CLIENT_NAME" =~ ^[a-zA-Z0-9_-]+$ ]]; then
        print_message "$RED" "❌ Nome inválido! Use apenas letras, números, _ ou -"
        continue
    fi
    
    # Verificar tamanho mínimo
    if [ ${#CLIENT_NAME} -lt 3 ]; then
        print_message "$RED" "❌ Nome muito curto! Use pelo menos 3 caracteres"
        continue
    fi
    
    # Verificar se já existe
    if [ -f "/root/VPN/OpenVPN/easy-rsa/pki/issued/${CLIENT_NAME}.crt" ]; then
        print_message "$RED" "❌ Cliente '$CLIENT_NAME' já existe!"
        print_message "$YELLOW" "💡 Escolha um nome diferente ou use o comando de listagem"
        continue
    fi
    
    break
done

# Solicitar tipo de plataforma
print_message "$YELLOW" "🖥️ PLATAFORMA DO DISPOSITIVO"
echo "Selecione a plataforma:"
echo "1) Windows"
echo "2) Raspberry Pi"
echo "3) Linux/Ubuntu"
echo "4) Android"
echo "5) iOS"
echo "6) Outro"
read -p "Opção (1-6): " PLATFORM_OPTION

case $PLATFORM_OPTION in
    1) PLATFORM="Windows" ;;
    2) PLATFORM="Raspberry Pi" ;;
    3) PLATFORM="Linux/Ubuntu" ;;
    4) PLATFORM="Android" ;;
    5) PLATFORM="iOS" ;;
    6) PLATFORM="Outro" ;;
    *) PLATFORM="Não especificado" ;;
esac

print_message "$GREEN" "✅ Dispositivo: $CLIENT_NAME ($PLATFORM)"

# Confirmação final
echo ""
print_message "$YELLOW" "📋 RESUMO DA CRIAÇÃO:"
print_message "$NC" "• Nome do cliente: $CLIENT_NAME"
print_message "$NC" "• Plataforma: $PLATFORM"
print_message "$NC" "• Rede VPN: *********/24"
echo ""
read -p "Confirma a criação do cliente? (s/N): " CONFIRM

if [[ ! "$CONFIRM" =~ ^[Ss]$ ]]; then
    print_message "$YELLOW" "❌ Operação cancelada pelo usuário"
    exit 0
fi

# Criar certificado
print_message "$BLUE" "🔧 Criando certificado para $CLIENT_NAME..."

cd /root/VPN/OpenVPN/easy-rsa

# Verificar se vars existe
if [ ! -f "vars" ]; then
    print_message "$RED" "❌ Arquivo 'vars' não encontrado!"
    print_message "$YELLOW" "💡 Execute a configuração PKI primeiro"
    exit 1
fi

# Gerar certificado do cliente (modo não-interativo)
source vars
print_message "$BLUE" "🔄 Gerando certificado (aguarde)..."

# Usar expect se disponível, senão usar echo
if command -v expect >/dev/null 2>&1; then
    expect << EXPECTEOF
spawn ./easyrsa gen-req $CLIENT_NAME nopass
expect "Common Name" { send "$CLIENT_NAME\r" }
expect eof
EXPECTEOF
    
    expect << EXPECTEOF
spawn ./easyrsa sign-req client $CLIENT_NAME
expect "Confirm request details" { send "yes\r" }
expect eof
EXPECTEOF
else
    # Fallback para modo batch
    ./easyrsa gen-req "$CLIENT_NAME" nopass batch
    ./easyrsa sign-req client "$CLIENT_NAME" batch
fi

# Verificar se certificado foi criado
if [ ! -f "pki/issued/${CLIENT_NAME}.crt" ]; then
    print_message "$RED" "❌ Erro ao criar certificado!"
    print_message "$YELLOW" "💡 Verifique os logs e tente novamente"
    exit 1
fi

print_message "$GREEN" "✅ Certificado criado com sucesso!"

# Criar arquivo .ovpn
print_message "$BLUE" "📄 Criando arquivo de configuração .ovpn..."

cat > "/root/VPN/OpenVPN/clients/${CLIENT_NAME}.ovpn" << EOL
# Cliente OpenVPN - Criado automaticamente
# Nome: $CLIENT_NAME
# Plataforma: $PLATFORM
# Criado em: $(date)
# Rede VPN: *********/24

client
dev tun
proto udp
remote vpn.evo-eden.site 1194
resolv-retry infinite
nobind
persist-key
persist-tun
comp-lzo
verb 3

<ca>
$(cat pki/ca.crt)
</ca>

<cert>
$(cat pki/issued/${CLIENT_NAME}.crt)
</cert>

<key>
$(cat pki/private/${CLIENT_NAME}.key)
</key>

<tls-auth>
$(cat pki/ta.key)
</tls-auth>
key-direction 1
EOL

# Verificar se arquivo .ovpn foi criado
if [ ! -f "/root/VPN/OpenVPN/clients/${CLIENT_NAME}.ovpn" ]; then
    print_message "$RED" "❌ Erro ao criar arquivo .ovpn!"
    exit 1
fi

print_message "$GREEN" "✅ Arquivo .ovpn criado com sucesso!"

# Registrar cliente em arquivo de controle simples (Fase 1)
echo "$(date '+%Y-%m-%d %H:%M:%S'),$CLIENT_NAME,$PLATFORM,created" >> /root/VPN/OpenVPN/logs/clients-registry.log

# Verificar tamanho do arquivo
file_size=$(stat -c%s "/root/VPN/OpenVPN/clients/${CLIENT_NAME}.ovpn")
if [ "$file_size" -lt 1000 ]; then
    print_message "$YELLOW" "⚠️ Arquivo .ovpn parece muito pequeno ($file_size bytes)"
    print_message "$YELLOW" "💡 Verifique se todos os certificados foram incluídos"
fi

# Mostrar próximo IP provável
CONNECTED_CLIENTS=$(grep -c "CLIENT_LIST" /root/VPN/OpenVPN/logs/openvpn-status.log 2>/dev/null || echo "0")
NEXT_IP=$((CONNECTED_CLIENTS + 2))

# Estatísticas finais
echo ""
print_message "$GREEN" "🎉 CLIENTE CRIADO COM SUCESSO!"
print_message "$GREEN" "================================"
echo ""
print_message "$BLUE" "📋 INFORMAÇÕES DO CLIENTE:"
print_message "$NC" "• Nome: $CLIENT_NAME"
print_message "$NC" "• Plataforma: $PLATFORM"
print_message "$NC" "• Arquivo: /root/VPN/OpenVPN/clients/${CLIENT_NAME}.ovpn"
print_message "$NC" "• Tamanho: $file_size bytes"
print_message "$NC" "• Rede VPN: *********/24"
print_message "$NC" "• IP provável: 10.12.0.$NEXT_IP"
echo ""

print_message "$YELLOW" "📤 TRANSFERÊNCIA DO ARQUIVO:"
print_message "$NC" "• SCP: scp <EMAIL>:/root/VPN/OpenVPN/clients/${CLIENT_NAME}.ovpn ."
print_message "$NC" "• HTTP: Disponível via API na porta 8080"
echo ""

print_message "$YELLOW" "📋 PRÓXIMOS PASSOS PARA O CLIENTE:"
case $PLATFORM in
    "Windows")
        print_message "$NC" "1. Baixar e instalar OpenVPN GUI"
        print_message "$NC" "2. Copiar ${CLIENT_NAME}.ovpn para C:\\Program Files\\OpenVPN\\config\\"
        print_message "$NC" "3. Conectar via OpenVPN GUI"
        print_message "$NC" "4. Configurar TightVNC para acesso remoto"
        ;;
    "Raspberry Pi"|"Linux/Ubuntu")
        print_message "$NC" "1. Baixar ${CLIENT_NAME}.ovpn"
        print_message "$NC" "2. sudo cp ${CLIENT_NAME}.ovpn /etc/openvpn/client/"
        print_message "$NC" "3. sudo systemctl enable openvpn-client@${CLIENT_NAME}"
        print_message "$NC" "4. sudo systemctl start openvpn-client@${CLIENT_NAME}"
        ;;
    *)
        print_message "$NC" "1. Baixar o arquivo ${CLIENT_NAME}.ovpn"
        print_message "$NC" "2. Instalar OpenVPN no dispositivo"
        print_message "$NC" "3. Importar o arquivo .ovpn"
        print_message "$NC" "4. Conectar à VPN"
        ;;
esac

echo ""
print_message "$CYAN" "✨ Cliente $CLIENT_NAME pronto para uso!"

# Perguntar se deseja criar outro cliente
echo ""
read -p "Deseja criar outro cliente? (s/N): " CREATE_ANOTHER

if [[ "$CREATE_ANOTHER" =~ ^[Ss]$ ]]; then
    print_message "$BLUE" "🔄 Reiniciando script..."
    exec "$0"
fi

print_message "$GREEN" "👋 Script finalizado!"
