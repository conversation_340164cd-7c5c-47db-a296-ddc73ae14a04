#!/bin/bash

# Script de Instalação Completa da Fase 3 - Dashboard VPN com Docker + Traefik

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}🚀 INSTALAÇÃO COMPLETA DA FASE 3 - DOCKER + TRAEFIK${NC}"
echo -e "${CYAN}===================================================${NC}"
echo ""

# Verificar se Fase 2 está instalada
if [ ! -d "/root/VPN/DataBase" ]; then
    echo -e "${RED}❌ Fase 2 não encontrada!${NC}"
    echo -e "${YELLOW}💡 Execute primeiro a implementação da Fase 2${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Fase 2 detectada${NC}"

# Verificar se Docker está rodando
if ! systemctl is-active --quiet docker; then
    echo -e "${RED}❌ Docker não está rodando!${NC}"
    echo -e "${YELLOW}💡 Inicie o Docker: systemctl start docker${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Docker está rodando${NC}"

# Verificar se Traefik está rodando
if ! docker ps | grep -q traefik; then
    echo -e "${RED}❌ Container Traefik não encontrado!${NC}"
    echo -e "${YELLOW}💡 Verifique se o Traefik está configurado e rodando${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Traefik está rodando${NC}"

# 1. Instalar Node.js se necessário
echo -e "${BLUE}📦 Verificando Node.js...${NC}"
if ! command -v node >/dev/null 2>&1; then
    echo -e "${BLUE}Instalando Node.js 18...${NC}"
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - >/dev/null 2>&1
    sudo apt-get install -y nodejs >/dev/null 2>&1
fi
NODE_VERSION=$(node --version)
echo -e "${GREEN}✅ Node.js: $NODE_VERSION${NC}"

# 2. Executar configuração Docker + Traefik
echo -e "${BLUE}🐳 Configurando Docker + Traefik...${NC}"
/root/VPN/OpenVPN/scripts/setup-docker-traefik.sh

# 3. Criar projeto React se não existir
echo -e "${BLUE}⚡ Criando projeto React...${NC}"
if [ ! -d "/root/VPN/FrontEnd/vpn-dashboard" ]; then
    /root/VPN/OpenVPN/scripts/create-dashboard-phase3.sh >/dev/null 2>&1
fi

# 4. Instalar dependências
echo -e "${BLUE}📦 Instalando dependências...${NC}"
cd /root/VPN/FrontEnd/vpn-dashboard
npm install >/dev/null 2>&1

# 5. Copiar logo da empresa
echo -e "${BLUE}🎨 Configurando assets...${NC}"
mkdir -p public/assets
if [ -f "/root/VPN/assets/logo_sem_fundo_branco.png" ]; then
    cp /root/VPN/assets/logo_sem_fundo_branco.png public/assets/
    echo -e "${GREEN}✅ Logo da empresa copiada${NC}"
else
    echo -e "${YELLOW}⚠️ Logo não encontrada em /root/VPN/assets/logo_sem_fundo_branco.png${NC}"
fi

# 6. Build e iniciar container
echo -e "${BLUE}🐳 Construindo e iniciando container...${NC}"
cd /root/VPN/FrontEnd
docker-compose down >/dev/null 2>&1
docker-compose up -d --build

# 7. Aguardar inicialização
echo -e "${BLUE}⏳ Aguardando inicialização...${NC}"
sleep 30

# 8. Verificar status
if docker ps | grep -q vpn-frontend; then
    echo -e "${GREEN}✅ Container iniciado com sucesso${NC}"
    
    # Verificar se está respondendo
    CONTAINER_IP=$(docker inspect vpn-frontend | grep '"IPAddress"' | tail -1 | cut -d'"' -f4)
    if curl -s -o /dev/null -w "%{http_code}" http://$CONTAINER_IP/ | grep -q "200"; then
        echo -e "${GREEN}✅ Frontend respondendo corretamente${NC}"
    else
        echo -e "${YELLOW}⚠️ Frontend pode estar iniciando...${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ Problema ao iniciar container${NC}"
    echo -e "${BLUE}Verificando logs...${NC}"
    docker-compose logs vpn-frontend
fi

echo ""
echo -e "${GREEN}🎉 FASE 3 INSTALADA COM SUCESSO!${NC}"
echo -e "${GREEN}=================================${NC}"
echo ""
echo -e "${YELLOW}🌐 DASHBOARD VPN MODERNO DISPONÍVEL:${NC}"
echo -e "   🔗 URL: https://vpn.evo-eden.site (quando configurado no Traefik)"
echo -e "   🔐 Login: admin"
echo -e "   🔑 Senha: VPNnbr5410!"
echo -e "   🐳 Container: vpn-frontend"
echo -e "   🔀 IP Container: $CONTAINER_IP"
echo ""
echo -e "${CYAN}⚠️ IMPORTANTE:${NC}"
echo -e "   Container criado e funcionando"
echo -e "   Para acesso via domínio, configure o Traefik para descobrir o container"
echo ""
echo -e "${BLUE}🔧 COMANDOS ÚTEIS:${NC}"
echo -e "   docker-compose logs -f vpn-frontend"
echo -e "   docker-compose restart vpn-frontend"
echo -e "   docker logs traefik"
