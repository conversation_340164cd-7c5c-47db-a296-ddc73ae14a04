#!/bin/bash

# Script para inicializar automação da VPN com Docker

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 Iniciando sistema de automação VPN com Docker...${NC}"

# Verificar se Docker está instalado
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker não está instalado!${NC}"
    echo -e "${YELLOW}💡 Instale o Docker primeiro: curl -fsSL https://get.docker.com | sh${NC}"
    exit 1
fi

# Verificar se Docker Compose está instalado
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose não está instalado!${NC}"
    echo -e "${YELLOW}💡 Instale o Docker Compose primeiro${NC}"
    exit 1
fi

# Verificar se OpenVPN está rodando
if ! systemctl is-active --quiet openvpn@server; then
    echo -e "${YELLOW}🔄 OpenVPN não está rodando. Iniciando...${NC}"
    systemctl start openvpn@server
    sleep 3
    
    if systemctl is-active --quiet openvpn@server; then
        echo -e "${GREEN}✅ OpenVPN iniciado com sucesso!${NC}"
    else
        echo -e "${RED}❌ Falha ao iniciar OpenVPN${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ OpenVPN já está rodando${NC}"
fi

# Verificar se API Docker já está rodando
if docker ps | grep -q "API-VPN"; then
    echo -e "${GREEN}✅ API VPN (Docker) já está rodando${NC}"
else
    echo -e "${BLUE}🔄 Iniciando API VPN no Docker...${NC}"
    
    # Ir para diretório da API
    cd /root/VPN/OpenVPN/api
    
    # Verificar se arquivos da API existem
    if [ ! -f "docker-compose.yml" ]; then
        echo -e "${RED}❌ Arquivos da API não encontrados!${NC}"
        echo -e "${YELLOW}💡 Execute primeiro a seção 4.2 para criar os arquivos da API${NC}"
        exit 1
    fi
    
    # Construir e iniciar container
    docker-compose up -d --build
    
    # Aguardar inicialização
    sleep 5
    
    # Verificar se container está rodando
    if docker ps | grep -q "API-VPN"; then
        echo -e "${GREEN}✅ API VPN iniciada no Docker!${NC}"
    else
        echo -e "${RED}❌ Falha ao iniciar API VPN${NC}"
        echo -e "${YELLOW}💡 Verifique os logs: docker-compose logs${NC}"
        exit 1
    fi
fi

# Configurar firewall para API (se necessário)
if ! iptables -L | grep -q "8080"; then
    echo -e "${BLUE}🔧 Configurando firewall para API...${NC}"
    iptables -A INPUT -p tcp --dport 8080 -j ACCEPT
    iptables-save > /etc/iptables/rules.v4
fi

# Testar API
echo -e "${BLUE}🔍 Testando API...${NC}"
if curl -s --connect-timeout 5 "http://localhost:8080/api/health" >/dev/null; then
    echo -e "${GREEN}✅ API respondendo corretamente!${NC}"
else
    echo -e "${YELLOW}⚠️ API pode estar iniciando ainda...${NC}"
fi

echo ""
echo -e "${GREEN}✅ Sistema de automação VPN iniciado com sucesso!${NC}"
echo ""
echo -e "${BLUE}📋 Status dos serviços:${NC}"
echo -e "   - OpenVPN: ${GREEN}$(systemctl is-active openvpn@server)${NC}"
echo -e "   - API VPN (Docker): ${GREEN}$(docker ps | grep -q "API-VPN" && echo "ativo" || echo "inativo")${NC}"
echo -e "   - Container: ${GREEN}$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep API-VPN || echo "não encontrado")${NC}"
echo ""
echo -e "${YELLOW}🌐 Clientes podem agora usar os scripts automáticos:${NC}"
echo -e "   - Windows: windows-setup.ps1"
echo -e "   - Raspberry Pi: raspberry-setup.sh"
echo ""
echo -e "${BLUE}📊 Para monitorar:${NC}"
echo -e "   - Logs da API: docker-compose logs -f"
echo -e "   - Status: docker ps"
echo -e "   - Health check: curl http://localhost:8080/api/health"
