#!/bin/bash
set -e

# Script de desconexão de cliente OpenVPN - Fase 5
# Este script é executado quando um cliente se desconecta da VPN
# Variáveis disponíveis do OpenVPN:
# $common_name - Nome do cliente
# $bytes_received - Bytes recebidos
# $bytes_sent - Bytes enviados
# $time_duration - Duração da sessão em segundos

CLIENT_NAME="$common_name"
BYTES_RECEIVED="$bytes_received"
BYTES_SENT="$bytes_sent"
DURATION="$time_duration"
TIMESTAMP=$(date -Iseconds)

# Log local
echo "$(date '+%Y-%m-%d %H:%M:%S') CLIENT_DISCONNECT: $CLIENT_NAME (RX: $BYTES_RECEIVED, TX: $BYTES_SENT, Duration: ${DURATION}s)" >> /root/VPN/OpenVPN/logs/events.log

# Notificar backend (CRÍTICO para integração Fase 5)
curl -s -X POST http://localhost:8080/api/v1/internal/events/disconnect \
  -H "Content-Type: application/json" \
  -H "X-Internal-Token: vpn_internal_secret_2024" \
  -d "{
    \"client_name\": \"$CLIENT_NAME\",
    \"bytes_received\": $BYTES_RECEIVED,
    \"bytes_sent\": $BYTES_SENT,
    \"duration\": $DURATION,
    \"timestamp\": \"$TIMESTAMP\",
    \"event_type\": \"disconnect\"
  }" || echo "$(date '+%Y-%m-%d %H:%M:%S') ERROR: Failed to notify backend about disconnection" >> /root/VPN/OpenVPN/logs/events.log

# Log de sucesso
echo "$(date '+%Y-%m-%d %H:%M:%S') SUCCESS: Client $CLIENT_NAME disconnection processed" >> /root/VPN/OpenVPN/logs/events.log

exit 0
