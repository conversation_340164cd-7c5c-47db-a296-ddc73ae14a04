#!/bin/bash
set -e

# Script de conexão de cliente OpenVPN - Fase 5
# Este script é executado quando um cliente se conecta à VPN
# Variáveis disponíveis do OpenVPN:
# $common_name - Nome do cliente
# $trusted_ip - IP externo do cliente
# $ifconfig_pool_remote_ip - IP VPN atribuído

CLIENT_NAME="$common_name"
EXTERNAL_IP="$trusted_ip"
VPN_IP="$ifconfig_pool_remote_ip"
TIMESTAMP=$(date -Iseconds)

# Log local
echo "$(date '+%Y-%m-%d %H:%M:%S') CLIENT_CONNECT: $CLIENT_NAME from $EXTERNAL_IP assigned $VPN_IP" >> /root/VPN/OpenVPN/logs/events.log

# Notificar backend (CRÍTICO para integração Fase 5)
curl -s -X POST http://localhost:8080/api/v1/internal/events/connect \
  -H "Content-Type: application/json" \
  -H "X-Internal-Token: vpn_internal_secret_2024" \
  -d "{
    \"client_name\": \"$CLIENT_NAME\",
    \"external_ip\": \"$EXTERNAL_IP\",
    \"vpn_ip\": \"$VPN_IP\",
    \"timestamp\": \"$TIMESTAMP\",
    \"event_type\": \"connect\"
  }" || echo "$(date '+%Y-%m-%d %H:%M:%S') ERROR: Failed to notify backend about connection" >> /root/VPN/OpenVPN/logs/events.log

# Log de sucesso
echo "$(date '+%Y-%m-%d %H:%M:%S') SUCCESS: Client $CLIENT_NAME connection processed" >> /root/VPN/OpenVPN/logs/events.log

exit 0
