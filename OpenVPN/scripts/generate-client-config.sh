#!/bin/bash

# Script para gerar configuração de cliente OpenVPN - Fase 5
CLIENT_NAME="$1"

if [ -z "$CLIENT_NAME" ]; then
    echo "Uso: $0 <nome-do-cliente>"
    exit 1
fi

OUTPUT_FILE="/tmp/${CLIENT_NAME}.ovpn"

cat > "$OUTPUT_FILE" << EOF
client
dev tun
proto udp
remote 127.0.0.1 1194
resolv-retry infinite
nobind
persist-key
persist-tun
cipher AES-256-GCM
auth SHA256
verb 3

<ca>
$(cat /root/VPN/OpenVPN/easy-rsa/pki/ca.crt)
</ca>

<cert>
$(cat /root/VPN/OpenVPN/easy-rsa/pki/issued/${CLIENT_NAME}.crt)
</cert>

<key>
$(cat /root/VPN/OpenVPN/easy-rsa/pki/private/${CLIENT_NAME}.key)
</key>

<tls-auth>
$(cat /root/VPN/OpenVPN/easy-rsa/pki/ta.key)
</tls-auth>
key-direction 1
EOF

echo "Arquivo de configuração criado: $OUTPUT_FILE"
