#!/bin/bash

# Script de Automação VPN para Raspberry Pi
# Versão: 1.0
# Compatível com: Raspberry Pi OS (Debian/Ubuntu)

# Configurações
VPN_PASSWORD="etenopenvpn"
SERVER_URL="${1:-vpn.evo-eden.site:8080}"
API_URL="http://$SERVER_URL/api"
CLIENT_NAME="$2"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${BLUE}🚀 Iniciando configuração automática da VPN para Raspberry Pi...${NC}"
echo -e "${CYAN}📋 Servidor: $SERVER_URL${NC}"

# Verificar se está executando como root
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}❌ Este script precisa ser executado como root!${NC}"
    echo -e "${YELLOW}💡 Execute: sudo $0${NC}"
    exit 1
fi

# Solicitar nome do cliente se não fornecido
if [ -z "$CLIENT_NAME" ]; then
    echo -e "${CYAN}📝 Digite o nome do cliente (sem espaços, apenas letras, números, _ e -):${NC}"
    read -p "Nome do cliente: " CLIENT_NAME
    
    if [[ ! "$CLIENT_NAME" =~ ^[a-zA-Z0-9_-]+$ ]]; then
        echo -e "${RED}❌ Nome inválido! Use apenas letras, números, _ e -${NC}"
        exit 1
    fi
fi

echo -e "${CYAN}👤 Cliente: $CLIENT_NAME${NC}"

# Atualizar sistema
echo -e "${BLUE}🔄 Atualizando sistema...${NC}"
apt update -qq

# Instalar dependências
echo -e "${BLUE}📦 Instalando dependências...${NC}"
apt install -y openvpn curl jq resolvconf

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Dependências instaladas!${NC}"
else
    echo -e "${RED}❌ Erro ao instalar dependências${NC}"
    exit 1
fi

# Verificar conectividade com a API
echo -e "${BLUE}🔍 Testando conectividade com o servidor...${NC}"
if curl -s --connect-timeout 10 "$API_URL/health" | jq -e '.success' >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Servidor VPN respondendo!${NC}"
else
    echo -e "${RED}❌ Não foi possível conectar ao servidor VPN!${NC}"
    echo -e "${RED}   Verifique se o servidor está online: $SERVER_URL${NC}"
    exit 1
fi

# Solicitar criação do certificado
echo -e "${BLUE}📋 Solicitando criação do certificado...${NC}"
RESPONSE=$(curl -s -X POST "$API_URL/create-client" \
    -H "Content-Type: application/json" \
    -d "{
        \"password\": \"$VPN_PASSWORD\",
        \"clientName\": \"$CLIENT_NAME\",
        \"platform\": \"raspberry-pi\"
    }")

if echo "$RESPONSE" | jq -e '.success' >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Certificado solicitado com sucesso!${NC}"
    MESSAGE=$(echo "$RESPONSE" | jq -r '.message')
    echo -e "${CYAN}📝 $MESSAGE${NC}"
    
    NOTE=$(echo "$RESPONSE" | jq -r '.note // empty')
    if [ -n "$NOTE" ]; then
        echo -e "${YELLOW}⚠️ $NOTE${NC}"
    fi
else
    ERROR=$(echo "$RESPONSE" | jq -r '.error // "Erro desconhecido"')
    echo -e "${RED}❌ Erro ao solicitar certificado: $ERROR${NC}"
    exit 1
fi

# Criar diretório para configurações VPN
VPN_DIR="/etc/openvpn/client"
mkdir -p "$VPN_DIR"

# Criar script de conexão
cat > "/usr/local/bin/vpn-connect" << 'EOF'
#!/bin/bash
VPN_CONFIG="/etc/openvpn/client/client.ovpn"

if [ ! -f "$VPN_CONFIG" ]; then
    echo "❌ Arquivo de configuração não encontrado: $VPN_CONFIG"
    echo "💡 Coloque o arquivo .ovpn recebido do administrador em $VPN_CONFIG"
    exit 1
fi

echo "🔄 Conectando à VPN..."
openvpn --config "$VPN_CONFIG" --daemon --log /var/log/openvpn-client.log

sleep 3
if pgrep openvpn >/dev/null; then
    echo "✅ VPN conectada com sucesso!"
    echo "📊 Para verificar status: vpn-status"
    echo "🔌 Para desconectar: vpn-disconnect"
else
    echo "❌ Falha ao conectar VPN"
    echo "📋 Verifique os logs: tail /var/log/openvpn-client.log"
fi
EOF

# Criar script de desconexão
cat > "/usr/local/bin/vpn-disconnect" << 'EOF'
#!/bin/bash
echo "🔄 Desconectando VPN..."
pkill openvpn
sleep 2

if ! pgrep openvpn >/dev/null; then
    echo "✅ VPN desconectada!"
else
    echo "⚠️ Forçando desconexão..."
    pkill -9 openvpn
    echo "✅ VPN desconectada!"
fi
EOF

# Criar script de status
cat > "/usr/local/bin/vpn-status" << 'EOF'
#!/bin/bash
if pgrep openvpn >/dev/null; then
    echo "✅ VPN está conectada"
    echo "📊 Processo: $(pgrep openvpn)"
    echo "🌐 Interface tun:"
    ip addr show tun0 2>/dev/null || echo "   Interface tun0 não encontrada"
    echo "📋 Últimas linhas do log:"
    tail -5 /var/log/openvpn-client.log 2>/dev/null || echo "   Log não encontrado"
else
    echo "❌ VPN não está conectada"
fi
EOF

# Tornar scripts executáveis
chmod +x /usr/local/bin/vpn-connect
chmod +x /usr/local/bin/vpn-disconnect
chmod +x /usr/local/bin/vpn-status

# Configurar systemd service (opcional)
cat > "/etc/systemd/system/vpn-client.service" << EOF
[Unit]
Description=OpenVPN Client
After=network.target

[Service]
Type=forking
ExecStart=/usr/bin/openvpn --config /etc/openvpn/client/client.ovpn --daemon
ExecReload=/bin/kill -HUP \$MAINPID
KillMode=process
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload

echo ""
echo -e "${GREEN}🎉 Configuração concluída!${NC}"
echo ""
echo -e "${CYAN}📋 Próximos passos:${NC}"
echo -e "   1. O administrador irá gerar seu certificado"
echo -e "   2. Coloque o arquivo .ovpn recebido em: ${YELLOW}/etc/openvpn/client/client.ovpn${NC}"
echo -e "   3. Use os comandos para gerenciar a VPN:"
echo ""
echo -e "${CYAN}📱 Comandos disponíveis:${NC}"
echo -e "   ${GREEN}vpn-connect${NC}     - Conectar à VPN"
echo -e "   ${GREEN}vpn-disconnect${NC}  - Desconectar da VPN"
echo -e "   ${GREEN}vpn-status${NC}      - Ver status da conexão"
echo ""
echo -e "${CYAN}🔧 Para auto-iniciar na boot:${NC}"
echo -e "   ${YELLOW}sudo systemctl enable vpn-client${NC}"
echo ""
echo -e "${CYAN}📋 Logs da VPN:${NC}"
echo -e "   ${YELLOW}tail -f /var/log/openvpn-client.log${NC}"
