#!/bin/bash

# Correção de Problemas no Banco de Dados - Fase 2

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}🔧 CORREÇÃO DE PROBLEMAS NO BANCO - FASE 2${NC}"
echo -e "${CYAN}===========================================${NC}"
echo ""

# Dados de conexão
DB_HOST="************"
DB_PORT="5432"
DB_USER="postgres"
DB_PASSWORD="ab3780bd73ee4e2804d566ce6fd96209"
DB_NAME="vpnetens"

echo -e "${BLUE}🔧 Corrigindo estrutura das tabelas...${NC}"

PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << 'SQLEOF'

-- Remover tabelas com problemas e recriar
DROP TABLE IF EXISTS vpn_sessions CASCADE;
DROP TABLE IF EXISTS network_events CASCADE;

-- Recriar tabela vpn_sessions sem campos calculados problemáticos
CREATE TABLE vpn_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID REFERENCES vpn_clients(id) ON DELETE CASCADE,
    client_name VARCHAR(100) NOT NULL,
    real_address INET NOT NULL,
    virtual_address INET,

    -- TIMESTAMPS ESSENCIAIS (TIMEZONE BRASIL -3)
    connected_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() AT TIME ZONE 'America/Sao_Paulo'),
    disconnected_at TIMESTAMP WITH TIME ZONE,

    -- DURAÇÃO E TRÁFEGO
    duration_seconds INTEGER,
    bytes_received BIGINT DEFAULT 0,
    bytes_sent BIGINT DEFAULT 0,

    -- STATUS E METADADOS
    disconnect_reason VARCHAR(50) DEFAULT 'unknown',
    server_port INTEGER,
    protocol VARCHAR(10) DEFAULT 'udp',
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'disconnected', 'timeout', 'error')),

    -- CAMPOS ESSENCIAIS PARA ANÁLISE (SEM GENERATED)
    session_date DATE,
    is_active BOOLEAN DEFAULT true,

    -- AUDITORIA
    created_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() AT TIME ZONE 'America/Sao_Paulo'),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() AT TIME ZONE 'America/Sao_Paulo'),

    -- CONSTRAINTS
    CONSTRAINT vpn_sessions_duration_check CHECK (duration_seconds >= 0),
    CONSTRAINT vpn_sessions_bytes_check CHECK (bytes_received >= 0 AND bytes_sent >= 0),
    CONSTRAINT vpn_sessions_timestamps_check CHECK (disconnected_at IS NULL OR disconnected_at >= connected_at)
);

-- Recriar tabela network_events
CREATE TABLE network_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES vpn_sessions(id) ON DELETE CASCADE,
    source_client VARCHAR(100) NOT NULL,
    destination_client VARCHAR(100) NOT NULL,
    source_ip INET NOT NULL,
    destination_ip INET NOT NULL,
    source_port INTEGER,
    destination_port INTEGER,
    protocol VARCHAR(10) NOT NULL,
    event_type VARCHAR(50) NOT NULL, -- 'ssh', 'vnc', 'ping', 'file_transfer', 'other'
    bytes_transferred BIGINT DEFAULT 0,
    duration_seconds INTEGER,
    detected_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() AT TIME ZONE 'America/Sao_Paulo'),
    event_date DATE,
    success BOOLEAN DEFAULT true,

    -- Constraints essenciais
    CONSTRAINT network_events_port_check CHECK (source_port > 0 AND destination_port > 0),
    CONSTRAINT network_events_bytes_check CHECK (bytes_transferred >= 0)
);

-- Criar triggers para atualizar campos automaticamente
CREATE OR REPLACE FUNCTION update_session_fields()
RETURNS TRIGGER AS $$
BEGIN
    -- Atualizar session_date
    NEW.session_date = NEW.connected_at::date;
    
    -- Atualizar is_active
    NEW.is_active = (NEW.disconnected_at IS NULL);
    
    -- Calcular duration_seconds se disconnected_at foi definido
    IF NEW.disconnected_at IS NOT NULL AND OLD.disconnected_at IS NULL THEN
        NEW.duration_seconds = EXTRACT(EPOCH FROM (NEW.disconnected_at - NEW.connected_at))::INTEGER;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_vpn_sessions_fields
    BEFORE INSERT OR UPDATE ON vpn_sessions
    FOR EACH ROW EXECUTE FUNCTION update_session_fields();

-- Trigger para network_events
CREATE OR REPLACE FUNCTION update_network_event_fields()
RETURNS TRIGGER AS $$
BEGIN
    NEW.event_date = NEW.detected_at::date;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_network_events_fields
    BEFORE INSERT OR UPDATE ON network_events
    FOR EACH ROW EXECUTE FUNCTION update_network_event_fields();

\echo '✅ Correções aplicadas com sucesso!'

SQLEOF

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Correções aplicadas${NC}"
else
    echo -e "${RED}❌ Erro ao aplicar correções${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 CORREÇÕES CONCLUÍDAS!${NC}"
