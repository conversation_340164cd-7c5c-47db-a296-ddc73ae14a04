#!/bin/bash

# Criação de Índices - Fase 2

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}📊 CRIAÇÃO DE ÍNDICES - FASE 2${NC}"
echo -e "${CYAN}==============================${NC}"
echo ""

# Dados de conexão
DB_HOST="************"
DB_PORT="5432"
DB_USER="postgres"
DB_PASSWORD="ab3780bd73ee4e2804d566ce6fd96209"
DB_NAME="vpnetens"

echo -e "${BLUE}📊 Criando índices estratégicos...${NC}"

PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << 'SQLEOF'

-- ============================================================================
-- ÍNDICES ESTRATÉGICOS PARA PERFORMANCE
-- ============================================================================

-- Índices para vpn_sessions
CREATE INDEX IF NOT EXISTS idx_vpn_sessions_client_date
ON vpn_sessions(client_name, session_date DESC);

CREATE INDEX IF NOT EXISTS idx_vpn_sessions_active
ON vpn_sessions(is_active, connected_at DESC);

CREATE INDEX IF NOT EXISTS idx_vpn_sessions_client_id
ON vpn_sessions(client_id);

-- Índices para client_status_log
CREATE INDEX IF NOT EXISTS idx_client_status_log_client_date
ON client_status_log(client_name, status_changed_at DESC);

CREATE INDEX IF NOT EXISTS idx_client_status_log_status
ON client_status_log(status, status_changed_at DESC);

-- Índices para network_events
CREATE INDEX IF NOT EXISTS idx_network_events_date_type
ON network_events(event_date, event_type);

CREATE INDEX IF NOT EXISTS idx_network_events_session
ON network_events(session_id);

-- Índices para system_events
CREATE INDEX IF NOT EXISTS idx_system_events_type_date
ON system_events(event_type, occurred_at DESC);

CREATE INDEX IF NOT EXISTS idx_system_events_severity
ON system_events(severity, occurred_at DESC);

-- Índices para daily_metrics
CREATE INDEX IF NOT EXISTS idx_daily_metrics_date_client
ON daily_metrics(metric_date DESC, client_name);

\echo '✅ Índices criados com sucesso!'

SQLEOF

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Índices criados${NC}"
else
    echo -e "${RED}❌ Erro ao criar índices${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 ÍNDICES CRIADOS COM SUCESSO!${NC}"
