#!/bin/bash

# Verificação do PostgreSQL Existente - Fase 2

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}🔍 VERIFICAÇÃO DO POSTGRESQL EXISTENTE - FASE 2${NC}"
echo -e "${CYAN}===============================================${NC}"
echo ""

# Dados de conexão
DB_HOST="************"
DB_PORT="5432"
DB_USER="postgres"
DB_PASSWORD="ab3780bd73ee4e2804d566ce6fd96209"
DB_NAME="vpnetens"

# Verificar se Fase 1 está instalada
if [ ! -f "/root/VPN/OpenVPN/configs/server.conf" ]; then
    echo -e "${RED}❌ Fase 1 não encontrada!${NC}"
    echo -e "${YELLOW}💡 Execute primeiro a implementação da Fase 1${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Fase 1 detectada${NC}"

# 1. Verificar se psql está instalado
echo -e "${BLUE}🔧 Verificando cliente PostgreSQL...${NC}"
if ! command -v psql &> /dev/null; then
    echo -e "${YELLOW}⚠️ Cliente psql não encontrado, instalando...${NC}"
    sudo apt update >/dev/null 2>&1
    sudo apt install -y postgresql-client >/dev/null 2>&1

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Cliente PostgreSQL instalado${NC}"
    else
        echo -e "${RED}❌ Erro ao instalar cliente PostgreSQL${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ Cliente PostgreSQL já está instalado${NC}"
fi

# 2. Testar conexão com o banco
echo -e "${BLUE}🧪 Testando conexão com o banco...${NC}"
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Conexão com banco estabelecida${NC}"
else
    echo -e "${RED}❌ Erro na conexão com o banco${NC}"
    echo -e "${YELLOW}💡 Verifique os dados de conexão${NC}"
    exit 1
fi

# 3. Verificar versão do PostgreSQL
echo -e "${BLUE}📊 Verificando versão do PostgreSQL...${NC}"
VERSION=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT version();" 2>/dev/null | head -1 | xargs)

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Versão: $VERSION${NC}"
else
    echo -e "${RED}❌ Erro ao verificar versão${NC}"
    exit 1
fi

# 4. Verificar bancos existentes
echo -e "${BLUE}🗄️ Verificando bancos existentes...${NC}"
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "\l" 2>/dev/null | grep -E "(vpnetens|dbetens)" >/dev/null

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Banco 'vpnetens' encontrado${NC}"
else
    echo -e "${RED}❌ Banco 'vpnetens' não encontrado${NC}"
    exit 1
fi

# 5. Verificar tabelas existentes
echo -e "${BLUE}📋 Verificando tabelas existentes...${NC}"
TABLES=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "\dt" 2>/dev/null | wc -l)

if [ "$TABLES" -eq 0 ]; then
    echo -e "${YELLOW}⚠️ Nenhuma tabela encontrada (banco vazio)${NC}"
    echo -e "${BLUE}💡 Pronto para criar estrutura de tabelas${NC}"
else
    echo -e "${GREEN}✅ $TABLES tabelas encontradas${NC}"
    echo -e "${BLUE}📋 Listando tabelas existentes:${NC}"
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "\dt" 2>/dev/null
fi

echo ""
echo -e "${GREEN}🎉 VERIFICAÇÃO CONCLUÍDA COM SUCESSO!${NC}"
echo -e "${GREEN}====================================${NC}"
echo ""
echo -e "${YELLOW}📋 INFORMAÇÕES DE CONEXÃO CONFIRMADAS:${NC}"
echo -e "   🏠 Host: $DB_HOST"
echo -e "   🔌 Porta: $DB_PORT"
echo -e "   🗄️ Banco: $DB_NAME"
echo -e "   👤 Usuário: $DB_USER"
echo -e "   🔑 Senha: [CONFIGURADA]"
echo -e "   📊 Status: ✅ Conectado e funcionando"
echo ""
echo -e "${CYAN}✨ Pronto para criar as tabelas!${NC}"
