[2025-08-12 18:51:46] === INÍCIO DO MONITORAMENTO VPN ===
[2025-08-12 18:51:46] OpenVPN Server: NÃO ENCONTRADO
[2025-08-12 18:51:46] Docker Service: ATIVO
[2025-08-12 18:51:46] Container API-VPN: RODANDO
[2025-08-12 18:51:46] OpenVPN: Porta 1194/udp FECHADA
[2025-08-12 18:51:46] API VPN: Porta 8080/tcp FECHADA
[2025-08-12 18:51:46] API VPN: RESPONDENDO
[2025-08-12 18:51:46] Interface tun0: INATIVA
[2025-08-12 18:51:46] Clientes conectados: 0
[2025-08-12 18:51:46] Encontrados 13 erros/avisos nos logs recentes
[2025-08-12 18:51:46] Erro: 2025-08-12 18:29:16 Note: --cipher is not set. OpenVPN versions before 2.5 defaulted to BF-CBC as fallback when cipher negotiation failed in this case. If you need this fallback please add '--data-ciphers-fallback BF-CBC' to your configuration and/or add BF-CBC to --data-ciphers.
[2025-08-12 18:51:46] Erro: 2025-08-12 18:29:16 sitnl_send: rtnl: generic error (-17): File exists
[2025-08-12 18:51:46] Erro: 2025-08-12 18:29:16 NOTE: Linux route add command failed because route exists
[2025-08-12 18:51:46] Erro: 2025-08-12 18:29:16 TCP/UDP: Socket bind failed on local address [AF_INET][undef]:1194: Address already in use (errno=98)
[2025-08-12 18:51:46] Erro: 2025-08-12 18:29:16 Exiting due to fatal error
[2025-08-12 18:51:46] Uso de recursos - CPU: 9.1%, Memória: 44.9%, Disco: 23%
[2025-08-12 18:51:46] === FIM DO MONITORAMENTO VPN ===
