2025-08-12 18:26:49 us=654376 Note: '--allow-compression' is not set to 'no', disabling data channel offload.
2025-08-12 18:26:49 us=654429 Consider using the '--compress migrate' option.
2025-08-12 18:26:49 us=654449 WARNING: Using --management on a TCP port WITHOUT passwords is STRONGLY discouraged and considered insecure
2025-08-12 18:26:49 us=654501 Current Parameter Settings:
2025-08-12 18:26:49 us=654514   config = '/etc/openvpn/server.conf'
2025-08-12 18:26:49 us=654525   mode = 1
2025-08-12 18:26:49 us=654542   persist_config = DISABLED
2025-08-12 18:26:49 us=654549   persist_mode = 1
2025-08-12 18:26:49 us=654556   show_ciphers = DISABLED
2025-08-12 18:26:49 us=654562   show_digests = DISABLED
2025-08-12 18:26:49 us=654576   show_engines = DISABLED
2025-08-12 18:26:49 us=654587   genkey = DISABLED
2025-08-12 18:26:49 us=654594   genkey_filename = '[UNDEF]'
2025-08-12 18:26:49 us=654599   key_pass_file = '[UNDEF]'
2025-08-12 18:26:49 us=654605   show_tls_ciphers = DISABLED
2025-08-12 18:26:49 us=654611   connect_retry_max = 0
2025-08-12 18:26:49 us=654616 Connection profiles [0]:
2025-08-12 18:26:49 us=654622   proto = udp
2025-08-12 18:26:49 us=654629   local = '[UNDEF]'
2025-08-12 18:26:49 us=654643   local_port = '1194'
2025-08-12 18:26:49 us=654649   remote = '[UNDEF]'
2025-08-12 18:26:49 us=654656   remote_port = '1194'
2025-08-12 18:26:49 us=654665   remote_float = DISABLED
2025-08-12 18:26:49 us=654675   bind_defined = DISABLED
2025-08-12 18:26:49 us=654682   bind_local = ENABLED
2025-08-12 18:26:49 us=654689   bind_ipv6_only = DISABLED
2025-08-12 18:26:49 us=654696   connect_retry_seconds = 1
2025-08-12 18:26:49 us=654702   connect_timeout = 120
2025-08-12 18:26:49 us=654708   socks_proxy_server = '[UNDEF]'
2025-08-12 18:26:49 us=654714   socks_proxy_port = '[UNDEF]'
2025-08-12 18:26:49 us=654720   tun_mtu = 1500
2025-08-12 18:26:49 us=654725   tun_mtu_defined = ENABLED
2025-08-12 18:26:49 us=654731   link_mtu = 1500
2025-08-12 18:26:49 us=654737   link_mtu_defined = DISABLED
2025-08-12 18:26:49 us=654743   tun_mtu_extra = 0
2025-08-12 18:26:49 us=654749   tun_mtu_extra_defined = DISABLED
2025-08-12 18:26:49 us=654756   tls_mtu = 1250
2025-08-12 18:26:49 us=654765   mtu_discover_type = -1
2025-08-12 18:26:49 us=654776   fragment = 0
2025-08-12 18:26:49 us=654784   mssfix = 1492
2025-08-12 18:26:49 us=654790   mssfix_encap = ENABLED
2025-08-12 18:26:49 us=654796   mssfix_fixed = DISABLED
2025-08-12 18:26:49 us=654803   explicit_exit_notification = 1
2025-08-12 18:26:49 us=654809   tls_auth_file = '[INLINE]'
2025-08-12 18:26:49 us=654815   key_direction = 0
2025-08-12 18:26:49 us=654820   tls_crypt_file = '[UNDEF]'
2025-08-12 18:26:49 us=654825   tls_crypt_v2_file = '[UNDEF]'
2025-08-12 18:26:49 us=654835 Connection profiles END
2025-08-12 18:26:49 us=654841   remote_random = DISABLED
2025-08-12 18:26:49 us=654845   ipchange = '[UNDEF]'
2025-08-12 18:26:49 us=654850   dev = 'tun'
2025-08-12 18:26:49 us=654856   dev_type = '[UNDEF]'
2025-08-12 18:26:49 us=654862   dev_node = '[UNDEF]'
2025-08-12 18:26:49 us=654868   tuntap_options.disable_dco = ENABLED
2025-08-12 18:26:49 us=654875   lladdr = '[UNDEF]'
2025-08-12 18:26:49 us=654881   topology = 3
2025-08-12 18:26:49 us=654886   ifconfig_local = '*********'
2025-08-12 18:26:49 us=654891   ifconfig_remote_netmask = '*************'
2025-08-12 18:26:49 us=654896   ifconfig_noexec = DISABLED
2025-08-12 18:26:49 us=654900   ifconfig_nowarn = DISABLED
2025-08-12 18:26:49 us=654905   ifconfig_ipv6_local = '[UNDEF]'
2025-08-12 18:26:49 us=654910   ifconfig_ipv6_netbits = 0
2025-08-12 18:26:49 us=654915   ifconfig_ipv6_remote = '[UNDEF]'
2025-08-12 18:26:49 us=654921   shaper = 0
2025-08-12 18:26:49 us=654927   mtu_test = 0
2025-08-12 18:26:49 us=654933   mlock = DISABLED
2025-08-12 18:26:49 us=654939   keepalive_ping = 10
2025-08-12 18:26:49 us=654944   keepalive_timeout = 120
2025-08-12 18:26:49 us=654949   inactivity_timeout = 0
2025-08-12 18:26:49 us=654957   session_timeout = 0
2025-08-12 18:26:49 us=654968   inactivity_minimum_bytes = 0
2025-08-12 18:26:49 us=654975   ping_send_timeout = 10
2025-08-12 18:26:49 us=654991   ping_rec_timeout = 240
2025-08-12 18:26:49 us=655003   ping_rec_timeout_action = 2
2025-08-12 18:26:49 us=655009   ping_timer_remote = DISABLED
2025-08-12 18:26:49 us=655014   remap_sigusr1 = 0
2025-08-12 18:26:49 us=655020   persist_tun = ENABLED
2025-08-12 18:26:49 us=655026   persist_local_ip = DISABLED
2025-08-12 18:26:49 us=655032   persist_remote_ip = DISABLED
2025-08-12 18:26:49 us=655038   persist_key = ENABLED
2025-08-12 18:26:49 us=655044   passtos = DISABLED
2025-08-12 18:26:49 us=655049   resolve_retry_seconds = 1000000000
2025-08-12 18:26:49 us=655054   resolve_in_advance = DISABLED
2025-08-12 18:26:49 us=655059   username = 'nobody'
2025-08-12 18:26:49 us=655063   groupname = 'nogroup'
2025-08-12 18:26:49 us=655068   chroot_dir = '[UNDEF]'
2025-08-12 18:26:49 us=655073   cd_dir = '[UNDEF]'
2025-08-12 18:26:49 us=655078   writepid = '[UNDEF]'
2025-08-12 18:26:49 us=655083   up_script = '[UNDEF]'
2025-08-12 18:26:49 us=655088   down_script = '[UNDEF]'
2025-08-12 18:26:49 us=655092   down_pre = DISABLED
2025-08-12 18:26:49 us=655097   up_restart = DISABLED
2025-08-12 18:26:49 us=655102   up_delay = DISABLED
2025-08-12 18:26:49 us=655107   daemon = DISABLED
2025-08-12 18:26:49 us=655112   log = ENABLED
2025-08-12 18:26:49 us=655117   suppress_timestamps = DISABLED
2025-08-12 18:26:49 us=655122   machine_readable_output = DISABLED
2025-08-12 18:26:49 us=655127   nice = 0
2025-08-12 18:26:49 us=655131   verbosity = 4
2025-08-12 18:26:49 us=655137   mute = 0
2025-08-12 18:26:49 us=655141   gremlin = 0
2025-08-12 18:26:49 us=655147   status_file = '/root/VPN/OpenVPN/logs/openvpn-status.log'
2025-08-12 18:26:49 us=655155   status_file_version = 1
2025-08-12 18:26:49 us=655183   status_file_update_freq = 60
2025-08-12 18:26:49 us=655194   occ = ENABLED
2025-08-12 18:26:49 us=655200   rcvbuf = 0
2025-08-12 18:26:49 us=655205   sndbuf = 0
2025-08-12 18:26:49 us=655211   mark = 0
2025-08-12 18:26:49 us=655217   sockflags = 0
2025-08-12 18:26:49 us=655224   fast_io = ENABLED
2025-08-12 18:26:49 us=655230   comp.alg = 2
2025-08-12 18:26:49 us=655235   comp.flags = 1
2025-08-12 18:26:49 us=655241   route_script = '[UNDEF]'
2025-08-12 18:26:49 us=655246   route_default_gateway = '*********'
2025-08-12 18:26:49 us=655251   route_default_metric = 0
2025-08-12 18:26:49 us=655256   route_noexec = DISABLED
2025-08-12 18:26:49 us=655261   route_delay = 0
2025-08-12 18:26:49 us=655266   route_delay_window = 30
2025-08-12 18:26:49 us=655272   route_delay_defined = DISABLED
2025-08-12 18:26:49 us=655281   route_nopull = DISABLED
2025-08-12 18:26:49 us=655291   route_gateway_via_dhcp = DISABLED
2025-08-12 18:26:49 us=655298   allow_pull_fqdn = DISABLED
2025-08-12 18:26:49 us=655305   management_addr = 'localhost'
2025-08-12 18:26:49 us=655310   management_port = '7505'
2025-08-12 18:26:49 us=655316   management_user_pass = '[UNDEF]'
2025-08-12 18:26:49 us=655322   management_log_history_cache = 250
2025-08-12 18:26:49 us=655328   management_echo_buffer_size = 100
2025-08-12 18:26:49 us=655341   management_client_user = '[UNDEF]'
2025-08-12 18:26:49 us=655347   management_client_group = '[UNDEF]'
2025-08-12 18:26:49 us=655352   management_flags = 0
2025-08-12 18:26:49 us=655359   shared_secret_file = '[UNDEF]'
2025-08-12 18:26:49 us=655371   key_direction = 0
2025-08-12 18:26:49 us=655378   ciphername = 'AES-256-GCM'
2025-08-12 18:26:49 us=655385   ncp_ciphers = 'AES-256-GCM:AES-128-GCM:CHACHA20-POLY1305'
2025-08-12 18:26:49 us=655391   authname = 'SHA256'
2025-08-12 18:26:49 us=655397   engine = DISABLED
2025-08-12 18:26:49 us=655402   replay = ENABLED
2025-08-12 18:26:49 us=655408   mute_replay_warnings = DISABLED
2025-08-12 18:26:49 us=655414   replay_window = 64
2025-08-12 18:26:49 us=655420   replay_time = 15
2025-08-12 18:26:49 us=655426   packet_id_file = '[UNDEF]'
2025-08-12 18:26:49 us=655432   test_crypto = DISABLED
2025-08-12 18:26:49 us=655437   tls_server = ENABLED
2025-08-12 18:26:49 us=655443   tls_client = DISABLED
2025-08-12 18:26:49 us=655449   ca_file = '/root/VPN/OpenVPN/easy-rsa/pki/ca.crt'
2025-08-12 18:26:49 us=655462   ca_path = '[UNDEF]'
2025-08-12 18:26:49 us=655467   dh_file = '/root/VPN/OpenVPN/easy-rsa/pki/dh.pem'
2025-08-12 18:26:49 us=655472   cert_file = '/root/VPN/OpenVPN/easy-rsa/pki/issued/server.crt'
2025-08-12 18:26:49 us=655477   extra_certs_file = '[UNDEF]'
2025-08-12 18:26:49 us=655483   priv_key_file = '/root/VPN/OpenVPN/easy-rsa/pki/private/server.key'
2025-08-12 18:26:49 us=655488   pkcs12_file = '[UNDEF]'
2025-08-12 18:26:49 us=655493   cipher_list = '[UNDEF]'
2025-08-12 18:26:49 us=655498   cipher_list_tls13 = '[UNDEF]'
2025-08-12 18:26:49 us=655503   tls_cert_profile = '[UNDEF]'
2025-08-12 18:26:49 us=655508   tls_verify = '[UNDEF]'
2025-08-12 18:26:49 us=655513   tls_export_peer_cert_dir = '[UNDEF]'
2025-08-12 18:26:49 us=655518   verify_x509_type = 0
2025-08-12 18:26:49 us=655523   verify_x509_name = '[UNDEF]'
2025-08-12 18:26:49 us=655528   crl_file = '[UNDEF]'
2025-08-12 18:26:49 us=655533   ns_cert_type = 0
2025-08-12 18:26:49 us=655538   remote_cert_ku[i] = 0
2025-08-12 18:26:49 us=655543   remote_cert_ku[i] = 0
2025-08-12 18:26:49 us=655547   remote_cert_ku[i] = 0
2025-08-12 18:26:49 us=655552   remote_cert_ku[i] = 0
2025-08-12 18:26:49 us=655557   remote_cert_ku[i] = 0
2025-08-12 18:26:49 us=655563   remote_cert_ku[i] = 0
2025-08-12 18:26:49 us=655569   remote_cert_ku[i] = 0
2025-08-12 18:26:49 us=655575   remote_cert_ku[i] = 0
2025-08-12 18:26:49 us=655581   remote_cert_ku[i] = 0
2025-08-12 18:26:49 us=655587   remote_cert_ku[i] = 0
2025-08-12 18:26:49 us=655592   remote_cert_ku[i] = 0
2025-08-12 18:26:49 us=655597   remote_cert_ku[i] = 0
2025-08-12 18:26:49 us=655602   remote_cert_ku[i] = 0
2025-08-12 18:26:49 us=655606   remote_cert_ku[i] = 0
2025-08-12 18:26:49 us=655611   remote_cert_ku[i] = 0
2025-08-12 18:26:49 us=655616   remote_cert_ku[i] = 0
2025-08-12 18:26:49 us=655621   remote_cert_eku = '[UNDEF]'
2025-08-12 18:26:49 us=655626   ssl_flags = 192
2025-08-12 18:26:49 us=655640   tls_timeout = 2
2025-08-12 18:26:49 us=655645   renegotiate_bytes = -1
2025-08-12 18:26:49 us=655650   renegotiate_packets = 0
2025-08-12 18:26:49 us=655655   renegotiate_seconds = 3600
2025-08-12 18:26:49 us=655660   handshake_window = 60
2025-08-12 18:26:49 us=655665   transition_window = 3600
2025-08-12 18:26:49 us=655670   single_session = DISABLED
2025-08-12 18:26:49 us=655675   push_peer_info = DISABLED
2025-08-12 18:26:49 us=655680   tls_exit = DISABLED
2025-08-12 18:26:49 us=655685   tls_crypt_v2_metadata = '[UNDEF]'
2025-08-12 18:26:49 us=655690   pkcs11_protected_authentication = DISABLED
2025-08-12 18:26:49 us=655695   pkcs11_protected_authentication = DISABLED
2025-08-12 18:26:49 us=655700   pkcs11_protected_authentication = DISABLED
2025-08-12 18:26:49 us=655705   pkcs11_protected_authentication = DISABLED
2025-08-12 18:26:49 us=655710   pkcs11_protected_authentication = DISABLED
2025-08-12 18:26:49 us=655715   pkcs11_protected_authentication = DISABLED
2025-08-12 18:26:49 us=655719   pkcs11_protected_authentication = DISABLED
2025-08-12 18:26:49 us=655724   pkcs11_protected_authentication = DISABLED
2025-08-12 18:26:49 us=655729   pkcs11_protected_authentication = DISABLED
2025-08-12 18:26:49 us=655735   pkcs11_protected_authentication = DISABLED
2025-08-12 18:26:49 us=655740   pkcs11_protected_authentication = DISABLED
2025-08-12 18:26:49 us=655745   pkcs11_protected_authentication = DISABLED
2025-08-12 18:26:49 us=655750   pkcs11_protected_authentication = DISABLED
2025-08-12 18:26:49 us=655754   pkcs11_protected_authentication = DISABLED
2025-08-12 18:26:49 us=655759   pkcs11_protected_authentication = DISABLED
2025-08-12 18:26:49 us=655764   pkcs11_protected_authentication = DISABLED
2025-08-12 18:26:49 us=655769   pkcs11_private_mode = 00000000
2025-08-12 18:26:49 us=655775   pkcs11_private_mode = 00000000
2025-08-12 18:26:49 us=655780   pkcs11_private_mode = 00000000
2025-08-12 18:26:49 us=655785   pkcs11_private_mode = 00000000
2025-08-12 18:26:49 us=655790   pkcs11_private_mode = 00000000
2025-08-12 18:26:49 us=655795   pkcs11_private_mode = 00000000
2025-08-12 18:26:49 us=655804   pkcs11_private_mode = 00000000
2025-08-12 18:26:49 us=655809   pkcs11_private_mode = 00000000
2025-08-12 18:26:49 us=655814   pkcs11_private_mode = 00000000
2025-08-12 18:26:49 us=655819   pkcs11_private_mode = 00000000
2025-08-12 18:26:49 us=655824   pkcs11_private_mode = 00000000
2025-08-12 18:26:49 us=655828   pkcs11_private_mode = 00000000
2025-08-12 18:26:49 us=655833   pkcs11_private_mode = 00000000
2025-08-12 18:26:49 us=655838   pkcs11_private_mode = 00000000
2025-08-12 18:26:49 us=655843   pkcs11_private_mode = 00000000
2025-08-12 18:26:49 us=655848   pkcs11_private_mode = 00000000
2025-08-12 18:26:49 us=655853   pkcs11_cert_private = DISABLED
2025-08-12 18:26:49 us=655858   pkcs11_cert_private = DISABLED
2025-08-12 18:26:49 us=655864   pkcs11_cert_private = DISABLED
2025-08-12 18:26:49 us=655877   pkcs11_cert_private = DISABLED
2025-08-12 18:26:49 us=655885   pkcs11_cert_private = DISABLED
2025-08-12 18:26:49 us=655892   pkcs11_cert_private = DISABLED
2025-08-12 18:26:49 us=655898   pkcs11_cert_private = DISABLED
2025-08-12 18:26:49 us=655904   pkcs11_cert_private = DISABLED
2025-08-12 18:26:49 us=655910   pkcs11_cert_private = DISABLED
2025-08-12 18:26:49 us=655916   pkcs11_cert_private = DISABLED
2025-08-12 18:26:49 us=655921   pkcs11_cert_private = DISABLED
2025-08-12 18:26:49 us=655926   pkcs11_cert_private = DISABLED
2025-08-12 18:26:49 us=655931   pkcs11_cert_private = DISABLED
2025-08-12 18:26:49 us=655936   pkcs11_cert_private = DISABLED
2025-08-12 18:26:49 us=655941   pkcs11_cert_private = DISABLED
2025-08-12 18:26:49 us=655946   pkcs11_cert_private = DISABLED
2025-08-12 18:26:49 us=655951   pkcs11_pin_cache_period = -1
2025-08-12 18:26:49 us=655956   pkcs11_id = '[UNDEF]'
2025-08-12 18:26:49 us=655961   pkcs11_id_management = DISABLED
2025-08-12 18:26:49 us=655968   server_network = *********
2025-08-12 18:26:49 us=655973   server_netmask = *************
2025-08-12 18:26:49 us=655978   server_network_ipv6 = ::
2025-08-12 18:26:49 us=655985   server_netbits_ipv6 = 0
2025-08-12 18:26:49 us=655996   server_bridge_ip = 0.0.0.0
2025-08-12 18:26:49 us=656005   server_bridge_netmask = 0.0.0.0
2025-08-12 18:26:49 us=656012   server_bridge_pool_start = 0.0.0.0
2025-08-12 18:26:49 us=656019   server_bridge_pool_end = 0.0.0.0
2025-08-12 18:26:49 us=656026   push_entry = 'redirect-gateway def1 bypass-dhcp'
2025-08-12 18:26:49 us=656032   push_entry = 'dhcp-option DNS *******'
2025-08-12 18:26:49 us=656038   push_entry = 'dhcp-option DNS *******'
2025-08-12 18:26:49 us=656044   push_entry = 'route-gateway *********'
2025-08-12 18:26:49 us=656049   push_entry = 'topology subnet'
2025-08-12 18:26:49 us=656055   push_entry = 'ping 10'
2025-08-12 18:26:49 us=656061   push_entry = 'ping-restart 120'
2025-08-12 18:26:49 us=656067   ifconfig_pool_defined = ENABLED
2025-08-12 18:26:49 us=656073   ifconfig_pool_start = *********
2025-08-12 18:26:49 us=656080   ifconfig_pool_end = *********54
2025-08-12 18:26:49 us=656087   ifconfig_pool_netmask = *************
2025-08-12 18:26:49 us=656094   ifconfig_pool_persist_filename = '/root/VPN/OpenVPN/logs/ipp.txt'
2025-08-12 18:26:49 us=656105   ifconfig_pool_persist_refresh_freq = 600
2025-08-12 18:26:49 us=656115   ifconfig_ipv6_pool_defined = DISABLED
2025-08-12 18:26:49 us=656122   ifconfig_ipv6_pool_base = ::
2025-08-12 18:26:49 us=656128   ifconfig_ipv6_pool_netbits = 0
2025-08-12 18:26:49 us=656134   n_bcast_buf = 256
2025-08-12 18:26:49 us=656141   tcp_queue_limit = 64
2025-08-12 18:26:49 us=656148   real_hash_size = 256
2025-08-12 18:26:49 us=656153   virtual_hash_size = 256
2025-08-12 18:26:49 us=656159   client_connect_script = '[UNDEF]'
2025-08-12 18:26:49 us=656163   learn_address_script = '[UNDEF]'
2025-08-12 18:26:49 us=656192   client_disconnect_script = '[UNDEF]'
2025-08-12 18:26:49 us=656197   client_crresponse_script = '[UNDEF]'
2025-08-12 18:26:49 us=656202   client_config_dir = '[UNDEF]'
2025-08-12 18:26:49 us=656207   ccd_exclusive = DISABLED
2025-08-12 18:26:49 us=656212   tmp_dir = '/tmp'
2025-08-12 18:26:49 us=656217   push_ifconfig_defined = DISABLED
2025-08-12 18:26:49 us=656229   push_ifconfig_local = 0.0.0.0
2025-08-12 18:26:49 us=656238   push_ifconfig_remote_netmask = 0.0.0.0
2025-08-12 18:26:49 us=656249   push_ifconfig_ipv6_defined = DISABLED
2025-08-12 18:26:49 us=656256   push_ifconfig_ipv6_local = ::/0
2025-08-12 18:26:49 us=656262   push_ifconfig_ipv6_remote = ::
2025-08-12 18:26:49 us=656305   enable_c2c = ENABLED
2025-08-12 18:26:49 us=656311   duplicate_cn = ENABLED
2025-08-12 18:26:49 us=656318   cf_max = 0
2025-08-12 18:26:49 us=656325   cf_per = 0
2025-08-12 18:26:49 us=656331   cf_initial_max = 100
2025-08-12 18:26:49 us=656337   cf_initial_per = 10
2025-08-12 18:26:49 us=656343   max_clients = 1024
2025-08-12 18:26:49 us=656348   max_routes_per_client = 256
2025-08-12 18:26:49 us=656353   auth_user_pass_verify_script = '[UNDEF]'
2025-08-12 18:26:49 us=656358   auth_user_pass_verify_script_via_file = DISABLED
2025-08-12 18:26:49 us=656362   auth_token_generate = DISABLED
2025-08-12 18:26:49 us=656367   force_key_material_export = DISABLED
2025-08-12 18:26:49 us=656372   auth_token_lifetime = 0
2025-08-12 18:26:49 us=656377   auth_token_secret_file = '[UNDEF]'
2025-08-12 18:26:49 us=656382   port_share_host = '[UNDEF]'
2025-08-12 18:26:49 us=656387   port_share_port = '[UNDEF]'
2025-08-12 18:26:49 us=656393   vlan_tagging = DISABLED
2025-08-12 18:26:49 us=656398   vlan_accept = all
2025-08-12 18:26:49 us=656403   vlan_pvid = 1
2025-08-12 18:26:49 us=656408   client = DISABLED
2025-08-12 18:26:49 us=656412   pull = DISABLED
2025-08-12 18:26:49 us=656417   auth_user_pass_file = '[UNDEF]'
2025-08-12 18:26:49 us=656423 OpenVPN 2.6.14 x86_64-pc-linux-gnu [SSL (OpenSSL)] [LZO] [LZ4] [EPOLL] [PKCS11] [MH/PKTINFO] [AEAD] [DCO]
2025-08-12 18:26:49 us=656437 library versions: OpenSSL 3.0.13 30 Jan 2024, LZO 2.10
2025-08-12 18:26:49 us=656452 DCO version: N/A
2025-08-12 18:26:49 us=656672 setsockopt(IPV6_V6ONLY=0)
2025-08-12 18:26:49 us=656703 MANAGEMENT: TCP Socket listening on [AF_INET6]::1:7505
2025-08-12 18:26:49 us=656788 WARNING: --ifconfig-pool-persist will not work with --duplicate-cn
2025-08-12 18:26:49 us=656795 net_route_v4_best_gw query: dst 0.0.0.0
2025-08-12 18:26:49 us=656861 net_route_v4_best_gw result: via ************ dev eth0
2025-08-12 18:26:49 us=657933 Diffie-Hellman initialized with 2048 bit key
2025-08-12 18:26:49 us=658985 Outgoing Control Channel Authentication: Using 256 bit message hash 'SHA256' for HMAC authentication
2025-08-12 18:26:49 us=659015 Incoming Control Channel Authentication: Using 256 bit message hash 'SHA256' for HMAC authentication
2025-08-12 18:26:49 us=659044 TLS-Auth MTU parms [ mss_fix:0 max_frag:0 tun_mtu:1250 tun_max_mtu:0 headroom:126 payload:1600 tailroom:126 ET:0 ]
2025-08-12 18:26:49 us=661225 TUN/TAP device tun0 opened
2025-08-12 18:26:49 us=661247 do_ifconfig, ipv4=1, ipv6=0
2025-08-12 18:26:49 us=661276 net_iface_mtu_set: mtu 1500 for tun0
2025-08-12 18:26:49 us=661313 net_iface_up: set tun0 up
2025-08-12 18:26:49 us=661980 net_addr_v4_add: *********/24 dev tun0
2025-08-12 18:26:49 us=662076 Data Channel MTU parms [ mss_fix:0 max_frag:0 tun_mtu:1500 tun_max_mtu:1600 headroom:136 payload:1768 tailroom:562 ET:0 ]
2025-08-12 18:26:49 us=662192 Could not determine IPv4/IPv6 protocol. Using AF_INET
2025-08-12 18:26:49 us=662214 Socket Buffers: R=[212992->212992] S=[212992->212992]
2025-08-12 18:26:49 us=662226 UDPv4 link local (bound): [AF_INET][undef]:1194
2025-08-12 18:26:49 us=662232 UDPv4 link remote: [AF_UNSPEC]
2025-08-12 18:26:49 us=662370 UID set to nobody
2025-08-12 18:26:49 us=662378 GID set to nogroup
2025-08-12 18:26:49 us=662383 Capabilities retained: CAP_NET_ADMIN
2025-08-12 18:26:49 us=662392 MULTI: multi_init called, r=256 v=256
2025-08-12 18:26:49 us=662412 IFCONFIG POOL IPv4: base=********* size=253
2025-08-12 18:26:49 us=662433 IFCONFIG POOL LIST
2025-08-12 18:26:49 us=662456 Initialization Sequence Completed
2025-08-12 18:27:10 us=242362 event_wait : Interrupted system call (fd=-1,code=4)
2025-08-12 18:27:12 us=244427 TCP/UDP: Closing socket
2025-08-12 18:27:12 us=244511 Closing TUN/TAP interface
2025-08-12 18:27:12 us=244537 net_addr_v4_del: ********* dev tun0
2025-08-12 18:27:12 us=253408 SIGHUP[hard,] received, process restarting
2025-08-12 18:27:12 us=253722 WARNING: Compression for receiving enabled. Compression has been used in the past to break encryption. Sent packets are not compressed unless "allow-compression yes" is also set.
2025-08-12 18:27:12 us=253953 Cannot pre-load keyfile (/root/VPN/OpenVPN/easy-rsa/pki/ta.key)
2025-08-12 18:27:12 us=253968 Exiting due to fatal error
2025-08-12 18:28:52 WARNING: --topology net30 support for server configs with IPv4 pools will be removed in a future release. Please migrate to --topology subnet as soon as possible.
2025-08-12 18:28:52 Note: --cipher is not set. OpenVPN versions before 2.5 defaulted to BF-CBC as fallback when cipher negotiation failed in this case. If you need this fallback please add '--data-ciphers-fallback BF-CBC' to your configuration and/or add BF-CBC to --data-ciphers.
2025-08-12 18:28:52 OpenVPN 2.6.14 x86_64-pc-linux-gnu [SSL (OpenSSL)] [LZO] [LZ4] [EPOLL] [PKCS11] [MH/PKTINFO] [AEAD] [DCO]
2025-08-12 18:28:52 library versions: OpenSSL 3.0.13 30 Jan 2024, LZO 2.10
2025-08-12 18:28:52 DCO version: N/A
2025-08-12 18:28:52 TUN/TAP device tun0 opened
2025-08-12 18:28:52 net_iface_mtu_set: mtu 1500 for tun0
2025-08-12 18:28:52 net_iface_up: set tun0 up
2025-08-12 18:28:52 net_addr_ptp_v4_add: ********* peer ********* dev tun0
2025-08-12 18:28:52 Could not determine IPv4/IPv6 protocol. Using AF_INET
2025-08-12 18:28:52 UDPv4 link local (bound): [AF_INET][undef]:1194
2025-08-12 18:28:52 UDPv4 link remote: [AF_UNSPEC]
2025-08-12 18:28:52 Initialization Sequence Completed
2025-08-12 18:29:08 event_wait : Interrupted system call (fd=-1,code=4)
2025-08-12 18:29:08 net_addr_ptp_v4_del: ********* dev tun0
2025-08-12 18:29:08 SIGHUP[hard,] received, process restarting
2025-08-12 18:29:08 WARNING: --topology net30 support for server configs with IPv4 pools will be removed in a future release. Please migrate to --topology subnet as soon as possible.
2025-08-12 18:29:08 Note: --cipher is not set. OpenVPN versions before 2.5 defaulted to BF-CBC as fallback when cipher negotiation failed in this case. If you need this fallback please add '--data-ciphers-fallback BF-CBC' to your configuration and/or add BF-CBC to --data-ciphers.
2025-08-12 18:29:08 OpenVPN 2.6.14 x86_64-pc-linux-gnu [SSL (OpenSSL)] [LZO] [LZ4] [EPOLL] [PKCS11] [MH/PKTINFO] [AEAD] [DCO]
2025-08-12 18:29:08 library versions: OpenSSL 3.0.13 30 Jan 2024, LZO 2.10
2025-08-12 18:29:08 DCO version: N/A
2025-08-12 18:29:09 TUN/TAP device tun0 opened
2025-08-12 18:29:09 net_iface_mtu_set: mtu 1500 for tun0
2025-08-12 18:29:09 net_iface_up: set tun0 up
2025-08-12 18:29:09 net_addr_ptp_v4_add: ********* peer ********* dev tun0
2025-08-12 18:29:09 Could not determine IPv4/IPv6 protocol. Using AF_INET
2025-08-12 18:29:09 UDPv4 link local (bound): [AF_INET][undef]:1194
2025-08-12 18:29:09 UDPv4 link remote: [AF_UNSPEC]
2025-08-12 18:29:09 Initialization Sequence Completed
2025-08-12 18:29:16 WARNING: --topology net30 support for server configs with IPv4 pools will be removed in a future release. Please migrate to --topology subnet as soon as possible.
2025-08-12 18:29:16 Note: --cipher is not set. OpenVPN versions before 2.5 defaulted to BF-CBC as fallback when cipher negotiation failed in this case. If you need this fallback please add '--data-ciphers-fallback BF-CBC' to your configuration and/or add BF-CBC to --data-ciphers.
2025-08-12 18:29:16 Note: NOT using '--topology subnet' disables data channel offload.
2025-08-12 18:29:16 OpenVPN 2.6.14 x86_64-pc-linux-gnu [SSL (OpenSSL)] [LZO] [LZ4] [EPOLL] [PKCS11] [MH/PKTINFO] [AEAD] [DCO]
2025-08-12 18:29:16 library versions: OpenSSL 3.0.13 30 Jan 2024, LZO 2.10
2025-08-12 18:29:16 DCO version: N/A
2025-08-12 18:29:16 net_route_v4_best_gw query: dst 0.0.0.0
2025-08-12 18:29:16 net_route_v4_best_gw result: via ************ dev eth0
2025-08-12 18:29:16 Diffie-Hellman initialized with 2048 bit key
2025-08-12 18:29:16 net_route_v4_best_gw query: dst 0.0.0.0
2025-08-12 18:29:16 net_route_v4_best_gw result: via ************ dev eth0
2025-08-12 18:29:16 ROUTE_GATEWAY ************/************* IFACE=eth0 HWADDR=be:e8:d4:b1:b5:3d
2025-08-12 18:29:16 TUN/TAP device tun1 opened
2025-08-12 18:29:16 net_iface_mtu_set: mtu 1500 for tun1
2025-08-12 18:29:16 net_iface_up: set tun1 up
2025-08-12 18:29:16 net_addr_ptp_v4_add: ********* peer ********* dev tun1
2025-08-12 18:29:16 net_route_v4_add: *********/24 via ********* dev [NULL] table 0 metric -1
2025-08-12 18:29:16 sitnl_send: rtnl: generic error (-17): File exists
2025-08-12 18:29:16 NOTE: Linux route add command failed because route exists
2025-08-12 18:29:16 Could not determine IPv4/IPv6 protocol. Using AF_INET
2025-08-12 18:29:16 Socket Buffers: R=[212992->212992] S=[212992->212992]
2025-08-12 18:29:16 TCP/UDP: Socket bind failed on local address [AF_INET][undef]:1194: Address already in use (errno=98)
2025-08-12 18:29:16 Exiting due to fatal error
2025-08-12 18:29:16 Closing TUN/TAP interface
2025-08-12 18:29:16 net_addr_ptp_v4_del: ********* dev tun1
2025-08-12 18:33:52 event_wait : Interrupted system call (fd=-1,code=4)
2025-08-12 18:33:52 net_addr_ptp_v4_del: ********* dev tun0
2025-08-12 18:33:52 SIGTERM[hard,] received, process exiting
2025-08-14 11:39:28 Note: Kernel support for ovpn-dco missing, disabling data channel offload.
2025-08-14 11:39:28 WARNING: Using --management on a TCP port WITHOUT passwords is STRONGLY discouraged and considered insecure
2025-08-14 11:39:28 OpenVPN 2.6.14 x86_64-pc-linux-gnu [SSL (OpenSSL)] [LZO] [LZ4] [EPOLL] [PKCS11] [MH/PKTINFO] [AEAD] [DCO]
2025-08-14 11:39:28 library versions: OpenSSL 3.0.13 30 Jan 2024, LZO 2.10
2025-08-14 11:39:28 DCO version: N/A
2025-08-14 11:39:28 setsockopt(IPV6_V6ONLY=0)
2025-08-14 11:39:28 MANAGEMENT: TCP Socket listening on [AF_INET6]::1:7505
2025-08-14 11:39:28 WARNING: --ifconfig-pool-persist will not work with --duplicate-cn
2025-08-14 11:39:28 net_route_v4_best_gw query: dst 0.0.0.0
2025-08-14 11:39:28 net_route_v4_best_gw result: via ************ dev eth0
2025-08-14 11:39:28 Diffie-Hellman initialized with 2048 bit key
2025-08-14 11:39:28 TUN/TAP device tun0 opened
2025-08-14 11:39:28 net_iface_mtu_set: mtu 1500 for tun0
2025-08-14 11:39:28 net_iface_up: set tun0 up
2025-08-14 11:39:28 net_addr_v4_add: *********/24 dev tun0
2025-08-14 11:39:28 Could not determine IPv4/IPv6 protocol. Using AF_INET
2025-08-14 11:39:28 Socket Buffers: R=[212992->212992] S=[212992->212992]
2025-08-14 11:39:28 UDPv4 link local (bound): [AF_INET][undef]:1194
2025-08-14 11:39:28 UDPv4 link remote: [AF_UNSPEC]
2025-08-14 11:39:28 MULTI: multi_init called, r=256 v=256
2025-08-14 11:39:28 IFCONFIG POOL IPv4: base=********* size=253
2025-08-14 11:39:28 IFCONFIG POOL LIST
2025-08-14 11:39:28 Initialization Sequence Completed
2025-08-14 11:39:58 event_wait : Interrupted system call (fd=-1,code=4)
2025-08-14 11:40:00 Closing TUN/TAP interface
2025-08-14 11:40:00 net_addr_v4_del: ********* dev tun0
2025-08-14 11:40:00 SIGINT[hard,] received, process exiting
2025-08-14 11:40:45 Note: Kernel support for ovpn-dco missing, disabling data channel offload.
2025-08-14 11:40:45 WARNING: Using --management on a TCP port WITHOUT passwords is STRONGLY discouraged and considered insecure
2025-08-14 11:40:45 OpenVPN 2.6.14 x86_64-pc-linux-gnu [SSL (OpenSSL)] [LZO] [LZ4] [EPOLL] [PKCS11] [MH/PKTINFO] [AEAD] [DCO]
2025-08-14 11:40:45 library versions: OpenSSL 3.0.13 30 Jan 2024, LZO 2.10
2025-08-14 11:40:45 DCO version: N/A
2025-08-14 11:40:45 setsockopt(IPV6_V6ONLY=0)
2025-08-14 11:40:45 MANAGEMENT: TCP Socket listening on [AF_INET6]::1:7505
2025-08-14 11:40:45 WARNING: --ifconfig-pool-persist will not work with --duplicate-cn
2025-08-14 11:40:45 net_route_v4_best_gw query: dst 0.0.0.0
2025-08-14 11:40:45 net_route_v4_best_gw result: via ************ dev eth0
2025-08-14 11:40:45 Diffie-Hellman initialized with 2048 bit key
2025-08-14 11:40:45 TUN/TAP device tun0 opened
2025-08-14 11:40:45 net_iface_mtu_set: mtu 1500 for tun0
2025-08-14 11:40:45 net_iface_up: set tun0 up
2025-08-14 11:40:45 net_addr_v4_add: *********/24 dev tun0
2025-08-14 11:40:45 Could not determine IPv4/IPv6 protocol. Using AF_INET
2025-08-14 11:40:45 Socket Buffers: R=[212992->212992] S=[212992->212992]
2025-08-14 11:40:45 UDPv4 link local (bound): [AF_INET][undef]:1194
2025-08-14 11:40:45 UDPv4 link remote: [AF_UNSPEC]
2025-08-14 11:40:45 MULTI: multi_init called, r=256 v=256
2025-08-14 11:40:45 IFCONFIG POOL IPv4: base=********* size=253
2025-08-14 11:40:45 IFCONFIG POOL LIST
2025-08-14 11:40:45 Initialization Sequence Completed
2025-08-14 11:42:19 event_wait : Interrupted system call (fd=-1,code=4)
2025-08-14 11:42:21 Closing TUN/TAP interface
2025-08-14 11:42:21 net_addr_v4_del: ********* dev tun0
2025-08-14 11:42:21 SIGTERM[hard,] received, process exiting
2025-08-14 12:38:55 us=944784 Note: Kernel support for ovpn-dco missing, disabling data channel offload.
2025-08-14 12:38:55 us=945125 WARNING: Using --management on a TCP port WITHOUT passwords is STRONGLY discouraged and considered insecure
2025-08-14 12:38:55 us=945215 Current Parameter Settings:
2025-08-14 12:38:55 us=945234   config = '/etc/openvpn/server.conf'
2025-08-14 12:38:55 us=945244   mode = 1
2025-08-14 12:38:55 us=945253   persist_config = DISABLED
2025-08-14 12:38:55 us=945261   persist_mode = 1
2025-08-14 12:38:55 us=945269   show_ciphers = DISABLED
2025-08-14 12:38:55 us=945282   show_digests = DISABLED
2025-08-14 12:38:55 us=945291   show_engines = DISABLED
2025-08-14 12:38:55 us=945299   genkey = DISABLED
2025-08-14 12:38:55 us=945305   genkey_filename = '[UNDEF]'
2025-08-14 12:38:55 us=945312   key_pass_file = '[UNDEF]'
2025-08-14 12:38:55 us=945318   show_tls_ciphers = DISABLED
2025-08-14 12:38:55 us=945324   connect_retry_max = 0
2025-08-14 12:38:55 us=945330 Connection profiles [0]:
2025-08-14 12:38:55 us=945338   proto = udp
2025-08-14 12:38:55 us=945344   local = '[UNDEF]'
2025-08-14 12:38:55 us=945350   local_port = '1194'
2025-08-14 12:38:55 us=945356   remote = '[UNDEF]'
2025-08-14 12:38:55 us=945362   remote_port = '1194'
2025-08-14 12:38:55 us=945369   remote_float = DISABLED
2025-08-14 12:38:55 us=945375   bind_defined = DISABLED
2025-08-14 12:38:55 us=945381   bind_local = ENABLED
2025-08-14 12:38:55 us=945387   bind_ipv6_only = DISABLED
2025-08-14 12:38:55 us=945394   connect_retry_seconds = 1
2025-08-14 12:38:55 us=945400   connect_timeout = 120
2025-08-14 12:38:55 us=945407   socks_proxy_server = '[UNDEF]'
2025-08-14 12:38:55 us=945413   socks_proxy_port = '[UNDEF]'
2025-08-14 12:38:55 us=945419   tun_mtu = 1500
2025-08-14 12:38:55 us=945426   tun_mtu_defined = ENABLED
2025-08-14 12:38:55 us=945432   link_mtu = 1500
2025-08-14 12:38:55 us=945438   link_mtu_defined = DISABLED
2025-08-14 12:38:55 us=945444   tun_mtu_extra = 0
2025-08-14 12:38:55 us=945451   tun_mtu_extra_defined = DISABLED
2025-08-14 12:38:55 us=945457   tls_mtu = 1250
2025-08-14 12:38:55 us=945463   mtu_discover_type = -1
2025-08-14 12:38:55 us=945470   fragment = 0
2025-08-14 12:38:55 us=945476   mssfix = 1492
2025-08-14 12:38:55 us=945482   mssfix_encap = ENABLED
2025-08-14 12:38:55 us=945488   mssfix_fixed = DISABLED
2025-08-14 12:38:55 us=945495   explicit_exit_notification = 1
2025-08-14 12:38:55 us=945501   tls_auth_file = '[INLINE]'
2025-08-14 12:38:55 us=945507   key_direction = 0
2025-08-14 12:38:55 us=945513   tls_crypt_file = '[UNDEF]'
2025-08-14 12:38:55 us=945520   tls_crypt_v2_file = '[UNDEF]'
2025-08-14 12:38:55 us=945526 Connection profiles END
2025-08-14 12:38:55 us=945533   remote_random = DISABLED
2025-08-14 12:38:55 us=945539   ipchange = '[UNDEF]'
2025-08-14 12:38:55 us=945545   dev = 'tun'
2025-08-14 12:38:55 us=945551   dev_type = '[UNDEF]'
2025-08-14 12:38:55 us=945557   dev_node = '[UNDEF]'
2025-08-14 12:38:55 us=945563   tuntap_options.disable_dco = ENABLED
2025-08-14 12:38:55 us=945570   lladdr = '[UNDEF]'
2025-08-14 12:38:55 us=945576   topology = 3
2025-08-14 12:38:55 us=945582   ifconfig_local = '*********'
2025-08-14 12:38:55 us=945588   ifconfig_remote_netmask = '*************'
2025-08-14 12:38:55 us=945594   ifconfig_noexec = DISABLED
2025-08-14 12:38:55 us=945609   ifconfig_nowarn = DISABLED
2025-08-14 12:38:55 us=945614   ifconfig_ipv6_local = '[UNDEF]'
2025-08-14 12:38:55 us=945619   ifconfig_ipv6_netbits = 0
2025-08-14 12:38:55 us=945624   ifconfig_ipv6_remote = '[UNDEF]'
2025-08-14 12:38:55 us=945629   shaper = 0
2025-08-14 12:38:55 us=945634   mtu_test = 0
2025-08-14 12:38:55 us=945639   mlock = DISABLED
2025-08-14 12:38:55 us=945644   keepalive_ping = 10
2025-08-14 12:38:55 us=945649   keepalive_timeout = 120
2025-08-14 12:38:55 us=945654   inactivity_timeout = 0
2025-08-14 12:38:55 us=945659   session_timeout = 0
2025-08-14 12:38:55 us=945664   inactivity_minimum_bytes = 0
2025-08-14 12:38:55 us=945669   ping_send_timeout = 10
2025-08-14 12:38:55 us=945674   ping_rec_timeout = 240
2025-08-14 12:38:55 us=945679   ping_rec_timeout_action = 2
2025-08-14 12:38:55 us=945684   ping_timer_remote = DISABLED
2025-08-14 12:38:55 us=945688   remap_sigusr1 = 0
2025-08-14 12:38:55 us=945693   persist_tun = ENABLED
2025-08-14 12:38:55 us=945698   persist_local_ip = DISABLED
2025-08-14 12:38:55 us=945703   persist_remote_ip = DISABLED
2025-08-14 12:38:55 us=945708   persist_key = ENABLED
2025-08-14 12:38:55 us=945713   passtos = DISABLED
2025-08-14 12:38:55 us=945718   resolve_retry_seconds = 1000000000
2025-08-14 12:38:55 us=945723   resolve_in_advance = DISABLED
2025-08-14 12:38:55 us=945728   username = '[UNDEF]'
2025-08-14 12:38:55 us=945733   groupname = '[UNDEF]'
2025-08-14 12:38:55 us=945738   chroot_dir = '[UNDEF]'
2025-08-14 12:38:55 us=945743   cd_dir = '[UNDEF]'
2025-08-14 12:38:55 us=945747   writepid = '/var/run/openvpn/server.pid'
2025-08-14 12:38:55 us=945752   up_script = '[UNDEF]'
2025-08-14 12:38:55 us=945757   down_script = '[UNDEF]'
2025-08-14 12:38:55 us=945762   down_pre = DISABLED
2025-08-14 12:38:55 us=945767   up_restart = DISABLED
2025-08-14 12:38:55 us=945772   up_delay = DISABLED
2025-08-14 12:38:55 us=945777   daemon = ENABLED
2025-08-14 12:38:55 us=945782   log = ENABLED
2025-08-14 12:38:55 us=945787   suppress_timestamps = DISABLED
2025-08-14 12:38:55 us=945792   machine_readable_output = DISABLED
2025-08-14 12:38:55 us=945797   nice = 0
2025-08-14 12:38:55 us=945802   verbosity = 4
2025-08-14 12:38:55 us=945807   mute = 0
2025-08-14 12:38:55 us=945811   gremlin = 0
2025-08-14 12:38:55 us=945816   status_file = '/root/VPN/OpenVPN/logs/openvpn-status.log'
2025-08-14 12:38:55 us=945822   status_file_version = 3
2025-08-14 12:38:55 us=945826   status_file_update_freq = 60
2025-08-14 12:38:55 us=945831   occ = ENABLED
2025-08-14 12:38:55 us=945836   rcvbuf = 0
2025-08-14 12:38:55 us=945841   sndbuf = 0
2025-08-14 12:38:55 us=945846   mark = 0
2025-08-14 12:38:55 us=945851   sockflags = 0
2025-08-14 12:38:55 us=945856   fast_io = ENABLED
2025-08-14 12:38:55 us=945862   comp.alg = 0
2025-08-14 12:38:55 us=945866   comp.flags = 24
2025-08-14 12:38:55 us=945871   route_script = '[UNDEF]'
2025-08-14 12:38:55 us=945876   route_default_gateway = '*********'
2025-08-14 12:38:55 us=945881   route_default_metric = 0
2025-08-14 12:38:55 us=945886   route_noexec = DISABLED
2025-08-14 12:38:55 us=945891   route_delay = 0
2025-08-14 12:38:55 us=945896   route_delay_window = 30
2025-08-14 12:38:55 us=945904   route_delay_defined = DISABLED
2025-08-14 12:38:55 us=945910   route_nopull = DISABLED
2025-08-14 12:38:55 us=945915   route_gateway_via_dhcp = DISABLED
2025-08-14 12:38:55 us=945920   allow_pull_fqdn = DISABLED
2025-08-14 12:38:55 us=945925   management_addr = 'localhost'
2025-08-14 12:38:55 us=945931   management_port = '7505'
2025-08-14 12:38:55 us=945936   management_user_pass = '[UNDEF]'
2025-08-14 12:38:55 us=945941   management_log_history_cache = 100
2025-08-14 12:38:55 us=945946   management_echo_buffer_size = 100
2025-08-14 12:38:55 us=945951   management_client_user = '[UNDEF]'
2025-08-14 12:38:55 us=945956   management_client_group = '[UNDEF]'
2025-08-14 12:38:55 us=945961   management_flags = 4
2025-08-14 12:38:55 us=945966   shared_secret_file = '[UNDEF]'
2025-08-14 12:38:55 us=945971   key_direction = 0
2025-08-14 12:38:55 us=945980   ciphername = 'AES-256-GCM'
2025-08-14 12:38:55 us=945985   ncp_ciphers = 'AES-256-GCM:AES-128-GCM:CHACHA20-POLY1305'
2025-08-14 12:38:55 us=945990   authname = 'SHA256'
2025-08-14 12:38:55 us=945995   engine = DISABLED
2025-08-14 12:38:55 us=946000   replay = ENABLED
2025-08-14 12:38:55 us=946005   mute_replay_warnings = DISABLED
2025-08-14 12:38:55 us=946010   replay_window = 64
2025-08-14 12:38:55 us=946015   replay_time = 15
2025-08-14 12:38:55 us=946041   packet_id_file = '[UNDEF]'
2025-08-14 12:38:55 us=946046   test_crypto = DISABLED
2025-08-14 12:38:55 us=946051   tls_server = ENABLED
2025-08-14 12:38:55 us=946056   tls_client = DISABLED
2025-08-14 12:38:55 us=946061   ca_file = '/root/VPN/OpenVPN/easy-rsa/pki/ca.crt'
2025-08-14 12:38:55 us=946066   ca_path = '[UNDEF]'
2025-08-14 12:38:55 us=946071   dh_file = '/root/VPN/OpenVPN/easy-rsa/pki/dh.pem'
2025-08-14 12:38:55 us=946076   cert_file = '/root/VPN/OpenVPN/easy-rsa/pki/issued/server.crt'
2025-08-14 12:38:55 us=946081   extra_certs_file = '[UNDEF]'
2025-08-14 12:38:55 us=946086   priv_key_file = '/root/VPN/OpenVPN/easy-rsa/pki/private/server.key'
2025-08-14 12:38:55 us=946092   pkcs12_file = '[UNDEF]'
2025-08-14 12:38:55 us=946097   cipher_list = '[UNDEF]'
2025-08-14 12:38:55 us=946102   cipher_list_tls13 = '[UNDEF]'
2025-08-14 12:38:55 us=946107   tls_cert_profile = '[UNDEF]'
2025-08-14 12:38:55 us=946112   tls_verify = '[UNDEF]'
2025-08-14 12:38:55 us=946117   tls_export_peer_cert_dir = '[UNDEF]'
2025-08-14 12:38:55 us=946122   verify_x509_type = 0
2025-08-14 12:38:55 us=946127   verify_x509_name = '[UNDEF]'
2025-08-14 12:38:55 us=946132   crl_file = '[UNDEF]'
2025-08-14 12:38:55 us=946137   ns_cert_type = 0
2025-08-14 12:38:55 us=946142   remote_cert_ku[i] = 0
2025-08-14 12:38:55 us=946147   remote_cert_ku[i] = 0
2025-08-14 12:38:55 us=946152   remote_cert_ku[i] = 0
2025-08-14 12:38:55 us=946157   remote_cert_ku[i] = 0
2025-08-14 12:38:55 us=946162   remote_cert_ku[i] = 0
2025-08-14 12:38:55 us=946180   remote_cert_ku[i] = 0
2025-08-14 12:38:55 us=946186   remote_cert_ku[i] = 0
2025-08-14 12:38:55 us=946191   remote_cert_ku[i] = 0
2025-08-14 12:38:55 us=946196   remote_cert_ku[i] = 0
2025-08-14 12:38:55 us=946201   remote_cert_ku[i] = 0
2025-08-14 12:38:55 us=946206   remote_cert_ku[i] = 0
2025-08-14 12:38:55 us=946211   remote_cert_ku[i] = 0
2025-08-14 12:38:55 us=946216   remote_cert_ku[i] = 0
2025-08-14 12:38:55 us=946221   remote_cert_ku[i] = 0
2025-08-14 12:38:55 us=946226   remote_cert_ku[i] = 0
2025-08-14 12:38:55 us=946231   remote_cert_ku[i] = 0
2025-08-14 12:38:55 us=946236   remote_cert_eku = '[UNDEF]'
2025-08-14 12:38:55 us=946241   ssl_flags = 192
2025-08-14 12:38:55 us=946246   tls_timeout = 2
2025-08-14 12:38:55 us=946251   renegotiate_bytes = -1
2025-08-14 12:38:55 us=946256   renegotiate_packets = 0
2025-08-14 12:38:55 us=946261   renegotiate_seconds = 3600
2025-08-14 12:38:55 us=946266   handshake_window = 60
2025-08-14 12:38:55 us=946271   transition_window = 3600
2025-08-14 12:38:55 us=946276   single_session = DISABLED
2025-08-14 12:38:55 us=946281   push_peer_info = DISABLED
2025-08-14 12:38:55 us=946286   tls_exit = DISABLED
2025-08-14 12:38:55 us=946291   tls_crypt_v2_metadata = '[UNDEF]'
2025-08-14 12:38:55 us=946296   pkcs11_protected_authentication = DISABLED
2025-08-14 12:38:55 us=946302   pkcs11_protected_authentication = DISABLED
2025-08-14 12:38:55 us=946307   pkcs11_protected_authentication = DISABLED
2025-08-14 12:38:55 us=946312   pkcs11_protected_authentication = DISABLED
2025-08-14 12:38:55 us=946316   pkcs11_protected_authentication = DISABLED
2025-08-14 12:38:55 us=946321   pkcs11_protected_authentication = DISABLED
2025-08-14 12:38:55 us=946326   pkcs11_protected_authentication = DISABLED
2025-08-14 12:38:55 us=946331   pkcs11_protected_authentication = DISABLED
2025-08-14 12:38:55 us=946336   pkcs11_protected_authentication = DISABLED
2025-08-14 12:38:55 us=946341   pkcs11_protected_authentication = DISABLED
2025-08-14 12:38:55 us=946346   pkcs11_protected_authentication = DISABLED
2025-08-14 12:38:55 us=946355   pkcs11_protected_authentication = DISABLED
2025-08-14 12:38:55 us=946360   pkcs11_protected_authentication = DISABLED
2025-08-14 12:38:55 us=946365   pkcs11_protected_authentication = DISABLED
2025-08-14 12:38:55 us=946370   pkcs11_protected_authentication = DISABLED
2025-08-14 12:38:55 us=946375   pkcs11_protected_authentication = DISABLED
2025-08-14 12:38:55 us=946380   pkcs11_private_mode = 00000000
2025-08-14 12:38:55 us=946385   pkcs11_private_mode = 00000000
2025-08-14 12:38:55 us=946390   pkcs11_private_mode = 00000000
2025-08-14 12:38:55 us=946395   pkcs11_private_mode = 00000000
2025-08-14 12:38:55 us=946400   pkcs11_private_mode = 00000000
2025-08-14 12:38:55 us=946405   pkcs11_private_mode = 00000000
2025-08-14 12:38:55 us=946410   pkcs11_private_mode = 00000000
2025-08-14 12:38:55 us=946415   pkcs11_private_mode = 00000000
2025-08-14 12:38:55 us=946420   pkcs11_private_mode = 00000000
2025-08-14 12:38:55 us=946425   pkcs11_private_mode = 00000000
2025-08-14 12:38:55 us=946430   pkcs11_private_mode = 00000000
2025-08-14 12:38:55 us=946435   pkcs11_private_mode = 00000000
2025-08-14 12:38:55 us=946440   pkcs11_private_mode = 00000000
2025-08-14 12:38:55 us=946445   pkcs11_private_mode = 00000000
2025-08-14 12:38:55 us=946450   pkcs11_private_mode = 00000000
2025-08-14 12:38:55 us=946455   pkcs11_private_mode = 00000000
2025-08-14 12:38:55 us=946460   pkcs11_cert_private = DISABLED
2025-08-14 12:38:55 us=946465   pkcs11_cert_private = DISABLED
2025-08-14 12:38:55 us=946470   pkcs11_cert_private = DISABLED
2025-08-14 12:38:55 us=946475   pkcs11_cert_private = DISABLED
2025-08-14 12:38:55 us=946480   pkcs11_cert_private = DISABLED
2025-08-14 12:38:55 us=946485   pkcs11_cert_private = DISABLED
2025-08-14 12:38:55 us=946490   pkcs11_cert_private = DISABLED
2025-08-14 12:38:55 us=946495   pkcs11_cert_private = DISABLED
2025-08-14 12:38:55 us=946499   pkcs11_cert_private = DISABLED
2025-08-14 12:38:55 us=946504   pkcs11_cert_private = DISABLED
2025-08-14 12:38:55 us=946509   pkcs11_cert_private = DISABLED
2025-08-14 12:38:55 us=946514   pkcs11_cert_private = DISABLED
2025-08-14 12:38:55 us=946519   pkcs11_cert_private = DISABLED
2025-08-14 12:38:55 us=946524   pkcs11_cert_private = DISABLED
2025-08-14 12:38:55 us=946529   pkcs11_cert_private = DISABLED
2025-08-14 12:38:55 us=946534   pkcs11_cert_private = DISABLED
2025-08-14 12:38:55 us=946539   pkcs11_pin_cache_period = -1
2025-08-14 12:38:55 us=946544   pkcs11_id = '[UNDEF]'
2025-08-14 12:38:55 us=946550   pkcs11_id_management = DISABLED
2025-08-14 12:38:55 us=946556   server_network = *********
2025-08-14 12:38:55 us=946562   server_netmask = *************
2025-08-14 12:38:55 us=946567   server_network_ipv6 = ::
2025-08-14 12:38:55 us=946572   server_netbits_ipv6 = 0
2025-08-14 12:38:55 us=946578   server_bridge_ip = 0.0.0.0
2025-08-14 12:38:55 us=946583   server_bridge_netmask = 0.0.0.0
2025-08-14 12:38:55 us=946588   server_bridge_pool_start = 0.0.0.0
2025-08-14 12:38:55 us=946593   server_bridge_pool_end = 0.0.0.0
2025-08-14 12:38:55 us=946600   push_entry = 'redirect-gateway def1 bypass-dhcp'
2025-08-14 12:38:55 us=946610   push_entry = 'dhcp-option DNS *******'
2025-08-14 12:38:55 us=946621   push_entry = 'dhcp-option DNS *******'
2025-08-14 12:38:55 us=946628   push_entry = 'route-gateway *********'
2025-08-14 12:38:55 us=946635   push_entry = 'topology subnet'
2025-08-14 12:38:55 us=946642   push_entry = 'ping 10'
2025-08-14 12:38:55 us=946648   push_entry = 'ping-restart 120'
2025-08-14 12:38:55 us=946655   ifconfig_pool_defined = ENABLED
2025-08-14 12:38:55 us=946661   ifconfig_pool_start = *********
2025-08-14 12:38:55 us=946667   ifconfig_pool_end = *********54
2025-08-14 12:38:55 us=946672   ifconfig_pool_netmask = *************
2025-08-14 12:38:55 us=946678   ifconfig_pool_persist_filename = '/root/VPN/OpenVPN/logs/ipp.txt'
2025-08-14 12:38:55 us=946683   ifconfig_pool_persist_refresh_freq = 600
2025-08-14 12:38:55 us=946689   ifconfig_ipv6_pool_defined = DISABLED
2025-08-14 12:38:55 us=946694   ifconfig_ipv6_pool_base = ::
2025-08-14 12:38:55 us=946705   ifconfig_ipv6_pool_netbits = 0
2025-08-14 12:38:55 us=946710   n_bcast_buf = 256
2025-08-14 12:38:55 us=946715   tcp_queue_limit = 64
2025-08-14 12:38:55 us=946720   real_hash_size = 256
2025-08-14 12:38:55 us=946727   virtual_hash_size = 256
2025-08-14 12:38:55 us=946734   client_connect_script = '/root/VPN/OpenVPN/scripts/client-connect.sh'
2025-08-14 12:38:55 us=946741   learn_address_script = '[UNDEF]'
2025-08-14 12:38:55 us=946747   client_disconnect_script = '/root/VPN/OpenVPN/scripts/client-disconnect.sh'
2025-08-14 12:38:55 us=946754   client_crresponse_script = '[UNDEF]'
2025-08-14 12:38:55 us=946760   client_config_dir = '[UNDEF]'
2025-08-14 12:38:55 us=946766   ccd_exclusive = DISABLED
2025-08-14 12:38:55 us=946773   tmp_dir = '/tmp'
2025-08-14 12:38:55 us=946779   push_ifconfig_defined = DISABLED
2025-08-14 12:38:55 us=946786   push_ifconfig_local = 0.0.0.0
2025-08-14 12:38:55 us=946797   push_ifconfig_remote_netmask = 0.0.0.0
2025-08-14 12:38:55 us=946803   push_ifconfig_ipv6_defined = DISABLED
2025-08-14 12:38:55 us=946809   push_ifconfig_ipv6_local = ::/0
2025-08-14 12:38:55 us=946814   push_ifconfig_ipv6_remote = ::
2025-08-14 12:38:55 us=946819   enable_c2c = ENABLED
2025-08-14 12:38:55 us=946824   duplicate_cn = ENABLED
2025-08-14 12:38:55 us=946829   cf_max = 0
2025-08-14 12:38:55 us=946834   cf_per = 0
2025-08-14 12:38:55 us=946839   cf_initial_max = 100
2025-08-14 12:38:55 us=946844   cf_initial_per = 10
2025-08-14 12:38:55 us=946849   max_clients = 1024
2025-08-14 12:38:55 us=946855   max_routes_per_client = 256
2025-08-14 12:38:55 us=946860   auth_user_pass_verify_script = '[UNDEF]'
2025-08-14 12:38:55 us=946865   auth_user_pass_verify_script_via_file = DISABLED
2025-08-14 12:38:55 us=946870   auth_token_generate = DISABLED
2025-08-14 12:38:55 us=946875   force_key_material_export = DISABLED
2025-08-14 12:38:55 us=946880   auth_token_lifetime = 0
2025-08-14 12:38:55 us=946885   auth_token_secret_file = '[UNDEF]'
2025-08-14 12:38:55 us=946890   port_share_host = '[UNDEF]'
2025-08-14 12:38:55 us=946895   port_share_port = '[UNDEF]'
2025-08-14 12:38:55 us=946900   vlan_tagging = DISABLED
2025-08-14 12:38:55 us=946905   vlan_accept = all
2025-08-14 12:38:55 us=946910   vlan_pvid = 1
2025-08-14 12:38:55 us=946915   client = DISABLED
2025-08-14 12:38:55 us=946920   pull = DISABLED
2025-08-14 12:38:55 us=946925   auth_user_pass_file = '[UNDEF]'
2025-08-14 12:38:55 us=946931 OpenVPN 2.6.14 x86_64-pc-linux-gnu [SSL (OpenSSL)] [LZO] [LZ4] [EPOLL] [PKCS11] [MH/PKTINFO] [AEAD] [DCO]
2025-08-14 12:38:55 us=946949 library versions: OpenSSL 3.0.13 30 Jan 2024, LZO 2.10
2025-08-14 12:38:55 us=946962 DCO version: N/A
2025-08-14 12:38:55 us=947852 setsockopt(IPV6_V6ONLY=0)
2025-08-14 12:38:55 us=948194 MANAGEMENT: TCP Socket listening on [AF_INET6]::1:7505
2025-08-14 12:38:55 us=948219 Need hold release from management interface, waiting...
2025-08-14 12:39:42 us=121439 MANAGEMENT: Client connected from [AF_INET6]::1:37420
2025-08-14 12:39:42 us=121592 MANAGEMENT: CMD 'hold release'
2025-08-14 12:39:42 us=121897 WARNING: --ifconfig-pool-persist will not work with --duplicate-cn
2025-08-14 12:39:42 us=121950 net_route_v4_best_gw query: dst 0.0.0.0
2025-08-14 12:39:42 us=122055 net_route_v4_best_gw result: via ************ dev eth0
2025-08-14 12:39:42 us=122093 NOTE: the current --script-security setting may allow this configuration to call user-defined scripts
2025-08-14 12:39:42 us=123550 Diffie-Hellman initialized with 2048 bit key
2025-08-14 12:39:42 us=124700 Outgoing Control Channel Authentication: Using 256 bit message hash 'SHA256' for HMAC authentication
2025-08-14 12:39:42 us=124733 Incoming Control Channel Authentication: Using 256 bit message hash 'SHA256' for HMAC authentication
2025-08-14 12:39:42 us=124759 TLS-Auth MTU parms [ mss_fix:0 max_frag:0 tun_mtu:1250 tun_max_mtu:0 headroom:126 payload:1600 tailroom:126 ET:0 ]
2025-08-14 12:39:42 us=126095 TUN/TAP device tun0 opened
2025-08-14 12:39:42 us=126144 do_ifconfig, ipv4=1, ipv6=0
2025-08-14 12:39:42 us=126512 net_iface_mtu_set: mtu 1500 for tun0
2025-08-14 12:39:42 us=126582 net_iface_up: set tun0 up
2025-08-14 12:39:42 us=126718 net_addr_v4_add: *********/24 dev tun0
2025-08-14 12:39:42 us=126817 Data Channel MTU parms [ mss_fix:0 max_frag:0 tun_mtu:1500 tun_max_mtu:1600 headroom:136 payload:1768 tailroom:562 ET:0 ]
2025-08-14 12:39:42 us=126837 Could not determine IPv4/IPv6 protocol. Using AF_INET
2025-08-14 12:39:42 us=126861 Socket Buffers: R=[212992->212992] S=[212992->212992]
2025-08-14 12:39:42 us=126877 UDPv4 link local (bound): [AF_INET][undef]:1194
2025-08-14 12:39:42 us=126885 UDPv4 link remote: [AF_UNSPEC]
2025-08-14 12:39:42 us=126903 MULTI: multi_init called, r=256 v=256
2025-08-14 12:39:42 us=126942 IFCONFIG POOL IPv4: base=********* size=253
2025-08-14 12:39:42 us=126956 IFCONFIG POOL LIST
2025-08-14 12:39:42 us=126991 Initialization Sequence Completed
2025-08-14 12:40:06 us=183395 MANAGEMENT: Client disconnected
2025-08-14 12:40:31 us=239446 event_wait : Interrupted system call (fd=-1,code=4)
2025-08-14 12:40:33 us=241731 TCP/UDP: Closing socket
2025-08-14 12:40:33 us=241806 Closing TUN/TAP interface
2025-08-14 12:40:33 us=241816 net_addr_v4_del: ********* dev tun0
2025-08-14 12:40:33 us=250580 SIGTERM[hard,] received, process exiting
2025-08-14 12:44:57 us=122496 Note: Kernel support for ovpn-dco missing, disabling data channel offload.
2025-08-14 12:44:57 us=122572 WARNING: Using --management on a TCP port WITHOUT passwords is STRONGLY discouraged and considered insecure
2025-08-14 12:44:57 us=122620 Current Parameter Settings:
2025-08-14 12:44:57 us=122628   config = '/etc/openvpn/server.conf'
2025-08-14 12:44:57 us=122636   mode = 1
2025-08-14 12:44:57 us=122643   persist_config = DISABLED
2025-08-14 12:44:57 us=122649   persist_mode = 1
2025-08-14 12:44:57 us=122655   show_ciphers = DISABLED
2025-08-14 12:44:57 us=122667   show_digests = DISABLED
2025-08-14 12:44:57 us=122673   show_engines = DISABLED
2025-08-14 12:44:57 us=122680   genkey = DISABLED
2025-08-14 12:44:57 us=122686   genkey_filename = '[UNDEF]'
2025-08-14 12:44:57 us=122692   key_pass_file = '[UNDEF]'
2025-08-14 12:44:57 us=122698   show_tls_ciphers = DISABLED
2025-08-14 12:44:57 us=122704   connect_retry_max = 0
2025-08-14 12:44:57 us=122713 Connection profiles [0]:
2025-08-14 12:44:57 us=122728   proto = udp
2025-08-14 12:44:57 us=122738   local = '[UNDEF]'
2025-08-14 12:44:57 us=122746   local_port = '1194'
2025-08-14 12:44:57 us=122754   remote = '[UNDEF]'
2025-08-14 12:44:57 us=122761   remote_port = '1194'
2025-08-14 12:44:57 us=122769   remote_float = DISABLED
2025-08-14 12:44:57 us=122777   bind_defined = DISABLED
2025-08-14 12:44:57 us=122784   bind_local = ENABLED
2025-08-14 12:44:57 us=122791   bind_ipv6_only = DISABLED
2025-08-14 12:44:57 us=122797   connect_retry_seconds = 1
2025-08-14 12:44:57 us=122803   connect_timeout = 120
2025-08-14 12:44:57 us=122810   socks_proxy_server = '[UNDEF]'
2025-08-14 12:44:57 us=122816   socks_proxy_port = '[UNDEF]'
2025-08-14 12:44:57 us=122822   tun_mtu = 1500
2025-08-14 12:44:57 us=122829   tun_mtu_defined = ENABLED
2025-08-14 12:44:57 us=122835   link_mtu = 1500
2025-08-14 12:44:57 us=122841   link_mtu_defined = DISABLED
2025-08-14 12:44:57 us=122848   tun_mtu_extra = 0
2025-08-14 12:44:57 us=122854   tun_mtu_extra_defined = DISABLED
2025-08-14 12:44:57 us=122862   tls_mtu = 1250
2025-08-14 12:44:57 us=122871   mtu_discover_type = -1
2025-08-14 12:44:57 us=122879   fragment = 0
2025-08-14 12:44:57 us=122887   mssfix = 1492
2025-08-14 12:44:57 us=122895   mssfix_encap = ENABLED
2025-08-14 12:44:57 us=122901   mssfix_fixed = DISABLED
2025-08-14 12:44:57 us=122908   explicit_exit_notification = 1
2025-08-14 12:44:57 us=122914   tls_auth_file = '[INLINE]'
2025-08-14 12:44:57 us=122920   key_direction = 0
2025-08-14 12:44:57 us=122927   tls_crypt_file = '[UNDEF]'
2025-08-14 12:44:57 us=122933   tls_crypt_v2_file = '[UNDEF]'
2025-08-14 12:44:57 us=122939 Connection profiles END
2025-08-14 12:44:57 us=122946   remote_random = DISABLED
2025-08-14 12:44:57 us=122952   ipchange = '[UNDEF]'
2025-08-14 12:44:57 us=122958   dev = 'tun'
2025-08-14 12:44:57 us=122974   dev_type = '[UNDEF]'
2025-08-14 12:44:57 us=122979   dev_node = '[UNDEF]'
2025-08-14 12:44:57 us=122984   tuntap_options.disable_dco = ENABLED
2025-08-14 12:44:57 us=122989   lladdr = '[UNDEF]'
2025-08-14 12:44:57 us=122995   topology = 3
2025-08-14 12:44:57 us=123000   ifconfig_local = '*********'
2025-08-14 12:44:57 us=123005   ifconfig_remote_netmask = '*************'
2025-08-14 12:44:57 us=123010   ifconfig_noexec = DISABLED
2025-08-14 12:44:57 us=123015   ifconfig_nowarn = DISABLED
2025-08-14 12:44:57 us=123020   ifconfig_ipv6_local = '[UNDEF]'
2025-08-14 12:44:57 us=123025   ifconfig_ipv6_netbits = 0
2025-08-14 12:44:57 us=123029   ifconfig_ipv6_remote = '[UNDEF]'
2025-08-14 12:44:57 us=123035   shaper = 0
2025-08-14 12:44:57 us=123039   mtu_test = 0
2025-08-14 12:44:57 us=123088   mlock = DISABLED
2025-08-14 12:44:57 us=123099   keepalive_ping = 10
2025-08-14 12:44:57 us=123106   keepalive_timeout = 120
2025-08-14 12:44:57 us=123113   inactivity_timeout = 0
2025-08-14 12:44:57 us=123119   session_timeout = 0
2025-08-14 12:44:57 us=123126   inactivity_minimum_bytes = 0
2025-08-14 12:44:57 us=123132   ping_send_timeout = 10
2025-08-14 12:44:57 us=123138   ping_rec_timeout = 240
2025-08-14 12:44:57 us=123144   ping_rec_timeout_action = 2
2025-08-14 12:44:57 us=123150   ping_timer_remote = DISABLED
2025-08-14 12:44:57 us=123156   remap_sigusr1 = 0
2025-08-14 12:44:57 us=123162   persist_tun = ENABLED
2025-08-14 12:44:57 us=123208   persist_local_ip = DISABLED
2025-08-14 12:44:57 us=123214   persist_remote_ip = DISABLED
2025-08-14 12:44:57 us=123219   persist_key = ENABLED
2025-08-14 12:44:57 us=123224   passtos = DISABLED
2025-08-14 12:44:57 us=123229   resolve_retry_seconds = 1000000000
2025-08-14 12:44:57 us=123234   resolve_in_advance = DISABLED
2025-08-14 12:44:57 us=123239   username = '[UNDEF]'
2025-08-14 12:44:57 us=123244   groupname = '[UNDEF]'
2025-08-14 12:44:57 us=123249   chroot_dir = '[UNDEF]'
2025-08-14 12:44:57 us=123254   cd_dir = '[UNDEF]'
2025-08-14 12:44:57 us=123259   writepid = '/var/run/openvpn/server.pid'
2025-08-14 12:44:57 us=123264   up_script = '[UNDEF]'
2025-08-14 12:44:57 us=123269   down_script = '[UNDEF]'
2025-08-14 12:44:57 us=123274   down_pre = DISABLED
2025-08-14 12:44:57 us=123278   up_restart = DISABLED
2025-08-14 12:44:57 us=123283   up_delay = DISABLED
2025-08-14 12:44:57 us=123288   daemon = ENABLED
2025-08-14 12:44:57 us=123293   log = ENABLED
2025-08-14 12:44:57 us=123298   suppress_timestamps = DISABLED
2025-08-14 12:44:57 us=123303   machine_readable_output = DISABLED
2025-08-14 12:44:57 us=123308   nice = 0
2025-08-14 12:44:57 us=123313   verbosity = 4
2025-08-14 12:44:57 us=123318   mute = 0
2025-08-14 12:44:57 us=123323   gremlin = 0
2025-08-14 12:44:57 us=123328   status_file = '/root/VPN/OpenVPN/logs/openvpn-status.log'
2025-08-14 12:44:57 us=123333   status_file_version = 3
2025-08-14 12:44:57 us=123338   status_file_update_freq = 60
2025-08-14 12:44:57 us=123343   occ = ENABLED
2025-08-14 12:44:57 us=123348   rcvbuf = 0
2025-08-14 12:44:57 us=123353   sndbuf = 0
2025-08-14 12:44:57 us=123358   mark = 0
2025-08-14 12:44:57 us=123364   sockflags = 0
2025-08-14 12:44:57 us=123369   fast_io = ENABLED
2025-08-14 12:44:57 us=123374   comp.alg = 0
2025-08-14 12:44:57 us=123379   comp.flags = 24
2025-08-14 12:44:57 us=123386   route_script = '[UNDEF]'
2025-08-14 12:44:57 us=123398   route_default_gateway = '*********'
2025-08-14 12:44:57 us=123405   route_default_metric = 0
2025-08-14 12:44:57 us=123412   route_noexec = DISABLED
2025-08-14 12:44:57 us=123418   route_delay = 0
2025-08-14 12:44:57 us=123425   route_delay_window = 30
2025-08-14 12:44:57 us=123438   route_delay_defined = DISABLED
2025-08-14 12:44:57 us=123445   route_nopull = DISABLED
2025-08-14 12:44:57 us=123450   route_gateway_via_dhcp = DISABLED
2025-08-14 12:44:57 us=123456   allow_pull_fqdn = DISABLED
2025-08-14 12:44:57 us=123462   management_addr = 'localhost'
2025-08-14 12:44:57 us=123468   management_port = '7505'
2025-08-14 12:44:57 us=123475   management_user_pass = '[UNDEF]'
2025-08-14 12:44:57 us=123492   management_log_history_cache = 100
2025-08-14 12:44:57 us=123498   management_echo_buffer_size = 100
2025-08-14 12:44:57 us=123505   management_client_user = '[UNDEF]'
2025-08-14 12:44:57 us=123511   management_client_group = '[UNDEF]'
2025-08-14 12:44:57 us=123518   management_flags = 4
2025-08-14 12:44:57 us=123528   shared_secret_file = '[UNDEF]'
2025-08-14 12:44:57 us=123538   key_direction = 0
2025-08-14 12:44:57 us=123544   ciphername = 'AES-256-GCM'
2025-08-14 12:44:57 us=123550   ncp_ciphers = 'AES-256-GCM:AES-128-GCM:CHACHA20-POLY1305'
2025-08-14 12:44:57 us=123556   authname = 'SHA256'
2025-08-14 12:44:57 us=123562   engine = DISABLED
2025-08-14 12:44:57 us=123568   replay = ENABLED
2025-08-14 12:44:57 us=123574   mute_replay_warnings = DISABLED
2025-08-14 12:44:57 us=123581   replay_window = 64
2025-08-14 12:44:57 us=123587   replay_time = 15
2025-08-14 12:44:57 us=123593   packet_id_file = '[UNDEF]'
2025-08-14 12:44:57 us=123599   test_crypto = DISABLED
2025-08-14 12:44:57 us=123605   tls_server = ENABLED
2025-08-14 12:44:57 us=123610   tls_client = DISABLED
2025-08-14 12:44:57 us=123616   ca_file = '/root/VPN/OpenVPN/easy-rsa/pki/ca.crt'
2025-08-14 12:44:57 us=123622   ca_path = '[UNDEF]'
2025-08-14 12:44:57 us=123627   dh_file = '/root/VPN/OpenVPN/easy-rsa/pki/dh.pem'
2025-08-14 12:44:57 us=123633   cert_file = '/root/VPN/OpenVPN/easy-rsa/pki/issued/server.crt'
2025-08-14 12:44:57 us=123639   extra_certs_file = '[UNDEF]'
2025-08-14 12:44:57 us=123645   priv_key_file = '/root/VPN/OpenVPN/easy-rsa/pki/private/server.key'
2025-08-14 12:44:57 us=123651   pkcs12_file = '[UNDEF]'
2025-08-14 12:44:57 us=123671   cipher_list = '[UNDEF]'
2025-08-14 12:44:57 us=123677   cipher_list_tls13 = '[UNDEF]'
2025-08-14 12:44:57 us=123683   tls_cert_profile = '[UNDEF]'
2025-08-14 12:44:57 us=123689   tls_verify = '[UNDEF]'
2025-08-14 12:44:57 us=123695   tls_export_peer_cert_dir = '[UNDEF]'
2025-08-14 12:44:57 us=123701   verify_x509_type = 0
2025-08-14 12:44:57 us=123707   verify_x509_name = '[UNDEF]'
2025-08-14 12:44:57 us=123714   crl_file = '[UNDEF]'
2025-08-14 12:44:57 us=123720   ns_cert_type = 0
2025-08-14 12:44:57 us=123726   remote_cert_ku[i] = 0
2025-08-14 12:44:57 us=123732   remote_cert_ku[i] = 0
2025-08-14 12:44:57 us=123738   remote_cert_ku[i] = 0
2025-08-14 12:44:57 us=123745   remote_cert_ku[i] = 0
2025-08-14 12:44:57 us=123751   remote_cert_ku[i] = 0
2025-08-14 12:44:57 us=123757   remote_cert_ku[i] = 0
2025-08-14 12:44:57 us=123763   remote_cert_ku[i] = 0
2025-08-14 12:44:57 us=123769   remote_cert_ku[i] = 0
2025-08-14 12:44:57 us=123776   remote_cert_ku[i] = 0
2025-08-14 12:44:57 us=123782   remote_cert_ku[i] = 0
2025-08-14 12:44:57 us=123789   remote_cert_ku[i] = 0
2025-08-14 12:44:57 us=123795   remote_cert_ku[i] = 0
2025-08-14 12:44:57 us=123801   remote_cert_ku[i] = 0
2025-08-14 12:44:57 us=123808   remote_cert_ku[i] = 0
2025-08-14 12:44:57 us=123814   remote_cert_ku[i] = 0
2025-08-14 12:44:57 us=123821   remote_cert_ku[i] = 0
2025-08-14 12:44:57 us=123827   remote_cert_eku = '[UNDEF]'
2025-08-14 12:44:57 us=123833   ssl_flags = 192
2025-08-14 12:44:57 us=123840   tls_timeout = 2
2025-08-14 12:44:57 us=123846   renegotiate_bytes = -1
2025-08-14 12:44:57 us=123852   renegotiate_packets = 0
2025-08-14 12:44:57 us=123858   renegotiate_seconds = 3600
2025-08-14 12:44:57 us=123865   handshake_window = 60
2025-08-14 12:44:57 us=123871   transition_window = 3600
2025-08-14 12:44:57 us=123878   single_session = DISABLED
2025-08-14 12:44:57 us=123884   push_peer_info = DISABLED
2025-08-14 12:44:57 us=123890   tls_exit = DISABLED
2025-08-14 12:44:57 us=123897   tls_crypt_v2_metadata = '[UNDEF]'
2025-08-14 12:44:57 us=123904   pkcs11_protected_authentication = DISABLED
2025-08-14 12:44:57 us=123910   pkcs11_protected_authentication = DISABLED
2025-08-14 12:44:57 us=123916   pkcs11_protected_authentication = DISABLED
2025-08-14 12:44:57 us=123922   pkcs11_protected_authentication = DISABLED
2025-08-14 12:44:57 us=123928   pkcs11_protected_authentication = DISABLED
2025-08-14 12:44:57 us=123941   pkcs11_protected_authentication = DISABLED
2025-08-14 12:44:57 us=123947   pkcs11_protected_authentication = DISABLED
2025-08-14 12:44:57 us=123954   pkcs11_protected_authentication = DISABLED
2025-08-14 12:44:57 us=123960   pkcs11_protected_authentication = DISABLED
2025-08-14 12:44:57 us=123966   pkcs11_protected_authentication = DISABLED
2025-08-14 12:44:57 us=123972   pkcs11_protected_authentication = DISABLED
2025-08-14 12:44:57 us=123979   pkcs11_protected_authentication = DISABLED
2025-08-14 12:44:57 us=123984   pkcs11_protected_authentication = DISABLED
2025-08-14 12:44:57 us=123989   pkcs11_protected_authentication = DISABLED
2025-08-14 12:44:57 us=123994   pkcs11_protected_authentication = DISABLED
2025-08-14 12:44:57 us=123999   pkcs11_protected_authentication = DISABLED
2025-08-14 12:44:57 us=124004   pkcs11_private_mode = 00000000
2025-08-14 12:44:57 us=124010   pkcs11_private_mode = 00000000
2025-08-14 12:44:57 us=124015   pkcs11_private_mode = 00000000
2025-08-14 12:44:57 us=124020   pkcs11_private_mode = 00000000
2025-08-14 12:44:57 us=124025   pkcs11_private_mode = 00000000
2025-08-14 12:44:57 us=124030   pkcs11_private_mode = 00000000
2025-08-14 12:44:57 us=124035   pkcs11_private_mode = 00000000
2025-08-14 12:44:57 us=124047   pkcs11_private_mode = 00000000
2025-08-14 12:44:57 us=124052   pkcs11_private_mode = 00000000
2025-08-14 12:44:57 us=124057   pkcs11_private_mode = 00000000
2025-08-14 12:44:57 us=124062   pkcs11_private_mode = 00000000
2025-08-14 12:44:57 us=124067   pkcs11_private_mode = 00000000
2025-08-14 12:44:57 us=124072   pkcs11_private_mode = 00000000
2025-08-14 12:44:57 us=124077   pkcs11_private_mode = 00000000
2025-08-14 12:44:57 us=124082   pkcs11_private_mode = 00000000
2025-08-14 12:44:57 us=124087   pkcs11_private_mode = 00000000
2025-08-14 12:44:57 us=124092   pkcs11_cert_private = DISABLED
2025-08-14 12:44:57 us=124098   pkcs11_cert_private = DISABLED
2025-08-14 12:44:57 us=124103   pkcs11_cert_private = DISABLED
2025-08-14 12:44:57 us=124107   pkcs11_cert_private = DISABLED
2025-08-14 12:44:57 us=124114   pkcs11_cert_private = DISABLED
2025-08-14 12:44:57 us=124125   pkcs11_cert_private = DISABLED
2025-08-14 12:44:57 us=124134   pkcs11_cert_private = DISABLED
2025-08-14 12:44:57 us=124141   pkcs11_cert_private = DISABLED
2025-08-14 12:44:57 us=124147   pkcs11_cert_private = DISABLED
2025-08-14 12:44:57 us=124153   pkcs11_cert_private = DISABLED
2025-08-14 12:44:57 us=124160   pkcs11_cert_private = DISABLED
2025-08-14 12:44:57 us=124199   pkcs11_cert_private = DISABLED
2025-08-14 12:44:57 us=124213   pkcs11_cert_private = DISABLED
2025-08-14 12:44:57 us=124220   pkcs11_cert_private = DISABLED
2025-08-14 12:44:57 us=124225   pkcs11_cert_private = DISABLED
2025-08-14 12:44:57 us=124231   pkcs11_cert_private = DISABLED
2025-08-14 12:44:57 us=124237   pkcs11_pin_cache_period = -1
2025-08-14 12:44:57 us=124243   pkcs11_id = '[UNDEF]'
2025-08-14 12:44:57 us=124249   pkcs11_id_management = DISABLED
2025-08-14 12:44:57 us=124264   server_network = *********
2025-08-14 12:44:57 us=124271   server_netmask = *************
2025-08-14 12:44:57 us=124278   server_network_ipv6 = ::
2025-08-14 12:44:57 us=124284   server_netbits_ipv6 = 0
2025-08-14 12:44:57 us=124290   server_bridge_ip = 0.0.0.0
2025-08-14 12:44:57 us=124297   server_bridge_netmask = 0.0.0.0
2025-08-14 12:44:57 us=124303   server_bridge_pool_start = 0.0.0.0
2025-08-14 12:44:57 us=124310   server_bridge_pool_end = 0.0.0.0
2025-08-14 12:44:57 us=124316   push_entry = 'redirect-gateway def1 bypass-dhcp'
2025-08-14 12:44:57 us=124322   push_entry = 'dhcp-option DNS *******'
2025-08-14 12:44:57 us=124329   push_entry = 'dhcp-option DNS *******'
2025-08-14 12:44:57 us=124335   push_entry = 'route-gateway *********'
2025-08-14 12:44:57 us=124341   push_entry = 'topology subnet'
2025-08-14 12:44:57 us=124347   push_entry = 'ping 10'
2025-08-14 12:44:57 us=124352   push_entry = 'ping-restart 120'
2025-08-14 12:44:57 us=124358   ifconfig_pool_defined = ENABLED
2025-08-14 12:44:57 us=124364   ifconfig_pool_start = *********
2025-08-14 12:44:57 us=124377   ifconfig_pool_end = *********54
2025-08-14 12:44:57 us=124384   ifconfig_pool_netmask = *************
2025-08-14 12:44:57 us=124390   ifconfig_pool_persist_filename = '/root/VPN/OpenVPN/logs/ipp.txt'
2025-08-14 12:44:57 us=124397   ifconfig_pool_persist_refresh_freq = 600
2025-08-14 12:44:57 us=124403   ifconfig_ipv6_pool_defined = DISABLED
2025-08-14 12:44:57 us=124410   ifconfig_ipv6_pool_base = ::
2025-08-14 12:44:57 us=124416   ifconfig_ipv6_pool_netbits = 0
2025-08-14 12:44:57 us=124422   n_bcast_buf = 256
2025-08-14 12:44:57 us=124429   tcp_queue_limit = 64
2025-08-14 12:44:57 us=124435   real_hash_size = 256
2025-08-14 12:44:57 us=124441   virtual_hash_size = 256
2025-08-14 12:44:57 us=124448   client_connect_script = '/root/VPN/OpenVPN/scripts/client-connect.sh'
2025-08-14 12:44:57 us=124454   learn_address_script = '[UNDEF]'
2025-08-14 12:44:57 us=124460   client_disconnect_script = '/root/VPN/OpenVPN/scripts/client-disconnect.sh'
2025-08-14 12:44:57 us=124467   client_crresponse_script = '[UNDEF]'
2025-08-14 12:44:57 us=124473   client_config_dir = '[UNDEF]'
2025-08-14 12:44:57 us=124478   ccd_exclusive = DISABLED
2025-08-14 12:44:57 us=124485   tmp_dir = '/tmp'
2025-08-14 12:44:57 us=124491   push_ifconfig_defined = DISABLED
2025-08-14 12:44:57 us=124498   push_ifconfig_local = 0.0.0.0
2025-08-14 12:44:57 us=124510   push_ifconfig_remote_netmask = 0.0.0.0
2025-08-14 12:44:57 us=124516   push_ifconfig_ipv6_defined = DISABLED
2025-08-14 12:44:57 us=124523   push_ifconfig_ipv6_local = ::/0
2025-08-14 12:44:57 us=124529   push_ifconfig_ipv6_remote = ::
2025-08-14 12:44:57 us=124536   enable_c2c = ENABLED
2025-08-14 12:44:57 us=124542   duplicate_cn = ENABLED
2025-08-14 12:44:57 us=124548   cf_max = 0
2025-08-14 12:44:57 us=124554   cf_per = 0
2025-08-14 12:44:57 us=124560   cf_initial_max = 100
2025-08-14 12:44:57 us=124566   cf_initial_per = 10
2025-08-14 12:44:57 us=124572   max_clients = 1024
2025-08-14 12:44:57 us=124578   max_routes_per_client = 256
2025-08-14 12:44:57 us=124585   auth_user_pass_verify_script = '[UNDEF]'
2025-08-14 12:44:57 us=124592   auth_user_pass_verify_script_via_file = DISABLED
2025-08-14 12:44:57 us=124598   auth_token_generate = DISABLED
2025-08-14 12:44:57 us=124605   force_key_material_export = DISABLED
2025-08-14 12:44:57 us=124611   auth_token_lifetime = 0
2025-08-14 12:44:57 us=124617   auth_token_secret_file = '[UNDEF]'
2025-08-14 12:44:57 us=124623   port_share_host = '[UNDEF]'
2025-08-14 12:44:57 us=124629   port_share_port = '[UNDEF]'
2025-08-14 12:44:57 us=124635   vlan_tagging = DISABLED
2025-08-14 12:44:57 us=124642   vlan_accept = all
2025-08-14 12:44:57 us=124649   vlan_pvid = 1
2025-08-14 12:44:57 us=124655   client = DISABLED
2025-08-14 12:44:57 us=124662   pull = DISABLED
2025-08-14 12:44:57 us=124668   auth_user_pass_file = '[UNDEF]'
2025-08-14 12:44:57 us=124675 OpenVPN 2.6.14 x86_64-pc-linux-gnu [SSL (OpenSSL)] [LZO] [LZ4] [EPOLL] [PKCS11] [MH/PKTINFO] [AEAD] [DCO]
2025-08-14 12:44:57 us=124691 library versions: OpenSSL 3.0.13 30 Jan 2024, LZO 2.10
2025-08-14 12:44:57 us=124705 DCO version: N/A
2025-08-14 12:44:57 us=125412 setsockopt(IPV6_V6ONLY=0)
2025-08-14 12:44:57 us=125453 MANAGEMENT: TCP Socket listening on [AF_INET6]::1:7505
2025-08-14 12:44:57 us=125465 Need hold release from management interface, waiting...
2025-08-14 12:45:03 us=461599 MANAGEMENT: Client connected from [AF_INET6]::1:56170
2025-08-14 12:45:03 us=461729 MANAGEMENT: CMD 'hold release'
2025-08-14 12:45:03 us=461988 WARNING: --ifconfig-pool-persist will not work with --duplicate-cn
2025-08-14 12:45:03 us=462002 net_route_v4_best_gw query: dst 0.0.0.0
2025-08-14 12:45:03 us=462102 net_route_v4_best_gw result: via ************ dev eth0
2025-08-14 12:45:03 us=462140 NOTE: the current --script-security setting may allow this configuration to call user-defined scripts
2025-08-14 12:45:03 us=463987 Diffie-Hellman initialized with 2048 bit key
2025-08-14 12:45:03 us=465225 Outgoing Control Channel Authentication: Using 256 bit message hash 'SHA256' for HMAC authentication
2025-08-14 12:45:03 us=465358 Incoming Control Channel Authentication: Using 256 bit message hash 'SHA256' for HMAC authentication
2025-08-14 12:45:03 us=465392 TLS-Auth MTU parms [ mss_fix:0 max_frag:0 tun_mtu:1250 tun_max_mtu:0 headroom:126 payload:1600 tailroom:126 ET:0 ]
2025-08-14 12:45:03 us=466682 TUN/TAP device tun0 opened
2025-08-14 12:45:03 us=466734 do_ifconfig, ipv4=1, ipv6=0
2025-08-14 12:45:03 us=466759 net_iface_mtu_set: mtu 1500 for tun0
2025-08-14 12:45:03 us=466805 net_iface_up: set tun0 up
2025-08-14 12:45:03 us=467034 net_addr_v4_add: *********/24 dev tun0
2025-08-14 12:45:03 us=467128 Data Channel MTU parms [ mss_fix:0 max_frag:0 tun_mtu:1500 tun_max_mtu:1600 headroom:136 payload:1768 tailroom:562 ET:0 ]
2025-08-14 12:45:03 us=467144 Could not determine IPv4/IPv6 protocol. Using AF_INET
2025-08-14 12:45:03 us=467251 Socket Buffers: R=[212992->212992] S=[212992->212992]
2025-08-14 12:45:03 us=467278 UDPv4 link local (bound): [AF_INET][undef]:1194
2025-08-14 12:45:03 us=467287 UDPv4 link remote: [AF_UNSPEC]
2025-08-14 12:45:03 us=467306 MULTI: multi_init called, r=256 v=256
2025-08-14 12:45:03 us=467342 IFCONFIG POOL IPv4: base=********* size=253
2025-08-14 12:45:03 us=467358 IFCONFIG POOL LIST
2025-08-14 12:45:03 us=467392 Initialization Sequence Completed
2025-08-14 12:45:04 us=344127 Connection Attempt MULTI: multi_create_instance called
2025-08-14 12:45:04 us=344206 127.0.0.1:57676 Re-using SSL/TLS context
2025-08-14 12:45:04 us=344268 127.0.0.1:57676 Outgoing Control Channel Authentication: Using 256 bit message hash 'SHA256' for HMAC authentication
2025-08-14 12:45:04 us=344279 127.0.0.1:57676 Incoming Control Channel Authentication: Using 256 bit message hash 'SHA256' for HMAC authentication
2025-08-14 12:45:04 us=344475 127.0.0.1:57676 Control Channel MTU parms [ mss_fix:0 max_frag:0 tun_mtu:1250 tun_max_mtu:0 headroom:126 payload:1600 tailroom:126 ET:0 ]
2025-08-14 12:45:04 us=344485 127.0.0.1:57676 Data Channel MTU parms [ mss_fix:0 max_frag:0 tun_mtu:1500 tun_max_mtu:1600 headroom:136 payload:1768 tailroom:562 ET:0 ]
2025-08-14 12:45:04 us=353099 127.0.0.1:57676 VERIFY OK: depth=1, CN=EVO-EDEN-VPN-CA
2025-08-14 12:45:04 us=353230 127.0.0.1:57676 VERIFY OK: depth=0, CN=EVO-EDEN-VPN-CA
2025-08-14 12:45:04 us=355057 127.0.0.1:57676 peer info: IV_VER=2.6.14
2025-08-14 12:45:04 us=355099 127.0.0.1:57676 peer info: IV_PLAT=linux
2025-08-14 12:45:04 us=355108 127.0.0.1:57676 peer info: IV_TCPNL=1
2025-08-14 12:45:04 us=355115 127.0.0.1:57676 peer info: IV_MTU=1600
2025-08-14 12:45:04 us=355123 127.0.0.1:57676 peer info: IV_NCP=2
2025-08-14 12:45:04 us=355130 127.0.0.1:57676 peer info: IV_CIPHERS=AES-256-GCM:AES-128-GCM:CHACHA20-POLY1305
2025-08-14 12:45:04 us=355137 127.0.0.1:57676 peer info: IV_PROTO=990
2025-08-14 12:45:04 us=355144 127.0.0.1:57676 peer info: IV_LZO_STUB=1
2025-08-14 12:45:04 us=355151 127.0.0.1:57676 peer info: IV_COMP_STUB=1
2025-08-14 12:45:04 us=355158 127.0.0.1:57676 peer info: IV_COMP_STUBv2=1
2025-08-14 12:45:04 us=355211 127.0.0.1:57676 TLS: move_session: dest=TM_ACTIVE src=TM_INITIAL reinit_src=1
2025-08-14 12:45:04 us=355250 127.0.0.1:57676 TLS: tls_multi_process: initial untrusted session promoted to trusted
2025-08-14 12:45:04 us=355502 127.0.0.1:57676 Control Channel: TLSv1.3, cipher TLSv1.3 TLS_AES_256_GCM_SHA384, peer certificate: 2048 bits RSA, signature: RSA-SHA256, peer temporary key: 253 bits X25519
2025-08-14 12:45:04 us=355526 127.0.0.1:57676 [EVO-EDEN-VPN-CA] Peer Connection Initiated with [AF_INET]127.0.0.1:57676
2025-08-14 12:45:04 us=355540 EVO-EDEN-VPN-CA/127.0.0.1:57676 MULTI_sva: pool returned IPv4=*********, IPv6=(Not enabled)
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Error</title>
</head>
<body>
<pre>Cannot POST /api/v1/internal/events/connect</pre>
</body>
</html>
2025-08-14 12:45:04 us=383852 EVO-EDEN-VPN-CA/127.0.0.1:57676 OPTIONS IMPORT: reading client specific options from: /tmp/openvpn_cc_5f0eaa820a7cb091288b7f544c2d7ccc.tmp
2025-08-14 12:45:04 us=384354 EVO-EDEN-VPN-CA/127.0.0.1:57676 MULTI: Learn: ********* -> EVO-EDEN-VPN-CA/127.0.0.1:57676
2025-08-14 12:45:04 us=384378 EVO-EDEN-VPN-CA/127.0.0.1:57676 MULTI: primary virtual IP for EVO-EDEN-VPN-CA/127.0.0.1:57676: *********
2025-08-14 12:45:04 us=384416 EVO-EDEN-VPN-CA/127.0.0.1:57676 Data Channel MTU parms [ mss_fix:1400 max_frag:0 tun_mtu:1500 tun_max_mtu:1600 headroom:136 payload:1768 tailroom:562 ET:0 ]
2025-08-14 12:45:04 us=384474 EVO-EDEN-VPN-CA/127.0.0.1:57676 Outgoing dynamic tls-crypt: Cipher 'AES-256-CTR' initialized with 256 bit key
2025-08-14 12:45:04 us=384491 EVO-EDEN-VPN-CA/127.0.0.1:57676 Outgoing dynamic tls-crypt: Using 256 bit message hash 'SHA256' for HMAC authentication
2025-08-14 12:45:04 us=384498 EVO-EDEN-VPN-CA/127.0.0.1:57676 Incoming dynamic tls-crypt: Cipher 'AES-256-CTR' initialized with 256 bit key
2025-08-14 12:45:04 us=384506 EVO-EDEN-VPN-CA/127.0.0.1:57676 Incoming dynamic tls-crypt: Using 256 bit message hash 'SHA256' for HMAC authentication
2025-08-14 12:45:04 us=384527 EVO-EDEN-VPN-CA/127.0.0.1:57676 Outgoing Data Channel: Cipher 'AES-256-GCM' initialized with 256 bit key
2025-08-14 12:45:04 us=384534 EVO-EDEN-VPN-CA/127.0.0.1:57676 Incoming Data Channel: Cipher 'AES-256-GCM' initialized with 256 bit key
2025-08-14 12:45:04 us=384584 EVO-EDEN-VPN-CA/127.0.0.1:57676 SENT CONTROL [EVO-EDEN-VPN-CA]: 'PUSH_REPLY,redirect-gateway def1 bypass-dhcp,dhcp-option DNS *******,dhcp-option DNS *******,route-gateway *********,topology subnet,ping 10,ping-restart 120,ifconfig ********* *************,peer-id 0,cipher AES-256-GCM,protocol-flags cc-exit tls-ekm dyn-tls-crypt,tun-mtu 1500' (status=1)
2025-08-14 12:45:05 us=591894 EVO-EDEN-VPN-CA/127.0.0.1:57676 Data Channel: cipher 'AES-256-GCM', peer-id: 0
2025-08-14 12:45:05 us=591974 EVO-EDEN-VPN-CA/127.0.0.1:57676 Timers: ping 10, ping-restart 240
2025-08-14 12:45:05 us=591987 EVO-EDEN-VPN-CA/127.0.0.1:57676 Protocol options: explicit-exit-notify 1, protocol-flags cc-exit tls-ekm dyn-tls-crypt
2025-08-14 12:46:29 us=549198 event_wait : Interrupted system call (fd=-1,code=4)
2025-08-14 12:46:29 us=549298 SENT CONTROL [EVO-EDEN-VPN-CA]: 'RESTART' (status=1)
2025-08-14 12:46:30 us=551684 MULTI: Connection attempt from 127.0.0.1:54257 ignored while server is shutting down
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Error</title>
</head>
<body>
<pre>Cannot POST /api/v1/internal/events/disconnect</pre>
</body>
</html>
2025-08-14 12:46:31 us=571568 TCP/UDP: Closing socket
2025-08-14 12:46:31 us=571632 Closing TUN/TAP interface
2025-08-14 12:46:31 us=571650 net_addr_v4_del: ********* dev tun0
2025-08-14 12:46:31 us=578332 SIGTERM[hard,] received, process exiting
2025-08-14 13:08:54 us=594599 Note: Kernel support for ovpn-dco missing, disabling data channel offload.
2025-08-14 13:08:54 us=595227 WARNING: Using --management on a TCP port WITHOUT passwords is STRONGLY discouraged and considered insecure
2025-08-14 13:08:54 us=595575 Current Parameter Settings:
2025-08-14 13:08:54 us=595600   config = '/etc/openvpn/server.conf'
2025-08-14 13:08:54 us=595612   mode = 1
2025-08-14 13:08:54 us=595621   persist_config = DISABLED
2025-08-14 13:08:54 us=595643   persist_mode = 1
2025-08-14 13:08:54 us=595653   show_ciphers = DISABLED
2025-08-14 13:08:54 us=595667   show_digests = DISABLED
2025-08-14 13:08:54 us=595678   show_engines = DISABLED
2025-08-14 13:08:54 us=595688   genkey = DISABLED
2025-08-14 13:08:54 us=595697   genkey_filename = '[UNDEF]'
2025-08-14 13:08:54 us=595712   key_pass_file = '[UNDEF]'
2025-08-14 13:08:54 us=595724   show_tls_ciphers = DISABLED
2025-08-14 13:08:54 us=595735   connect_retry_max = 0
2025-08-14 13:08:54 us=595744 Connection profiles [0]:
2025-08-14 13:08:54 us=595754   proto = udp
2025-08-14 13:08:54 us=595764   local = '[UNDEF]'
2025-08-14 13:08:54 us=595773   local_port = '1194'
2025-08-14 13:08:54 us=595782   remote = '[UNDEF]'
2025-08-14 13:08:54 us=595792   remote_port = '1194'
2025-08-14 13:08:54 us=595810   remote_float = DISABLED
2025-08-14 13:08:54 us=595821   bind_defined = DISABLED
2025-08-14 13:08:54 us=595831   bind_local = ENABLED
2025-08-14 13:08:54 us=595849   bind_ipv6_only = DISABLED
2025-08-14 13:08:54 us=595856   connect_retry_seconds = 1
2025-08-14 13:08:54 us=595862   connect_timeout = 120
2025-08-14 13:08:54 us=595869   socks_proxy_server = '[UNDEF]'
2025-08-14 13:08:54 us=595877   socks_proxy_port = '[UNDEF]'
2025-08-14 13:08:54 us=595885   tun_mtu = 1500
2025-08-14 13:08:54 us=595894   tun_mtu_defined = ENABLED
2025-08-14 13:08:54 us=595902   link_mtu = 1500
2025-08-14 13:08:54 us=595910   link_mtu_defined = DISABLED
2025-08-14 13:08:54 us=595919   tun_mtu_extra = 0
2025-08-14 13:08:54 us=595926   tun_mtu_extra_defined = DISABLED
2025-08-14 13:08:54 us=595933   tls_mtu = 1250
2025-08-14 13:08:54 us=595940   mtu_discover_type = -1
2025-08-14 13:08:54 us=595946   fragment = 0
2025-08-14 13:08:54 us=595953   mssfix = 1492
2025-08-14 13:08:54 us=595959   mssfix_encap = ENABLED
2025-08-14 13:08:54 us=595966   mssfix_fixed = DISABLED
2025-08-14 13:08:54 us=595974   explicit_exit_notification = 1
2025-08-14 13:08:54 us=595983   tls_auth_file = '[INLINE]'
2025-08-14 13:08:54 us=595991   key_direction = 0
2025-08-14 13:08:54 us=595999   tls_crypt_file = '[UNDEF]'
2025-08-14 13:08:54 us=596006   tls_crypt_v2_file = '[UNDEF]'
2025-08-14 13:08:54 us=596012 Connection profiles END
2025-08-14 13:08:54 us=596019   remote_random = DISABLED
2025-08-14 13:08:54 us=596028   ipchange = '[UNDEF]'
2025-08-14 13:08:54 us=596043   dev = 'tun'
2025-08-14 13:08:54 us=596052   dev_type = '[UNDEF]'
2025-08-14 13:08:54 us=596060   dev_node = '[UNDEF]'
2025-08-14 13:08:54 us=596068   tuntap_options.disable_dco = ENABLED
2025-08-14 13:08:54 us=596076   lladdr = '[UNDEF]'
2025-08-14 13:08:54 us=596083   topology = 3
2025-08-14 13:08:54 us=596091   ifconfig_local = '*********'
2025-08-14 13:08:54 us=596098   ifconfig_remote_netmask = '*************'
2025-08-14 13:08:54 us=596106   ifconfig_noexec = DISABLED
2025-08-14 13:08:54 us=596114   ifconfig_nowarn = DISABLED
2025-08-14 13:08:54 us=596122   ifconfig_ipv6_local = '[UNDEF]'
2025-08-14 13:08:54 us=596131   ifconfig_ipv6_netbits = 0
2025-08-14 13:08:54 us=596139   ifconfig_ipv6_remote = '[UNDEF]'
2025-08-14 13:08:54 us=596145   shaper = 0
2025-08-14 13:08:54 us=596152   mtu_test = 0
2025-08-14 13:08:54 us=596158   mlock = DISABLED
2025-08-14 13:08:54 us=596164   keepalive_ping = 10
2025-08-14 13:08:54 us=596191   keepalive_timeout = 120
2025-08-14 13:08:54 us=596198   inactivity_timeout = 0
2025-08-14 13:08:54 us=596204   session_timeout = 0
2025-08-14 13:08:54 us=596210   inactivity_minimum_bytes = 0
2025-08-14 13:08:54 us=596217   ping_send_timeout = 10
2025-08-14 13:08:54 us=596223   ping_rec_timeout = 240
2025-08-14 13:08:54 us=596230   ping_rec_timeout_action = 2
2025-08-14 13:08:54 us=596236   ping_timer_remote = DISABLED
2025-08-14 13:08:54 us=596250   remap_sigusr1 = 0
2025-08-14 13:08:54 us=596256   persist_tun = ENABLED
2025-08-14 13:08:54 us=596263   persist_local_ip = DISABLED
2025-08-14 13:08:54 us=596269   persist_remote_ip = DISABLED
2025-08-14 13:08:54 us=596276   persist_key = ENABLED
2025-08-14 13:08:54 us=596282   passtos = DISABLED
2025-08-14 13:08:54 us=596288   resolve_retry_seconds = 1000000000
2025-08-14 13:08:54 us=596294   resolve_in_advance = DISABLED
2025-08-14 13:08:54 us=596301   username = '[UNDEF]'
2025-08-14 13:08:54 us=596307   groupname = '[UNDEF]'
2025-08-14 13:08:54 us=596313   chroot_dir = '[UNDEF]'
2025-08-14 13:08:54 us=596319   cd_dir = '[UNDEF]'
2025-08-14 13:08:54 us=596326   writepid = '/var/run/openvpn/server.pid'
2025-08-14 13:08:54 us=596332   up_script = '[UNDEF]'
2025-08-14 13:08:54 us=596338   down_script = '[UNDEF]'
2025-08-14 13:08:54 us=596344   down_pre = DISABLED
2025-08-14 13:08:54 us=596351   up_restart = DISABLED
2025-08-14 13:08:54 us=596357   up_delay = DISABLED
2025-08-14 13:08:54 us=596363   daemon = ENABLED
2025-08-14 13:08:54 us=596369   log = ENABLED
2025-08-14 13:08:54 us=596376   suppress_timestamps = DISABLED
2025-08-14 13:08:54 us=596382   machine_readable_output = DISABLED
2025-08-14 13:08:54 us=596388   nice = 0
2025-08-14 13:08:54 us=596395   verbosity = 4
2025-08-14 13:08:54 us=596407   mute = 0
2025-08-14 13:08:54 us=596414   gremlin = 0
2025-08-14 13:08:54 us=596420   status_file = '/root/VPN/OpenVPN/logs/openvpn-status.log'
2025-08-14 13:08:54 us=596427   status_file_version = 3
2025-08-14 13:08:54 us=596433   status_file_update_freq = 60
2025-08-14 13:08:54 us=596439   occ = ENABLED
2025-08-14 13:08:54 us=596446   rcvbuf = 0
2025-08-14 13:08:54 us=596452   sndbuf = 0
2025-08-14 13:08:54 us=596459   mark = 0
2025-08-14 13:08:54 us=596465   sockflags = 0
2025-08-14 13:08:54 us=596471   fast_io = ENABLED
2025-08-14 13:08:54 us=596478   comp.alg = 0
2025-08-14 13:08:54 us=596484   comp.flags = 24
2025-08-14 13:08:54 us=596491   route_script = '[UNDEF]'
2025-08-14 13:08:54 us=596497   route_default_gateway = '*********'
2025-08-14 13:08:54 us=596504   route_default_metric = 0
2025-08-14 13:08:54 us=596510   route_noexec = DISABLED
2025-08-14 13:08:54 us=596516   route_delay = 0
2025-08-14 13:08:54 us=596523   route_delay_window = 30
2025-08-14 13:08:54 us=596534   route_delay_defined = DISABLED
2025-08-14 13:08:54 us=596541   route_nopull = DISABLED
2025-08-14 13:08:54 us=596547   route_gateway_via_dhcp = DISABLED
2025-08-14 13:08:54 us=596554   allow_pull_fqdn = DISABLED
2025-08-14 13:08:54 us=596562   management_addr = 'localhost'
2025-08-14 13:08:54 us=596575   management_port = '7505'
2025-08-14 13:08:54 us=596586   management_user_pass = '[UNDEF]'
2025-08-14 13:08:54 us=596594   management_log_history_cache = 100
2025-08-14 13:08:54 us=596601   management_echo_buffer_size = 100
2025-08-14 13:08:54 us=596609   management_client_user = '[UNDEF]'
2025-08-14 13:08:54 us=596617   management_client_group = '[UNDEF]'
2025-08-14 13:08:54 us=596624   management_flags = 4
2025-08-14 13:08:54 us=596632   shared_secret_file = '[UNDEF]'
2025-08-14 13:08:54 us=596640   key_direction = 0
2025-08-14 13:08:54 us=596647   ciphername = 'AES-256-GCM'
2025-08-14 13:08:54 us=596654   ncp_ciphers = 'AES-256-GCM:AES-128-GCM:CHACHA20-POLY1305'
2025-08-14 13:08:54 us=596661   authname = 'SHA256'
2025-08-14 13:08:54 us=596669   engine = DISABLED
2025-08-14 13:08:54 us=596676   replay = ENABLED
2025-08-14 13:08:54 us=596683   mute_replay_warnings = DISABLED
2025-08-14 13:08:54 us=596690   replay_window = 64
2025-08-14 13:08:54 us=596698   replay_time = 15
2025-08-14 13:08:54 us=596705   packet_id_file = '[UNDEF]'
2025-08-14 13:08:54 us=596712   test_crypto = DISABLED
2025-08-14 13:08:54 us=596719   tls_server = ENABLED
2025-08-14 13:08:54 us=596726   tls_client = DISABLED
2025-08-14 13:08:54 us=596734   ca_file = '/root/VPN/OpenVPN/easy-rsa/pki/ca.crt'
2025-08-14 13:08:54 us=596741   ca_path = '[UNDEF]'
2025-08-14 13:08:54 us=596749   dh_file = '/root/VPN/OpenVPN/easy-rsa/pki/dh.pem'
2025-08-14 13:08:54 us=596758   cert_file = '/root/VPN/OpenVPN/easy-rsa/pki/issued/server.crt'
2025-08-14 13:08:54 us=596766   extra_certs_file = '[UNDEF]'
2025-08-14 13:08:54 us=596774   priv_key_file = '/root/VPN/OpenVPN/easy-rsa/pki/private/server.key'
2025-08-14 13:08:54 us=596782   pkcs12_file = '[UNDEF]'
2025-08-14 13:08:54 us=596789   cipher_list = '[UNDEF]'
2025-08-14 13:08:54 us=596798   cipher_list_tls13 = '[UNDEF]'
2025-08-14 13:08:54 us=596804   tls_cert_profile = '[UNDEF]'
2025-08-14 13:08:54 us=596810   tls_verify = '[UNDEF]'
2025-08-14 13:08:54 us=596817   tls_export_peer_cert_dir = '[UNDEF]'
2025-08-14 13:08:54 us=596823   verify_x509_type = 0
2025-08-14 13:08:54 us=596829   verify_x509_name = '[UNDEF]'
2025-08-14 13:08:54 us=596836   crl_file = '[UNDEF]'
2025-08-14 13:08:54 us=596842   ns_cert_type = 0
2025-08-14 13:08:54 us=596848   remote_cert_ku[i] = 0
2025-08-14 13:08:54 us=596855   remote_cert_ku[i] = 0
2025-08-14 13:08:54 us=596861   remote_cert_ku[i] = 0
2025-08-14 13:08:54 us=596867   remote_cert_ku[i] = 0
2025-08-14 13:08:54 us=596874   remote_cert_ku[i] = 0
2025-08-14 13:08:54 us=596880   remote_cert_ku[i] = 0
2025-08-14 13:08:54 us=596886   remote_cert_ku[i] = 0
2025-08-14 13:08:54 us=596892   remote_cert_ku[i] = 0
2025-08-14 13:08:54 us=596899   remote_cert_ku[i] = 0
2025-08-14 13:08:54 us=596911   remote_cert_ku[i] = 0
2025-08-14 13:08:54 us=596919   remote_cert_ku[i] = 0
2025-08-14 13:08:54 us=596925   remote_cert_ku[i] = 0
2025-08-14 13:08:54 us=596932   remote_cert_ku[i] = 0
2025-08-14 13:08:54 us=596938   remote_cert_ku[i] = 0
2025-08-14 13:08:54 us=596944   remote_cert_ku[i] = 0
2025-08-14 13:08:54 us=596950   remote_cert_ku[i] = 0
2025-08-14 13:08:54 us=596957   remote_cert_eku = '[UNDEF]'
2025-08-14 13:08:54 us=596963   ssl_flags = 192
2025-08-14 13:08:54 us=596970   tls_timeout = 2
2025-08-14 13:08:54 us=596976   renegotiate_bytes = -1
2025-08-14 13:08:54 us=596982   renegotiate_packets = 0
2025-08-14 13:08:54 us=596989   renegotiate_seconds = 3600
2025-08-14 13:08:54 us=596995   handshake_window = 60
2025-08-14 13:08:54 us=597007   transition_window = 3600
2025-08-14 13:08:54 us=597013   single_session = DISABLED
2025-08-14 13:08:54 us=597020   push_peer_info = DISABLED
2025-08-14 13:08:54 us=597026   tls_exit = DISABLED
2025-08-14 13:08:54 us=597033   tls_crypt_v2_metadata = '[UNDEF]'
2025-08-14 13:08:54 us=597039   pkcs11_protected_authentication = DISABLED
2025-08-14 13:08:54 us=597045   pkcs11_protected_authentication = DISABLED
2025-08-14 13:08:54 us=597052   pkcs11_protected_authentication = DISABLED
2025-08-14 13:08:54 us=597058   pkcs11_protected_authentication = DISABLED
2025-08-14 13:08:54 us=597064   pkcs11_protected_authentication = DISABLED
2025-08-14 13:08:54 us=597071   pkcs11_protected_authentication = DISABLED
2025-08-14 13:08:54 us=597077   pkcs11_protected_authentication = DISABLED
2025-08-14 13:08:54 us=597083   pkcs11_protected_authentication = DISABLED
2025-08-14 13:08:54 us=597089   pkcs11_protected_authentication = DISABLED
2025-08-14 13:08:54 us=597096   pkcs11_protected_authentication = DISABLED
2025-08-14 13:08:54 us=597102   pkcs11_protected_authentication = DISABLED
2025-08-14 13:08:54 us=597108   pkcs11_protected_authentication = DISABLED
2025-08-14 13:08:54 us=597115   pkcs11_protected_authentication = DISABLED
2025-08-14 13:08:54 us=597121   pkcs11_protected_authentication = DISABLED
2025-08-14 13:08:54 us=597127   pkcs11_protected_authentication = DISABLED
2025-08-14 13:08:54 us=597133   pkcs11_protected_authentication = DISABLED
2025-08-14 13:08:54 us=597140   pkcs11_private_mode = 00000000
2025-08-14 13:08:54 us=597146   pkcs11_private_mode = 00000000
2025-08-14 13:08:54 us=597153   pkcs11_private_mode = 00000000
2025-08-14 13:08:54 us=597159   pkcs11_private_mode = 00000000
2025-08-14 13:08:54 us=597294   pkcs11_private_mode = 00000000
2025-08-14 13:08:54 us=597365   pkcs11_private_mode = 00000000
2025-08-14 13:08:54 us=597410   pkcs11_private_mode = 00000000
2025-08-14 13:08:54 us=597469   pkcs11_private_mode = 00000000
2025-08-14 13:08:54 us=597553   pkcs11_private_mode = 00000000
2025-08-14 13:08:54 us=597619   pkcs11_private_mode = 00000000
2025-08-14 13:08:54 us=597633   pkcs11_private_mode = 00000000
2025-08-14 13:08:54 us=597641   pkcs11_private_mode = 00000000
2025-08-14 13:08:54 us=597649   pkcs11_private_mode = 00000000
2025-08-14 13:08:54 us=597656   pkcs11_private_mode = 00000000
2025-08-14 13:08:54 us=597664   pkcs11_private_mode = 00000000
2025-08-14 13:08:54 us=597671   pkcs11_private_mode = 00000000
2025-08-14 13:08:54 us=597797   pkcs11_cert_private = DISABLED
2025-08-14 13:08:54 us=597806   pkcs11_cert_private = DISABLED
2025-08-14 13:08:54 us=597813   pkcs11_cert_private = DISABLED
2025-08-14 13:08:54 us=597819   pkcs11_cert_private = DISABLED
2025-08-14 13:08:54 us=597826   pkcs11_cert_private = DISABLED
2025-08-14 13:08:54 us=597832   pkcs11_cert_private = DISABLED
2025-08-14 13:08:54 us=597933   pkcs11_cert_private = DISABLED
2025-08-14 13:08:54 us=597939   pkcs11_cert_private = DISABLED
2025-08-14 13:08:54 us=597946   pkcs11_cert_private = DISABLED
2025-08-14 13:08:54 us=597952   pkcs11_cert_private = DISABLED
2025-08-14 13:08:54 us=597958   pkcs11_cert_private = DISABLED
2025-08-14 13:08:54 us=597965   pkcs11_cert_private = DISABLED
2025-08-14 13:08:54 us=597971   pkcs11_cert_private = DISABLED
2025-08-14 13:08:54 us=597977   pkcs11_cert_private = DISABLED
2025-08-14 13:08:54 us=597990   pkcs11_cert_private = DISABLED
2025-08-14 13:08:54 us=598120   pkcs11_cert_private = DISABLED
2025-08-14 13:08:54 us=598152   pkcs11_pin_cache_period = -1
2025-08-14 13:08:54 us=598159   pkcs11_id = '[UNDEF]'
2025-08-14 13:08:54 us=598272   pkcs11_id_management = DISABLED
2025-08-14 13:08:54 us=598285   server_network = *********
2025-08-14 13:08:54 us=598292   server_netmask = *************
2025-08-14 13:08:54 us=598299   server_network_ipv6 = ::
2025-08-14 13:08:54 us=598306   server_netbits_ipv6 = 0
2025-08-14 13:08:54 us=598313   server_bridge_ip = 0.0.0.0
2025-08-14 13:08:54 us=598319   server_bridge_netmask = 0.0.0.0
2025-08-14 13:08:54 us=598326   server_bridge_pool_start = 0.0.0.0
2025-08-14 13:08:54 us=598446   server_bridge_pool_end = 0.0.0.0
2025-08-14 13:08:54 us=598486   push_entry = 'redirect-gateway def1 bypass-dhcp'
2025-08-14 13:08:54 us=598493   push_entry = 'dhcp-option DNS *******'
2025-08-14 13:08:54 us=598546   push_entry = 'dhcp-option DNS *******'
2025-08-14 13:08:54 us=598553   push_entry = 'route-gateway *********'
2025-08-14 13:08:54 us=598559   push_entry = 'topology subnet'
2025-08-14 13:08:54 us=598566   push_entry = 'ping 10'
2025-08-14 13:08:54 us=598572   push_entry = 'ping-restart 120'
2025-08-14 13:08:54 us=598635   ifconfig_pool_defined = ENABLED
2025-08-14 13:08:54 us=598642   ifconfig_pool_start = *********
2025-08-14 13:08:54 us=598648   ifconfig_pool_end = *********54
2025-08-14 13:08:54 us=598726   ifconfig_pool_netmask = *************
2025-08-14 13:08:54 us=598733   ifconfig_pool_persist_filename = '/root/VPN/OpenVPN/logs/ipp.txt'
2025-08-14 13:08:54 us=598739   ifconfig_pool_persist_refresh_freq = 600
2025-08-14 13:08:54 us=598746   ifconfig_ipv6_pool_defined = DISABLED
2025-08-14 13:08:54 us=598860   ifconfig_ipv6_pool_base = ::
2025-08-14 13:08:54 us=598908   ifconfig_ipv6_pool_netbits = 0
2025-08-14 13:08:54 us=598984   n_bcast_buf = 256
2025-08-14 13:08:54 us=599019   tcp_queue_limit = 64
2025-08-14 13:08:54 us=599026   real_hash_size = 256
2025-08-14 13:08:54 us=599032   virtual_hash_size = 256
2025-08-14 13:08:54 us=599039   client_connect_script = '/root/VPN/OpenVPN/scripts/client-connect.sh'
2025-08-14 13:08:54 us=599045   learn_address_script = '[UNDEF]'
2025-08-14 13:08:54 us=599052   client_disconnect_script = '/root/VPN/OpenVPN/scripts/client-disconnect.sh'
2025-08-14 13:08:54 us=599058   client_crresponse_script = '[UNDEF]'
2025-08-14 13:08:54 us=599065   client_config_dir = '[UNDEF]'
2025-08-14 13:08:54 us=599071   ccd_exclusive = DISABLED
2025-08-14 13:08:54 us=599186   tmp_dir = '/tmp'
2025-08-14 13:08:54 us=599196   push_ifconfig_defined = DISABLED
2025-08-14 13:08:54 us=599203   push_ifconfig_local = 0.0.0.0
2025-08-14 13:08:54 us=599216   push_ifconfig_remote_netmask = 0.0.0.0
2025-08-14 13:08:54 us=599224   push_ifconfig_ipv6_defined = DISABLED
2025-08-14 13:08:54 us=599345   push_ifconfig_ipv6_local = ::/0
2025-08-14 13:08:54 us=599387   push_ifconfig_ipv6_remote = ::
2025-08-14 13:08:54 us=599396   enable_c2c = ENABLED
2025-08-14 13:08:54 us=599404   duplicate_cn = ENABLED
2025-08-14 13:08:54 us=599412   cf_max = 0
2025-08-14 13:08:54 us=599420   cf_per = 0
2025-08-14 13:08:54 us=599500   cf_initial_max = 100
2025-08-14 13:08:54 us=599566   cf_initial_per = 10
2025-08-14 13:08:54 us=599574   max_clients = 1024
2025-08-14 13:08:54 us=599580   max_routes_per_client = 256
2025-08-14 13:08:54 us=599587   auth_user_pass_verify_script = '[UNDEF]'
2025-08-14 13:08:54 us=599640   auth_user_pass_verify_script_via_file = DISABLED
2025-08-14 13:08:54 us=599647   auth_token_generate = DISABLED
2025-08-14 13:08:54 us=599653   force_key_material_export = DISABLED
2025-08-14 13:08:54 us=599660   auth_token_lifetime = 0
2025-08-14 13:08:54 us=599666   auth_token_secret_file = '[UNDEF]'
2025-08-14 13:08:54 us=599729   port_share_host = '[UNDEF]'
2025-08-14 13:08:54 us=599736   port_share_port = '[UNDEF]'
2025-08-14 13:08:54 us=599742   vlan_tagging = DISABLED
2025-08-14 13:08:54 us=599797   vlan_accept = all
2025-08-14 13:08:54 us=599810   vlan_pvid = 1
2025-08-14 13:08:54 us=599817   client = DISABLED
2025-08-14 13:08:54 us=599823   pull = DISABLED
2025-08-14 13:08:54 us=599923   auth_user_pass_file = '[UNDEF]'
2025-08-14 13:08:54 us=599932 OpenVPN 2.6.14 x86_64-pc-linux-gnu [SSL (OpenSSL)] [LZO] [LZ4] [EPOLL] [PKCS11] [MH/PKTINFO] [AEAD] [DCO]
2025-08-14 13:08:54 us=599952 library versions: OpenSSL 3.0.13 30 Jan 2024, LZO 2.10
2025-08-14 13:08:54 us=599970 DCO version: N/A
2025-08-14 13:08:54 us=601723 setsockopt(IPV6_V6ONLY=0)
2025-08-14 13:08:54 us=601776 MANAGEMENT: TCP Socket listening on [AF_INET6]::1:7505
2025-08-14 13:08:54 us=601812 Need hold release from management interface, waiting...
2025-08-14 13:09:00 us=848422 MANAGEMENT: Client connected from [AF_INET6]::1:55056
2025-08-14 13:09:00 us=848676 MANAGEMENT: CMD 'hold release'
2025-08-14 13:09:00 us=848939 WARNING: --ifconfig-pool-persist will not work with --duplicate-cn
2025-08-14 13:09:00 us=849568 net_route_v4_best_gw query: dst 0.0.0.0
2025-08-14 13:09:00 us=849805 net_route_v4_best_gw result: via ************ dev eth0
2025-08-14 13:09:00 us=849915 NOTE: the current --script-security setting may allow this configuration to call user-defined scripts
2025-08-14 13:09:00 us=852648 Diffie-Hellman initialized with 2048 bit key
2025-08-14 13:09:00 us=855233 Outgoing Control Channel Authentication: Using 256 bit message hash 'SHA256' for HMAC authentication
2025-08-14 13:09:00 us=855267 Incoming Control Channel Authentication: Using 256 bit message hash 'SHA256' for HMAC authentication
2025-08-14 13:09:00 us=855288 TLS-Auth MTU parms [ mss_fix:0 max_frag:0 tun_mtu:1250 tun_max_mtu:0 headroom:126 payload:1600 tailroom:126 ET:0 ]
2025-08-14 13:09:00 us=856483 TUN/TAP device tun0 opened
2025-08-14 13:09:00 us=856948 do_ifconfig, ipv4=1, ipv6=0
2025-08-14 13:09:00 us=856977 net_iface_mtu_set: mtu 1500 for tun0
2025-08-14 13:09:00 us=857022 net_iface_up: set tun0 up
2025-08-14 13:09:00 us=858224 net_addr_v4_add: *********/24 dev tun0
2025-08-14 13:09:00 us=858740 Data Channel MTU parms [ mss_fix:0 max_frag:0 tun_mtu:1500 tun_max_mtu:1600 headroom:136 payload:1768 tailroom:562 ET:0 ]
2025-08-14 13:09:00 us=859224 Could not determine IPv4/IPv6 protocol. Using AF_INET
2025-08-14 13:09:00 us=859704 Socket Buffers: R=[212992->212992] S=[212992->212992]
2025-08-14 13:09:00 us=859964 UDPv4 link local (bound): [AF_INET][undef]:1194
2025-08-14 13:09:00 us=859982 UDPv4 link remote: [AF_UNSPEC]
2025-08-14 13:09:00 us=860000 MULTI: multi_init called, r=256 v=256
2025-08-14 13:09:00 us=860029 IFCONFIG POOL IPv4: base=********* size=253
2025-08-14 13:09:00 us=860042 IFCONFIG POOL LIST
2025-08-14 13:09:00 us=860070 Initialization Sequence Completed
2025-08-14 13:09:52 us=377719 MANAGEMENT: Client disconnected
2025-08-14 13:10:29 us=489495 event_wait : Interrupted system call (fd=-1,code=4)
2025-08-14 13:10:31 us=490527 TCP/UDP: Closing socket
2025-08-14 13:10:31 us=490610 Closing TUN/TAP interface
2025-08-14 13:10:31 us=490622 net_addr_v4_del: ********* dev tun0
2025-08-14 13:10:31 us=498366 SIGTERM[hard,] received, process exiting
