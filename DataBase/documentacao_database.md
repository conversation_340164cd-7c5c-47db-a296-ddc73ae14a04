# Documentação do Banco de Dados PostgreSQL - Sistema VPN

## 📋 Visão Geral

Este documento contém instruções detalhadas para implementar o banco de dados PostgreSQL para o sistema de monitoramento VPN corporativo. O banco foi projetado para armazenar logs completos de conexão, métricas de disponibilidade e eventos de rede com alta performance e resistência a falhas.

## 🔧 Configuração Inicial Crítica

### ⚠️ PONTOS CRÍTICOS DE ATENÇÃO

1. **TIMEZONE OBRIGATÓRIO**: Sempre configurar timezone para 'America/Sao_Paulo'
2. **EXTENSÕES ESSENCIAIS**: uuid-ossp e pgcrypto devem ser habilitadas
3. **CONSTRAINTS**: Todas as constraints devem ser implementadas para evitar dados inconsistentes
4. **ÍNDICES**: Índices estratégicos são obrigatórios para performance
5. **TRIGGERS**: Triggers automáticos são essenciais para integridade dos dados

### Dados de Conexão
```
Host: ************
Porta: 5432
Usuário: postgres
Senha: ab3780bd73ee4e2804d566ce6fd96209
Banco: vpnetens
```

## 📊 Estrutura das Tabelas

### 1. Tabela: vpn_clients
**Finalidade**: Cadastro de clientes VPN com informações básicas e status

#### Colunas Críticas:
- `id` (UUID, PK): Identificador único - **OBRIGATÓRIO usar uuid_generate_v4()**
- `client_name` (VARCHAR(100), UNIQUE): Nome do cliente - **DEVE ser único no sistema**
- `common_name` (VARCHAR(100), UNIQUE): Nome comum do certificado - **DEVE ser único**
- `status` (VARCHAR(20)): Status do cliente - **DEVE ser 'active', 'inactive', 'suspended', 'revoked'**
- `created_at` (TIMESTAMPTZ): Data de criação - **DEVE usar timezone Brasil**
- `updated_at` (TIMESTAMPTZ): Data de atualização - **DEVE ser atualizado automaticamente**

#### ⚠️ Pontos de Atenção:
- **FALHA CRÍTICA**: Se client_name ou common_name não forem únicos, causará conflitos
- **FALHA CRÍTICA**: Se status não estiver nos valores permitidos, causará inconsistência
- **FALHA CRÍTICA**: Se timezone não for configurado, timestamps serão incorretos

### 2. Tabela: vpn_sessions
**Finalidade**: Registro de todas as sessões de conexão VPN com timestamps precisos

#### Colunas Críticas:
- `id` (UUID, PK): Identificador único da sessão
- `client_id` (UUID, FK): Referência ao cliente - **DEVE existir em vpn_clients**
- `client_name` (VARCHAR(100)): Nome do cliente - **DEVE ser consistente com vpn_clients**
- `real_address` (INET): IP real do cliente - **OBRIGATÓRIO para rastreamento**
- `virtual_address` (INET): IP virtual atribuído - **PODE ser NULL inicialmente**
- `connected_at` (TIMESTAMPTZ): Timestamp de conexão - **CRÍTICO para cálculos de uptime**
- `disconnected_at` (TIMESTAMPTZ): Timestamp de desconexão - **NULL para sessões ativas**
- `duration_seconds` (INTEGER): Duração em segundos - **Calculado automaticamente**
- `session_date` (DATE): Data da sessão - **Atualizado automaticamente via trigger**
- `is_active` (BOOLEAN): Status ativo - **Atualizado automaticamente via trigger**

#### ⚠️ Pontos de Atenção:
- **FALHA CRÍTICA**: Se connected_at não tiver timezone, cálculos serão incorretos
- **FALHA CRÍTICA**: Se triggers não funcionarem, campos calculados ficarão desatualizados
- **FALHA CRÍTICA**: Se client_id não existir, causará erro de integridade referencial
- **ANOMALIA**: Se duration_seconds for negativo, indica problema nos timestamps

### 3. Tabela: network_events
**Finalidade**: Registro de eventos de rede entre clientes (SSH, VNC, etc.)

#### Colunas Críticas:
- `session_id` (UUID, FK): Referência à sessão - **DEVE existir em vpn_sessions**
- `source_client` (VARCHAR(100)): Cliente origem - **OBRIGATÓRIO**
- `destination_client` (VARCHAR(100)): Cliente destino - **OBRIGATÓRIO**
- `source_ip` (INET): IP origem - **OBRIGATÓRIO**
- `destination_ip` (INET): IP destino - **OBRIGATÓRIO**
- `event_type` (VARCHAR(50)): Tipo do evento - **'ssh', 'vnc', 'ping', 'file_transfer', 'other'**
- `detected_at` (TIMESTAMPTZ): Timestamp de detecção - **CRÍTICO para análise temporal**
- `event_date` (DATE): Data do evento - **Atualizado automaticamente via trigger**

#### ⚠️ Pontos de Atenção:
- **FALHA CRÍTICA**: Se session_id não existir, causará erro de integridade
- **FALHA CRÍTICA**: Se portas forem <= 0, violará constraint
- **ANOMALIA**: Se bytes_transferred for negativo, indica erro na coleta

### 4. Tabela: client_status_log
**Finalidade**: Log de mudanças de status dos clientes (online/offline)

#### Colunas Críticas:
- `client_name` (VARCHAR(100)): Nome do cliente - **DEVE ser consistente**
- `client_id` (UUID, FK): Referência ao cliente - **DEVE existir em vpn_clients**
- `status` (VARCHAR(20)): Status - **DEVE ser 'online', 'offline', 'connecting', 'disconnecting'**
- `status_changed_at` (TIMESTAMPTZ): Timestamp da mudança - **CRÍTICO para cálculos de uptime**
- `session_id` (UUID, FK): Referência à sessão - **PODE ser NULL**

#### ⚠️ Pontos de Atenção:
- **FALHA CRÍTICA**: Se status não estiver nos valores permitidos, causará inconsistência
- **FALHA CRÍTICA**: Se timestamps não tiverem timezone, cálculos serão incorretos
- **ANOMALIA**: Se houver gaps nos logs de status, uptime será impreciso

### 5. Tabela: system_events
**Finalidade**: Eventos do sistema (alertas, reboots, erros)

#### Colunas Críticas:
- `event_type` (VARCHAR(50)): Tipo do evento - **'alert', 'reboot', 'config_change', 'maintenance', 'error'**
- `severity` (VARCHAR(20)): Severidade - **DEVE ser 'info', 'warning', 'error', 'critical'**
- `title` (VARCHAR(200)): Título do evento - **OBRIGATÓRIO, mínimo 5 caracteres**
- `event_data` (JSONB): Dados específicos - **USAR JSONB para performance**
- `occurred_at` (TIMESTAMPTZ): Timestamp do evento - **CRÍTICO para auditoria**

#### ⚠️ Pontos de Atenção:
- **FALHA CRÍTICA**: Se severity não estiver nos valores permitidos, causará inconsistência
- **FALHA CRÍTICA**: Se title for muito curto, violará constraint
- **ANOMALIA**: Se event_data não for JSON válido, causará erro

### 6. Tabela: daily_metrics
**Finalidade**: Métricas agregadas diárias por cliente

#### Colunas Críticas:
- `metric_date` (DATE): Data da métrica - **OBRIGATÓRIO**
- `client_name` (VARCHAR(100)): Nome do cliente - **OBRIGATÓRIO**
- `total_connections` (INTEGER): Total de conexões - **DEVE ser >= 0**
- `total_online_seconds` (INTEGER): Segundos online - **CRÍTICO para disponibilidade**
- `total_bytes_sent` (BIGINT): Bytes enviados - **DEVE ser >= 0**
- `total_bytes_received` (BIGINT): Bytes recebidos - **DEVE ser >= 0**

#### ⚠️ Pontos de Atenção:
- **FALHA CRÍTICA**: Se metric_date + client_name não forem únicos, causará duplicatas
- **ANOMALIA**: Se valores forem negativos, indica erro nos cálculos
- **FALHA CRÍTICA**: Se não for atualizado diariamente, métricas ficarão desatualizadas

## 🔧 Funções Críticas

### 1. calculate_client_availability()
**Finalidade**: Calcular disponibilidade de um cliente em período específico

#### Parâmetros:
- `p_client_name`: Nome do cliente
- `p_start_date`: Data inicial
- `p_end_date`: Data final

#### ⚠️ Pontos Críticos:
- **FALHA CRÍTICA**: Se datas forem inválidas, retornará 0%
- **ANOMALIA**: Se não houver sessões finalizadas, disponibilidade será 0%
- **IMPORTANTE**: Considera apenas sessões com status 'disconnected'

### 2. update_daily_metrics()
**Finalidade**: Atualizar métricas diárias de um cliente

#### ⚠️ Pontos Críticos:
- **FALHA CRÍTICA**: Se não for executada diariamente, métricas ficarão desatualizadas
- **FALHA CRÍTICA**: Se houver erro na função, métricas serão inconsistentes
- **IMPORTANTE**: Usa UPSERT para evitar duplicatas

## 🚀 Triggers Essenciais

### 1. update_session_fields()
**Finalidade**: Atualizar campos calculados em vpn_sessions

#### ⚠️ Pontos Críticos:
- **FALHA CRÍTICA**: Se trigger falhar, session_date e is_active ficarão incorretos
- **FALHA CRÍTICA**: Se duration_seconds não for calculado, métricas serão imprecisas

### 2. update_network_event_fields()
**Finalidade**: Atualizar event_date em network_events

#### ⚠️ Pontos Críticos:
- **FALHA CRÍTICA**: Se trigger falhar, event_date ficará NULL
- **ANOMALIA**: Se detected_at for NULL, causará erro

### 3. log_client_status_change()
**Finalidade**: Registrar mudanças de status automaticamente

#### ⚠️ Pontos Críticos:
- **FALHA CRÍTICA**: Se trigger falhar, logs de status não serão criados
- **FALHA CRÍTICA**: Se não registrar desconexões, uptime será impreciso

## 📊 Índices Estratégicos

### ⚠️ ÍNDICES OBRIGATÓRIOS PARA PERFORMANCE

1. **vpn_sessions**:
   - `idx_vpn_sessions_client_date` - **CRÍTICO para consultas por cliente e data**
   - `idx_vpn_sessions_active` - **CRÍTICO para consultas de sessões ativas**

2. **client_status_log**:
   - `idx_client_status_log_client_date` - **CRÍTICO para cálculos de uptime**
   - `idx_client_status_log_status` - **IMPORTANTE para filtros por status**

3. **network_events**:
   - `idx_network_events_date_type` - **CRÍTICO para análise temporal**
   - `idx_network_events_session` - **IMPORTANTE para joins com sessões**

#### ⚠️ Pontos Críticos:
- **FALHA CRÍTICA**: Sem índices, consultas serão extremamente lentas
- **FALHA CRÍTICA**: Índices em campos de data são obrigatórios
- **ANOMALIA**: Se índices não forem criados, sistema será inutilizável com muitos dados

## 🔒 Constraints e Validações

### ⚠️ CONSTRAINTS OBRIGATÓRIAS

1. **Unicidade**:
   - `vpn_clients.client_name` - **DEVE ser único**
   - `vpn_clients.common_name` - **DEVE ser único**
   - `daily_metrics(metric_date, client_name)` - **DEVE ser único**

2. **Integridade Referencial**:
   - `vpn_sessions.client_id` → `vpn_clients.id` - **OBRIGATÓRIO**
   - `network_events.session_id` → `vpn_sessions.id` - **OBRIGATÓRIO**
   - `client_status_log.client_id` → `vpn_clients.id` - **OBRIGATÓRIO**

3. **Validações de Dados**:
   - Valores negativos em bytes e duração - **PROIBIDOS**
   - Status inválidos - **PROIBIDOS**
   - Timestamps inconsistentes - **PROIBIDOS**

#### ⚠️ Pontos Críticos:
- **FALHA CRÍTICA**: Sem constraints, dados inconsistentes serão inseridos
- **FALHA CRÍTICA**: Integridade referencial é essencial para consistência
- **ANOMALIA**: Dados inválidos causarão erros nos cálculos

## 🚨 Procedimentos de Implementação

### 1. Ordem de Criação OBRIGATÓRIA:

```sql
-- 1. Habilitar extensões
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- 2. Configurar timezone
SET timezone = 'America/Sao_Paulo';

-- 3. Criar tabelas na ordem:
-- vpn_clients (primeiro - sem dependências)
-- vpn_sessions (depende de vpn_clients)
-- network_events (depende de vpn_sessions)
-- client_status_log (depende de vpn_clients e vpn_sessions)
-- system_events (independente)
-- daily_metrics (independente)

-- 4. Criar índices
-- 5. Criar funções
-- 6. Criar triggers
```

### 2. Verificações Obrigatórias:

```sql
-- Verificar se todas as tabelas foram criadas
\dt

-- Verificar se extensões estão ativas
SELECT extname FROM pg_extension WHERE extname IN ('uuid-ossp', 'pgcrypto');

-- Verificar se funções foram criadas
SELECT routine_name FROM information_schema.routines
WHERE routine_schema = 'public'
AND routine_name IN ('calculate_client_availability', 'update_daily_metrics');

-- Verificar se triggers estão ativos
SELECT trigger_name, event_object_table
FROM information_schema.triggers
WHERE trigger_schema = 'public';
```

### ⚠️ FALHAS COMUNS E SOLUÇÕES:

1. **Erro "generation expression is not immutable"**:
   - **Causa**: Campos GENERATED com funções não imutáveis
   - **Solução**: Usar triggers em vez de campos GENERATED

2. **Erro "relation does not exist"**:
   - **Causa**: Ordem incorreta de criação das tabelas
   - **Solução**: Criar tabelas na ordem de dependência

3. **Erro de timezone**:
   - **Causa**: Timezone não configurado
   - **Solução**: Sempre executar `SET timezone = 'America/Sao_Paulo'`

4. **Performance lenta**:
   - **Causa**: Índices não criados
   - **Solução**: Criar todos os índices estratégicos

## 📈 Monitoramento e Manutenção

### Comandos de Monitoramento:

```sql
-- Verificar tamanho das tabelas
SELECT schemaname, tablename,
       pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as tamanho
FROM pg_tables WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Verificar número de registros
SELECT 'vpn_sessions' as tabela, COUNT(*) as registros FROM vpn_sessions
UNION ALL SELECT 'client_status_log', COUNT(*) FROM client_status_log
UNION ALL SELECT 'network_events', COUNT(*) FROM network_events;

-- Verificar disponibilidade de um cliente
SELECT calculate_client_availability('nome_cliente', '2024-01-01', '2024-01-31');
```

### ⚠️ ALERTAS CRÍTICOS:

- **Se tabelas crescerem > 100MB**: Implementar particionamento
- **Se consultas > 5 segundos**: Verificar índices
- **Se triggers falharem**: Dados ficarão inconsistentes
- **Se funções retornarem NULL**: Verificar parâmetros de entrada

---

## ✅ Checklist de Implementação

- [ ] Extensões uuid-ossp e pgcrypto habilitadas
- [ ] Timezone configurado para 'America/Sao_Paulo'
- [ ] Todas as 6 tabelas criadas na ordem correta
- [ ] Todos os índices estratégicos criados
- [ ] Todas as 3 funções principais criadas
- [ ] Todos os triggers essenciais criados
- [ ] Constraints de integridade validadas
- [ ] Testes de inserção realizados
- [ ] Monitoramento de performance configurado
- [ ] Backup e recovery testados

**⚠️ IMPORTANTE**: Este banco de dados é CRÍTICO para o funcionamento do sistema VPN. Qualquer falha na implementação pode causar perda de dados de monitoramento e métricas incorretas de disponibilidade.
