# 📋 Implementação Fase 5: Integração Completa OpenVPN + Frontend + Backend

## 🎯 **OBJETIVO DA FASE 5**

Integrar completamente a OpenVPN com o Frontend e Backend para criar um sistema unificado de monitoramento e gerenciamento VPN, onde todos os dados, logs e indicadores sejam exibidos em tempo real no Frontend.

## 🚨 **ESTADO ATUAL E CONFIGURAÇÕES PENDENTES**

### **✅ COMPONENTES JÁ FUNCIONAIS:**
1. **OpenVPN Server**: ✅ Rodando (daemon manual, porta 1194, interface tun0)
2. **API OpenVPN**: ✅ Funcionando (container API-VPN, porta 8080)
3. **Frontend React**: ✅ Acessível (https://vpn.evo-eden.site)
4. **Backend Go**: ✅ Processando logs (container Swarm vpn-backend)
5. **PostgreSQL**: ✅ Operacional (container Swarm postgres)
6. **Traefik**: ✅ SSL e roteamento funcionando

### **⚠️ CONFIGURAÇÕES PENDENTES CRÍTICAS:**

#### **1. OpenVPN - Scripts de Eventos NÃO CONFIGURADOS**
```bash
# PROBLEMA: OpenVPN rodando sem scripts de eventos
# IMPACTO: Backend não recebe eventos VPN reais
# SOLUÇÃO: Configurar client-connect/disconnect scripts

# Configuração atual em /etc/openvpn/server.conf:
# ❌ FALTAM estas linhas:
client-connect /root/VPN/OpenVPN/scripts/client-connect.sh
client-disconnect /root/VPN/OpenVPN/scripts/client-disconnect.sh
management 127.0.0.1 7505
script-security 3
```

#### **2. Backend - Endpoints de Integração PENDENTES**
```bash
# PROBLEMA: Backend não tem endpoints para receber eventos OpenVPN
# IMPACTO: Dados simulados no frontend
# SOLUÇÃO: Implementar handlers de integração

# Endpoints necessários:
POST /api/v1/internal/events/connect
POST /api/v1/internal/events/disconnect
GET  /api/v1/vpn/realtime-status
POST /api/v1/vpn/clients/:name/disconnect
```

#### **3. Frontend - Integração WebSocket PENDENTE**
```bash
# PROBLEMA: Frontend usa dados simulados
# IMPACTO: Não mostra dados reais da VPN
# SOLUÇÃO: Conectar ao backend via WebSocket

# Configurações necessárias:
- WebSocket connection para wss://vpn.evo-eden.site/api/v1/ws
- Substituir dados simulados por dados reais
- Implementar notificações em tempo real
```

#### **4. Management Interface INATIVA**
```bash
# PROBLEMA: Management interface não configurada
# IMPACTO: Não é possível controlar OpenVPN remotamente
# SOLUÇÃO: Ativar management na porta 7505

# Verificação atual:
netstat -tulpn | grep 7505  # Deve retornar vazio (não configurado)
```

### **🎯 PRIORIDADES DE IMPLEMENTAÇÃO:**
1. **CRÍTICO**: Configurar scripts de eventos OpenVPN
2. **CRÍTICO**: Implementar endpoints de integração backend
3. **IMPORTANTE**: Ativar management interface
4. **IMPORTANTE**: Conectar frontend ao backend
5. **DESEJÁVEL**: Implementar controle remoto de clientes

---

## 🏗️ **ARQUITETURA DE INTEGRAÇÃO**

### **Fluxo de Dados Completo**
```
OpenVPN Server ──→ Logs/Status ──→ Backend Parser ──→ PostgreSQL
                                        ↓
Frontend ←── WebSocket ←── Backend API ←── Processamento
```

### **Componentes da Integração**
1. **OpenVPN Server**: Geração de logs e status
2. **Backend Parser**: Processamento em tempo real
3. **PostgreSQL**: Armazenamento estruturado
4. **Backend API**: Endpoints para dados VPN
5. **Frontend**: Interface unificada
6. **WebSocket**: Notificações tempo real

---

## 🔧 **IMPLEMENTAÇÕES NECESSÁRIAS**

### **1. Configuração OpenVPN para Integração**

#### **1.1 Configuração de Logs Detalhados - ATUALIZAÇÃO NECESSÁRIA**

**🚨 CONFIGURAÇÃO ATUAL EM /etc/openvpn/server.conf:**
```bash
# Configuração atual (FUNCIONAL mas INCOMPLETA):
status /root/VPN/OpenVPN/logs/openvpn-status.log
log-append /root/VPN/OpenVPN/logs/openvpn.log
verb 3
management localhost 7505
script-security 2
```

**✅ CONFIGURAÇÃO NECESSÁRIA PARA FASE 5:**
```bash
# ADICIONAR estas linhas ao /etc/openvpn/server.conf:

# Logs de conexão detalhados (CRÍTICO - FALTANDO)
client-connect /root/VPN/OpenVPN/scripts/client-connect.sh
client-disconnect /root/VPN/OpenVPN/scripts/client-disconnect.sh

# Status detalhado para parser (MELHORAR)
status /root/VPN/OpenVPN/logs/status.log 10
status-version 3

# Logs mais verbosos (MELHORAR)
verb 4

# Script security para permitir scripts (ATUALIZAR)
script-security 3

# Management interface melhorada (MELHORAR)
management 127.0.0.1 7505
management-log-cache 100
management-hold

# Log de eventos separado (NOVO)
log /root/VPN/OpenVPN/logs/openvpn.log
log-append
```

**🔧 COMANDO PARA APLICAR CONFIGURAÇÃO:**
```bash
# 1. Parar OpenVPN atual
pkill openvpn

# 2. Backup da configuração atual
cp /etc/openvpn/server.conf /etc/openvpn/server.conf.backup

# 3. Adicionar configurações da Fase 5
cat >> /etc/openvpn/server.conf << 'EOF'

# === CONFIGURAÇÕES FASE 5 - INTEGRAÇÃO ===
# Scripts de eventos para integração
client-connect /root/VPN/OpenVPN/scripts/client-connect.sh
client-disconnect /root/VPN/OpenVPN/scripts/client-disconnect.sh

# Status detalhado
status-version 3

# Logs verbosos
verb 4

# Script security
script-security 3

# Management interface melhorada
management-log-cache 100
management-hold
EOF

# 4. Reiniciar OpenVPN
openvpn --daemon --config /etc/openvpn/server.conf --writepid /var/run/openvpn/server.pid
```

#### **1.2 Scripts de Eventos OpenVPN - IMPLEMENTAÇÃO OBRIGATÓRIA**

**🚨 STATUS ATUAL:**
```bash
# Scripts existem mas NÃO ESTÃO CONFIGURADOS no OpenVPN:
ls -la /root/VPN/OpenVPN/scripts/
# client-connect.sh, client-disconnect.sh existem mas não são executados
```

**✅ IMPLEMENTAÇÃO NECESSÁRIA:**

**1. Criar Script client-connect.sh:**
```bash
# Criar /root/VPN/OpenVPN/scripts/client-connect.sh
cat > /root/VPN/OpenVPN/scripts/client-connect.sh << 'EOF'
#!/bin/bash
set -e

# Variáveis do OpenVPN disponíveis:
# $common_name - Nome do cliente
# $trusted_ip - IP externo do cliente
# $ifconfig_pool_remote_ip - IP VPN atribuído

CLIENT_NAME="$common_name"
EXTERNAL_IP="$trusted_ip"
VPN_IP="$ifconfig_pool_remote_ip"
TIMESTAMP=$(date -Iseconds)

# Log local
echo "$(date '+%Y-%m-%d %H:%M:%S') CLIENT_CONNECT: $CLIENT_NAME from $EXTERNAL_IP assigned $VPN_IP" >> /root/VPN/OpenVPN/logs/events.log

# Notificar backend (CRÍTICO para integração)
curl -s -X POST http://localhost:8080/api/v1/internal/events/connect \
  -H "Content-Type: application/json" \
  -H "X-Internal-Token: vpn_internal_secret_2024" \
  -d "{
    \"client_name\": \"$CLIENT_NAME\",
    \"external_ip\": \"$EXTERNAL_IP\",
    \"vpn_ip\": \"$VPN_IP\",
    \"timestamp\": \"$TIMESTAMP\",
    \"event_type\": \"connect\"
  }" || echo "Failed to notify backend"

exit 0
EOF

# Dar permissão de execução
chmod +x /root/VPN/OpenVPN/scripts/client-connect.sh
```

**2. Criar Script client-disconnect.sh:**
```bash
# Criar /root/VPN/OpenVPN/scripts/client-disconnect.sh
cat > /root/VPN/OpenVPN/scripts/client-disconnect.sh << 'EOF'
#!/bin/bash
set -e

# Variáveis do OpenVPN disponíveis:
# $common_name - Nome do cliente
# $bytes_received - Bytes recebidos
# $bytes_sent - Bytes enviados
# $time_duration - Duração da sessão em segundos

CLIENT_NAME="$common_name"
BYTES_RECEIVED="$bytes_received"
BYTES_SENT="$bytes_sent"
DURATION="$time_duration"
TIMESTAMP=$(date -Iseconds)

# Log local
echo "$(date '+%Y-%m-%d %H:%M:%S') CLIENT_DISCONNECT: $CLIENT_NAME (RX: $BYTES_RECEIVED, TX: $BYTES_SENT, Duration: ${DURATION}s)" >> /root/VPN/OpenVPN/logs/events.log

# Notificar backend (CRÍTICO para integração)
curl -s -X POST http://localhost:8080/api/v1/internal/events/disconnect \
  -H "Content-Type: application/json" \
  -H "X-Internal-Token: vpn_internal_secret_2024" \
  -d "{
    \"client_name\": \"$CLIENT_NAME\",
    \"bytes_received\": $BYTES_RECEIVED,
    \"bytes_sent\": $BYTES_SENT,
    \"duration\": $DURATION,
    \"timestamp\": \"$TIMESTAMP\",
    \"event_type\": \"disconnect\"
  }" || echo "Failed to notify backend"

exit 0
EOF

# Dar permissão de execução
chmod +x /root/VPN/OpenVPN/scripts/client-disconnect.sh
```

**3. Criar arquivo de log de eventos:**
```bash
# Criar arquivo de log se não existir
touch /root/VPN/OpenVPN/logs/events.log
chmod 644 /root/VPN/OpenVPN/logs/events.log
```

**🔧 COMANDO COMPLETO PARA IMPLEMENTAR:**
```bash
# Script completo para implementar scripts de eventos
#!/bin/bash
echo "Implementando scripts de eventos OpenVPN..."

# 1. Criar client-connect.sh
cat > /root/VPN/OpenVPN/scripts/client-connect.sh << 'EOF'
#!/bin/bash
set -e
CLIENT_NAME="$common_name"
EXTERNAL_IP="$trusted_ip"
VPN_IP="$ifconfig_pool_remote_ip"
TIMESTAMP=$(date -Iseconds)
echo "$(date '+%Y-%m-%d %H:%M:%S') CLIENT_CONNECT: $CLIENT_NAME from $EXTERNAL_IP assigned $VPN_IP" >> /root/VPN/OpenVPN/logs/events.log
curl -s -X POST http://localhost:8080/api/v1/internal/events/connect \
  -H "Content-Type: application/json" \
  -H "X-Internal-Token: vpn_internal_secret_2024" \
  -d "{\"client_name\":\"$CLIENT_NAME\",\"external_ip\":\"$EXTERNAL_IP\",\"vpn_ip\":\"$VPN_IP\",\"timestamp\":\"$TIMESTAMP\",\"event_type\":\"connect\"}" || echo "Failed to notify backend"
exit 0
EOF

# 2. Criar client-disconnect.sh
cat > /root/VPN/OpenVPN/scripts/client-disconnect.sh << 'EOF'
#!/bin/bash
set -e
CLIENT_NAME="$common_name"
BYTES_RECEIVED="$bytes_received"
BYTES_SENT="$bytes_sent"
DURATION="$time_duration"
TIMESTAMP=$(date -Iseconds)
echo "$(date '+%Y-%m-%d %H:%M:%S') CLIENT_DISCONNECT: $CLIENT_NAME (RX: $BYTES_RECEIVED, TX: $BYTES_SENT, Duration: ${DURATION}s)" >> /root/VPN/OpenVPN/logs/events.log
curl -s -X POST http://localhost:8080/api/v1/internal/events/disconnect \
  -H "Content-Type: application/json" \
  -H "X-Internal-Token: vpn_internal_secret_2024" \
  -d "{\"client_name\":\"$CLIENT_NAME\",\"bytes_received\":$BYTES_RECEIVED,\"bytes_sent\":$BYTES_SENT,\"duration\":$DURATION,\"timestamp\":\"$TIMESTAMP\",\"event_type\":\"disconnect\"}" || echo "Failed to notify backend"
exit 0
EOF

# 3. Dar permissões
chmod +x /root/VPN/OpenVPN/scripts/client-connect.sh
chmod +x /root/VPN/OpenVPN/scripts/client-disconnect.sh

# 4. Criar log de eventos
touch /root/VPN/OpenVPN/logs/events.log
chmod 644 /root/VPN/OpenVPN/logs/events.log

echo "Scripts de eventos implementados com sucesso!"
```

### **2. Expansão do Backend para Integração Completa**

#### **2.1 Novos Endpoints de Integração - IMPLEMENTAÇÃO OBRIGATÓRIA**

**🚨 STATUS ATUAL:**
```bash
# Backend atual NÃO TEM endpoints para receber eventos OpenVPN
# Resultado: Scripts de eventos falharão ao tentar notificar backend
# CRÍTICO: Implementar endpoints antes de ativar scripts
```

**✅ ENDPOINTS OBRIGATÓRIOS PARA IMPLEMENTAR:**

**1. Endpoints Internos (para OpenVPN):**
```go
// internal/handlers/integration_handler.go
type IntegrationHandler struct {
    db         *gorm.DB
    vpnService *openvpn.OpenVPNService
    notifier   *websocket.Notifier
}

// POST /api/v1/internal/events/connect
func (h *IntegrationHandler) HandleClientConnect(c *gin.Context) {
    var event struct {
        ClientName  string `json:"client_name"`
        ExternalIP  string `json:"external_ip"`
        VpnIP       string `json:"vpn_ip"`
        Timestamp   string `json:"timestamp"`
        EventType   string `json:"event_type"`
    }

    if err := c.ShouldBindJSON(&event); err != nil {
        c.JSON(400, gin.H{"error": "Invalid JSON"})
        return
    }

    // Salvar no banco
    connection := models.VPNConnection{
        ClientName: event.ClientName,
        ExternalIP: event.ExternalIP,
        VpnIP:      event.VpnIP,
        ConnectedAt: time.Now(),
        Status:     "connected",
    }
    h.db.Create(&connection)

    // Notificar via WebSocket
    h.notifier.BroadcastEvent("client_connected", event)

    c.JSON(200, gin.H{"status": "success"})
}

// POST /api/v1/internal/events/disconnect
func (h *IntegrationHandler) HandleClientDisconnect(c *gin.Context) {
    var event struct {
        ClientName     string `json:"client_name"`
        BytesReceived  int64  `json:"bytes_received"`
        BytesSent      int64  `json:"bytes_sent"`
        Duration       int    `json:"duration"`
        Timestamp      string `json:"timestamp"`
        EventType      string `json:"event_type"`
    }

    if err := c.ShouldBindJSON(&event); err != nil {
        c.JSON(400, gin.H{"error": "Invalid JSON"})
        return
    }

    // Atualizar no banco
    h.db.Model(&models.VPNConnection{}).
        Where("client_name = ? AND status = ?", event.ClientName, "connected").
        Updates(map[string]interface{}{
            "status": "disconnected",
            "disconnected_at": time.Now(),
            "bytes_received": event.BytesReceived,
            "bytes_sent": event.BytesSent,
            "duration": event.Duration,
        })

    // Notificar via WebSocket
    h.notifier.BroadcastEvent("client_disconnected", event)

    c.JSON(200, gin.H{"status": "success"})
}
```

**2. Endpoints para Frontend:**
```go
// GET /api/v1/vpn/realtime-status
func (h *IntegrationHandler) GetRealTimeStatus(c *gin.Context) {
    var activeConnections []models.VPNConnection
    h.db.Where("status = ?", "connected").Find(&activeConnections)

    status := gin.H{
        "active_connections": len(activeConnections),
        "clients": activeConnections,
        "server_status": "running",
        "timestamp": time.Now(),
    }

    c.JSON(200, status)
}

// GET /api/v1/vpn/clients/:name
func (h *IntegrationHandler) GetClientDetails(c *gin.Context) {
    clientName := c.Param("name")

    var connections []models.VPNConnection
    h.db.Where("client_name = ?", clientName).
        Order("connected_at DESC").
        Limit(10).
        Find(&connections)

    c.JSON(200, gin.H{
        "client_name": clientName,
        "connections": connections,
    })
}

// POST /api/v1/vpn/clients/:name/disconnect
func (h *IntegrationHandler) DisconnectClient(c *gin.Context) {
    clientName := c.Param("name")

    // Usar management interface para desconectar
    err := h.vpnService.DisconnectClient(clientName)
    if err != nil {
        c.JSON(500, gin.H{"error": "Failed to disconnect client"})
        return
    }

    c.JSON(200, gin.H{"status": "disconnected", "client": clientName})
}
```

**3. Middleware de Autenticação Interna:**
```go
// internal/middleware/internal_auth.go
func InternalAuth() gin.HandlerFunc {
    return func(c *gin.Context) {
        token := c.GetHeader("X-Internal-Token")
        if token != "vpn_internal_secret_2024" {
            c.JSON(401, gin.H{"error": "Unauthorized internal access"})
            c.Abort()
            return
        }
        c.Next()
    }
}
```

**🔧 IMPLEMENTAÇÃO NO MAIN.GO:**
```go
// Adicionar ao main.go do backend:
func setupRoutes(r *gin.Engine, handlers *handlers.Handlers) {
    // ... rotas existentes ...

    // Rotas internas (para OpenVPN)
    internal := r.Group("/api/v1/internal")
    internal.Use(middleware.InternalAuth())
    {
        internal.POST("/events/connect", handlers.Integration.HandleClientConnect)
        internal.POST("/events/disconnect", handlers.Integration.HandleClientDisconnect)
    }

    // Rotas VPN (para Frontend)
    vpn := r.Group("/api/v1/vpn")
    vpn.Use(middleware.AuthRequired())
    {
        vpn.GET("/realtime-status", handlers.Integration.GetRealTimeStatus)
        vpn.GET("/clients/:name", handlers.Integration.GetClientDetails)
        vpn.POST("/clients/:name/disconnect", handlers.Integration.DisconnectClient)
    }
}
```

#### **2.2 Parser OpenVPN Avançado**
```go
// internal/openvpn/advanced_parser.go
type AdvancedParser struct {
    statusFile    string
    logFile       string
    eventsFile    string
    managementAPI *ManagementAPI
}

// Funcionalidades avançadas
func (p *AdvancedParser) ParseStatusFile() (*VPNStatus, error)
func (p *AdvancedParser) ParseConnectionEvents() ([]ConnectionEvent, error)
func (p *AdvancedParser) GetActiveClients() ([]ActiveClient, error)
func (p *AdvancedParser) GetBandwidthStats() (*BandwidthStats, error)
func (p *AdvancedParser) GetRoutingTable() ([]Route, error)
```

#### **2.3 Management API Integration**
```go
// internal/openvpn/management_api.go
type ManagementAPI struct {
    host string
    port int
    conn net.Conn
}

func (m *ManagementAPI) Connect() error
func (m *ManagementAPI) GetStatus() (*StatusResponse, error)
func (m *ManagementAPI) KillClient(commonName string) error
func (m *ManagementAPI) GetLoadStats() (*LoadStats, error)
func (m *ManagementAPI) SendCommand(cmd string) (string, error)
```

### **3. Frontend - Interface Unificada VPN**

#### **3.1 Dashboard Principal VPN**
```typescript
// src/components/VPNDashboard.tsx
interface VPNDashboard {
  // Componentes principais
  ServerStatus: React.FC          // Status do servidor OpenVPN
  ActiveClients: React.FC         // Clientes conectados em tempo real
  NetworkTopology: React.FC       // Topologia da rede VPN
  BandwidthMonitor: React.FC      // Monitor de largura de banda
  EventsTimeline: React.FC        // Timeline de eventos
  SecurityAlerts: React.FC        // Alertas de segurança
}

// Estados em tempo real via WebSocket
const [serverStatus, setServerStatus] = useState<ServerStatus>()
const [activeClients, setActiveClients] = useState<Client[]>([])
const [networkStats, setNetworkStats] = useState<NetworkStats>()
const [recentEvents, setRecentEvents] = useState<Event[]>([])
```

#### **3.2 Componentes Específicos VPN**
```typescript
// src/components/VPN/ServerStatus.tsx
interface ServerStatusProps {
  uptime: string
  totalClients: number
  activeConnections: number
  serverLoad: number
  networkThroughput: {
    incoming: number
    outgoing: number
  }
}

// src/components/VPN/ClientManager.tsx
interface ClientManagerProps {
  clients: ActiveClient[]
  onDisconnectClient: (clientId: string) => void
  onViewClientDetails: (clientId: string) => void
  onGenerateConfig: (clientName: string) => void
}

// src/components/VPN/NetworkTopology.tsx
interface NetworkTopologyProps {
  vpnNetwork: string
  routes: Route[]
  subnets: Subnet[]
  connectedDevices: Device[]
}
```

#### **3.3 Integração com Scripts de Setup**
```typescript
// src/components/VPN/DeviceSetup.tsx
interface DeviceSetupProps {
  deviceType: 'raspberry' | 'windows' | 'linux' | 'mobile'
  onGenerateConfig: (deviceInfo: DeviceInfo) => void
  onDownloadScript: (scriptType: string) => void
}

// Integração com scripts existentes
const downloadSetupScript = (deviceType: string) => {
  const scripts = {
    raspberry: '/api/v1/setup/raspberry-setup.sh',
    windows: '/api/v1/setup/windows-setup.ps1',
    linux: '/api/v1/setup/linux-setup.sh'
  }
  
  window.open(scripts[deviceType], '_blank')
}
```

### **4. Integração com Scripts de Setup Existentes**

#### **4.1 Endpoint para Scripts**
```go
// internal/handlers/setup_handler.go
func (h *SetupHandler) GetRaspberryScript(c *gin.Context) {
    // Servir /root/VPN/OpenVPN/scripts/raspberry-setup.sh
    c.File("/root/VPN/OpenVPN/scripts/raspberry-setup.sh")
}

func (h *SetupHandler) GetWindowsScript(c *gin.Context) {
    // Servir /root/VPN/OpenVPN/scripts/windows-setup.ps1
    c.File("/root/VPN/OpenVPN/scripts/windows-setup.ps1")
}

func (h *SetupHandler) GenerateClientConfig(c *gin.Context) {
    // Gerar configuração personalizada para cliente
    clientName := c.Param("name")
    config := h.vpnService.GenerateClientConfig(clientName)
    c.Header("Content-Disposition", "attachment; filename="+clientName+".ovpn")
    c.Data(200, "application/x-openvpn-profile", config)
}
```

#### **4.2 Modificação dos Scripts Existentes**
```bash
# Adicionar ao raspberry-setup.sh
# Notificar backend sobre nova instalação
curl -X POST http://vpn.evo-eden.site/api/v1/internal/device/register \
  -H "Content-Type: application/json" \
  -d "{\"device_type\":\"raspberry\",\"hostname\":\"$(hostname)\",\"ip\":\"$(hostname -I | awk '{print $1}')\",\"timestamp\":\"$(date -Iseconds)\"}"
```

### **5. Monitoramento em Tempo Real**

#### **5.1 WebSocket Events Expandidos**
```go
// internal/websocket/vpn_events.go
type VPNEventType string

const (
    ClientConnected    VPNEventType = "client_connected"
    ClientDisconnected VPNEventType = "client_disconnected"
    ServerStatusUpdate VPNEventType = "server_status_update"
    BandwidthAlert     VPNEventType = "bandwidth_alert"
    SecurityAlert      VPNEventType = "security_alert"
    NetworkTopologyChange VPNEventType = "network_topology_change"
)

type VPNEvent struct {
    Type      VPNEventType `json:"type"`
    Timestamp time.Time    `json:"timestamp"`
    Data      interface{}  `json:"data"`
    ClientID  string       `json:"client_id,omitempty"`
}
```

#### **5.2 Notificações Automáticas**
```go
// internal/services/notification_service.go
func (s *NotificationService) NotifyClientConnection(client *models.VPNClient) {
    event := VPNEvent{
        Type:      ClientConnected,
        Timestamp: time.Now(),
        Data:      client,
        ClientID:  client.Name,
    }
    s.wsHub.BroadcastToRole("admin", event)
}

func (s *NotificationService) NotifySecurityAlert(alert *SecurityAlert) {
    event := VPNEvent{
        Type:      SecurityAlert,
        Timestamp: time.Now(),
        Data:      alert,
    }
    s.wsHub.BroadcastToAll(event)
}
```

---

## 📊 **DADOS EXIBIDOS NO FRONTEND**

### **1. Dashboard Principal**
- **Status do Servidor**: Uptime, carga, versão OpenVPN
- **Clientes Ativos**: Lista em tempo real com IP, tráfego, tempo conectado
- **Métricas de Rede**: Largura de banda, latência, perda de pacotes
- **Eventos Recentes**: Timeline de conexões/desconexões
- **Alertas de Segurança**: Tentativas de acesso, falhas de autenticação

### **2. Monitoramento de Clientes**
- **Detalhes por Cliente**: IP interno/externo, localização, dispositivo
- **Histórico de Conexões**: Sessões anteriores, duração, tráfego
- **Estatísticas de Uso**: Dados transferidos, picos de tráfego
- **Status de Saúde**: Latência, qualidade da conexão

### **3. Análise de Rede**
- **Topologia VPN**: Mapa visual da rede
- **Roteamento**: Tabelas de rotas, subnets
- **Performance**: Throughput, utilização de recursos
- **Logs Estruturados**: Eventos filtráveis e pesquisáveis

### **4. Gerenciamento de Dispositivos**
- **Configuração Automática**: Download de scripts por tipo de dispositivo
- **Geração de Certificados**: Interface para novos clientes
- **Revogação**: Desativar clientes comprometidos
- **Backup/Restore**: Configurações e certificados

---

## 🔄 **FLUXO DE INTEGRAÇÃO**

### **Sequência de Eventos**
1. **Cliente conecta** → OpenVPN gera log → Script notifica Backend
2. **Backend processa** → Atualiza banco → Envia WebSocket
3. **Frontend recebe** → Atualiza interface → Mostra notificação
4. **Monitoramento contínuo** → Parser coleta métricas → Dashboard atualiza

### **Sincronização de Dados**
- **Tempo Real**: WebSocket para eventos imediatos
- **Periódico**: Coleta de métricas a cada 30s
- **Sob Demanda**: Refresh manual de dados específicos
- **Histórico**: Consultas ao banco para análises

---

## 🎯 **RESULTADOS ESPERADOS**

### **Interface Unificada**
- ✅ Dashboard único para todo o sistema VPN
- ✅ Monitoramento em tempo real de todos os componentes
- ✅ Gestão completa de clientes e dispositivos
- ✅ Análise histórica e relatórios

### **Automação Completa**
- ✅ Setup automático de novos dispositivos
- ✅ Notificações instantâneas de eventos
- ✅ Alertas proativos de segurança
- ✅ Backup automático de configurações

### **Experiência do Usuário**
- ✅ Interface intuitiva e responsiva
- ✅ Dados em tempo real sem refresh manual
- ✅ Controle total via web interface
- ✅ Suporte a múltiplos tipos de dispositivos

---

## 📋 **CHECKLIST DE IMPLEMENTAÇÃO**

### **Backend**
- [ ] Implementar endpoints de integração OpenVPN
- [ ] Criar Management API client
- [ ] Expandir parser para dados avançados
- [ ] Adicionar endpoints para scripts de setup
- [ ] Implementar notificações WebSocket expandidas

### **Frontend**
- [ ] Criar dashboard VPN unificado
- [ ] Implementar componentes de monitoramento
- [ ] Adicionar interface de gerenciamento de clientes
- [ ] Criar sistema de download de scripts
- [ ] Implementar visualização de topologia de rede

### **OpenVPN**
- [ ] Configurar logs detalhados
- [ ] Implementar scripts de eventos
- [ ] Ativar management interface
- [ ] Configurar hooks de conexão/desconexão

### **Integração**
- [ ] Testar fluxo completo de dados
- [ ] Validar notificações em tempo real
- [ ] Verificar scripts de setup
- [ ] Testar interface unificada

---

## 🛠️ **IMPLEMENTAÇÃO DETALHADA**

### **1. Configuração OpenVPN Avançada**

#### **1.1 Arquivo de Configuração Expandido**
```bash
# /root/VPN/OpenVPN/server.conf - Configuração completa para integração
port 1194
proto udp
dev tun

# Certificados e chaves
ca /root/VPN/OpenVPN/pki/ca.crt
cert /root/VPN/OpenVPN/pki/issued/server.crt
key /root/VPN/OpenVPN/pki/private/server.key
dh /root/VPN/OpenVPN/pki/dh.pem

# Rede VPN
server ******** *************
ifconfig-pool-persist /root/VPN/OpenVPN/logs/ipp.txt

# Logs detalhados para integração
log /root/VPN/OpenVPN/logs/openvpn.log
log-append
status /root/VPN/OpenVPN/logs/status.log 10
status-version 3
verb 4

# Scripts de integração
client-connect /root/VPN/OpenVPN/scripts/client-connect.sh
client-disconnect /root/VPN/OpenVPN/scripts/client-disconnect.sh
learn-address /root/VPN/OpenVPN/scripts/learn-address.sh

# Management interface para controle remoto
management 127.0.0.1 7505
management-log-cache 100
management-hold

# Segurança
auth SHA256
cipher AES-256-CBC
tls-auth /root/VPN/OpenVPN/pki/ta.key 0
tls-version-min 1.2

# Configurações de rede
push "redirect-gateway def1 bypass-dhcp"
push "dhcp-option DNS *******"
push "dhcp-option DNS *******"

# Keepalive
keepalive 10 120

# Compressão
compress lz4-v2
push "compress lz4-v2"

# Usuário não privilegiado
user nobody
group nogroup
persist-key
persist-tun
```

#### **1.2 Scripts de Eventos Detalhados**
```bash
# /root/VPN/OpenVPN/scripts/client-connect.sh
#!/bin/bash
set -e

# Variáveis do OpenVPN
CLIENT_NAME="$common_name"
CLIENT_IP="$trusted_ip"
VPN_IP="$ifconfig_pool_remote_ip"
TIMESTAMP=$(date -Iseconds)

# Log local
echo "$(date '+%Y-%m-%d %H:%M:%S') CLIENT_CONNECT: $CLIENT_NAME from $CLIENT_IP assigned $VPN_IP" >> /root/VPN/OpenVPN/logs/events.log

# Notificar backend
curl -s -X POST http://localhost:8080/api/v1/internal/events/connect \
  -H "Content-Type: application/json" \
  -H "X-Internal-Token: vpn_internal_secret_2024" \
  -d "{
    \"client_name\": \"$CLIENT_NAME\",
    \"external_ip\": \"$CLIENT_IP\",
    \"vpn_ip\": \"$VPN_IP\",
    \"timestamp\": \"$TIMESTAMP\",
    \"event_type\": \"connect\"
  }" || echo "Failed to notify backend"

# Configurações específicas por cliente (opcional)
if [ -f "/root/VPN/OpenVPN/client-configs/$CLIENT_NAME.conf" ]; then
    cat "/root/VPN/OpenVPN/client-configs/$CLIENT_NAME.conf"
fi

exit 0
```

```bash
# /root/VPN/OpenVPN/scripts/client-disconnect.sh
#!/bin/bash
set -e

# Variáveis do OpenVPN
CLIENT_NAME="$common_name"
BYTES_RECEIVED="$bytes_received"
BYTES_SENT="$bytes_sent"
DURATION="$time_duration"
TIMESTAMP=$(date -Iseconds)

# Log local
echo "$(date '+%Y-%m-%d %H:%M:%S') CLIENT_DISCONNECT: $CLIENT_NAME (RX: $BYTES_RECEIVED, TX: $BYTES_SENT, Duration: ${DURATION}s)" >> /root/VPN/OpenVPN/logs/events.log

# Notificar backend
curl -s -X POST http://localhost:8080/api/v1/internal/events/disconnect \
  -H "Content-Type: application/json" \
  -H "X-Internal-Token: vpn_internal_secret_2024" \
  -d "{
    \"client_name\": \"$CLIENT_NAME\",
    \"bytes_received\": $BYTES_RECEIVED,
    \"bytes_sent\": $BYTES_SENT,
    \"duration\": $DURATION,
    \"timestamp\": \"$TIMESTAMP\",
    \"event_type\": \"disconnect\"
  }" || echo "Failed to notify backend"

exit 0
```

```bash
# /root/VPN/OpenVPN/scripts/learn-address.sh
#!/bin/bash
set -e

# Variáveis: $1=operation $2=address $3=common_name
OPERATION="$1"
ADDRESS="$2"
CLIENT_NAME="$3"
TIMESTAMP=$(date -Iseconds)

# Log local
echo "$(date '+%Y-%m-%d %H:%M:%S') LEARN_ADDRESS: $OPERATION $ADDRESS for $CLIENT_NAME" >> /root/VPN/OpenVPN/logs/events.log

# Notificar backend sobre mudanças de endereço
curl -s -X POST http://localhost:8080/api/v1/internal/events/address \
  -H "Content-Type: application/json" \
  -H "X-Internal-Token: vpn_internal_secret_2024" \
  -d "{
    \"operation\": \"$OPERATION\",
    \"address\": \"$ADDRESS\",
    \"client_name\": \"$CLIENT_NAME\",
    \"timestamp\": \"$TIMESTAMP\",
    \"event_type\": \"address_change\"
  }" || echo "Failed to notify backend"

exit 0
```

### **2. Backend - Expansão para Integração Completa**

#### **2.1 Novos Modelos de Dados**
```go
// internal/models/vpn_models.go
type VPNServerStatus struct {
    ID              uint      `gorm:"primaryKey" json:"id"`
    Timestamp       time.Time `json:"timestamp"`
    Uptime          int64     `json:"uptime"`
    ActiveClients   int       `json:"active_clients"`
    TotalClients    int       `json:"total_clients"`
    BytesIn         int64     `json:"bytes_in"`
    BytesOut        int64     `json:"bytes_out"`
    LoadAverage     float64   `json:"load_average"`
    MemoryUsage     float64   `json:"memory_usage"`
    CPUUsage        float64   `json:"cpu_usage"`
}

type VPNConnectionEvent struct {
    ID           uint      `gorm:"primaryKey" json:"id"`
    ClientName   string    `json:"client_name"`
    EventType    string    `json:"event_type"` // connect, disconnect, address_change
    ExternalIP   string    `json:"external_ip"`
    VPNAddress   string    `json:"vpn_address"`
    BytesReceived int64    `json:"bytes_received"`
    BytesSent    int64     `json:"bytes_sent"`
    Duration     int       `json:"duration"`
    Timestamp    time.Time `json:"timestamp"`
    CreatedAt    time.Time `json:"created_at"`
}

type VPNClientStatus struct {
    ID              uint      `gorm:"primaryKey" json:"id"`
    ClientName      string    `gorm:"uniqueIndex" json:"client_name"`
    IsConnected     bool      `json:"is_connected"`
    ExternalIP      string    `json:"external_ip"`
    VPNAddress      string    `json:"vpn_address"`
    ConnectedSince  *time.Time `json:"connected_since"`
    LastSeen        time.Time `json:"last_seen"`
    TotalBytesIn    int64     `json:"total_bytes_in"`
    TotalBytesOut   int64     `json:"total_bytes_out"`
    SessionBytesIn  int64     `json:"session_bytes_in"`
    SessionBytesOut int64     `json:"session_bytes_out"`
    DeviceType      string    `json:"device_type"`
    UserAgent       string    `json:"user_agent"`
    Location        string    `json:"location"`
    UpdatedAt       time.Time `json:"updated_at"`
}

type VPNRoute struct {
    ID          uint   `gorm:"primaryKey" json:"id"`
    Network     string `json:"network"`
    Netmask     string `json:"netmask"`
    Gateway     string `json:"gateway"`
    Metric      int    `json:"metric"`
    Interface   string `json:"interface"`
    IsActive    bool   `json:"is_active"`
    CreatedAt   time.Time `json:"created_at"`
}
```

#### **2.2 Handler de Integração OpenVPN**
```go
// internal/handlers/vpn_integration_handler.go
type VPNIntegrationHandler struct {
    db           *gorm.DB
    vpnService   *openvpn.OpenVPNService
    notifier     *websocket.Notifier
    internalToken string
}

func NewVPNIntegrationHandler(db *gorm.DB, vpnService *openvpn.OpenVPNService, notifier *websocket.Notifier) *VPNIntegrationHandler {
    return &VPNIntegrationHandler{
        db:           db,
        vpnService:   vpnService,
        notifier:     notifier,
        internalToken: "vpn_internal_secret_2024",
    }
}

// Middleware para validar token interno
func (h *VPNIntegrationHandler) validateInternalToken(c *gin.Context) {
    token := c.GetHeader("X-Internal-Token")
    if token != h.internalToken {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid internal token"})
        c.Abort()
        return
    }
    c.Next()
}

// Evento de conexão de cliente
func (h *VPNIntegrationHandler) HandleClientConnect(c *gin.Context) {
    var event VPNConnectionEvent
    if err := c.ShouldBindJSON(&event); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    // Salvar evento no banco
    if err := h.db.Create(&event).Error; err != nil {
        log.Printf("Erro ao salvar evento de conexão: %v", err)
    }

    // Atualizar status do cliente
    clientStatus := VPNClientStatus{
        ClientName:     event.ClientName,
        IsConnected:    true,
        ExternalIP:     event.ExternalIP,
        VPNAddress:     event.VPNAddress,
        ConnectedSince: &event.Timestamp,
        LastSeen:       event.Timestamp,
    }

    h.db.Save(&clientStatus)

    // Notificar via WebSocket
    h.notifier.NotifyClientConnection(&clientStatus)

    c.JSON(http.StatusOK, gin.H{"status": "success"})
}

// Evento de desconexão de cliente
func (h *VPNIntegrationHandler) HandleClientDisconnect(c *gin.Context) {
    var event VPNConnectionEvent
    if err := c.ShouldBindJSON(&event); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    // Salvar evento no banco
    if err := h.db.Create(&event).Error; err != nil {
        log.Printf("Erro ao salvar evento de desconexão: %v", err)
    }

    // Atualizar status do cliente
    var clientStatus VPNClientStatus
    if err := h.db.Where("client_name = ?", event.ClientName).First(&clientStatus).Error; err == nil {
        clientStatus.IsConnected = false
        clientStatus.ConnectedSince = nil
        clientStatus.LastSeen = event.Timestamp
        clientStatus.TotalBytesIn += event.BytesReceived
        clientStatus.TotalBytesOut += event.BytesSent
        h.db.Save(&clientStatus)
    }

    // Notificar via WebSocket
    h.notifier.NotifyClientDisconnection(&clientStatus)

    c.JSON(http.StatusOK, gin.H{"status": "success"})
}

// Mudança de endereço
func (h *VPNIntegrationHandler) HandleAddressChange(c *gin.Context) {
    var event VPNConnectionEvent
    if err := c.ShouldBindJSON(&event); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    // Salvar evento no banco
    if err := h.db.Create(&event).Error; err != nil {
        log.Printf("Erro ao salvar evento de mudança de endereço: %v", err)
    }

    c.JSON(http.StatusOK, gin.H{"status": "success"})
}
```

#### **2.3 Management API Client**
```go
// internal/openvpn/management_client.go
type ManagementClient struct {
    host string
    port int
    conn net.Conn
    mu   sync.Mutex
}

func NewManagementClient(host string, port int) *ManagementClient {
    return &ManagementClient{
        host: host,
        port: port,
    }
}

func (m *ManagementClient) Connect() error {
    m.mu.Lock()
    defer m.mu.Unlock()

    conn, err := net.Dial("tcp", fmt.Sprintf("%s:%d", m.host, m.port))
    if err != nil {
        return err
    }
    m.conn = conn
    return nil
}

func (m *ManagementClient) SendCommand(command string) (string, error) {
    m.mu.Lock()
    defer m.mu.Unlock()

    if m.conn == nil {
        if err := m.Connect(); err != nil {
            return "", err
        }
    }

    _, err := m.conn.Write([]byte(command + "\n"))
    if err != nil {
        return "", err
    }

    buffer := make([]byte, 4096)
    n, err := m.conn.Read(buffer)
    if err != nil {
        return "", err
    }

    return string(buffer[:n]), nil
}

func (m *ManagementClient) GetStatus() (*VPNServerStatus, error) {
    response, err := m.SendCommand("status")
    if err != nil {
        return nil, err
    }

    // Parse da resposta do status
    status := &VPNServerStatus{
        Timestamp: time.Now(),
    }

    lines := strings.Split(response, "\n")
    for _, line := range lines {
        if strings.HasPrefix(line, "CLIENTS,") {
            // Parse client info
        } else if strings.HasPrefix(line, "ROUTING,") {
            // Parse routing info
        }
    }

    return status, nil
}

func (m *ManagementClient) KillClient(commonName string) error {
    _, err := m.SendCommand(fmt.Sprintf("kill %s", commonName))
    return err
}

func (m *ManagementClient) GetLoadStats() (map[string]interface{}, error) {
    response, err := m.SendCommand("load-stats")
    if err != nil {
        return nil, err
    }

    stats := make(map[string]interface{})
    // Parse load stats response
    return stats, nil
}

func (m *ManagementClient) Close() error {
    m.mu.Lock()
    defer m.mu.Unlock()

    if m.conn != nil {
        return m.conn.Close()
    }
    return nil
}
```

### **3. Frontend - Interface VPN Unificada**

#### **3.1 Dashboard VPN Principal**
```typescript
// src/pages/VPNDashboard.tsx
import React, { useState, useEffect } from 'react'
import { useWebSocket } from '../hooks/useWebSocket'
import { vpnAPI } from '../services/vpnAPI'

interface VPNDashboardState {
  serverStatus: VPNServerStatus | null
  activeClients: VPNClient[]
  recentEvents: VPNEvent[]
  networkStats: NetworkStats | null
  isLoading: boolean
}

export const VPNDashboard: React.FC = () => {
  const [state, setState] = useState<VPNDashboardState>({
    serverStatus: null,
    activeClients: [],
    recentEvents: [],
    networkStats: null,
    isLoading: true
  })

  // WebSocket para atualizações em tempo real
  const { lastMessage, connectionStatus } = useWebSocket('/ws')

  useEffect(() => {
    // Carregar dados iniciais
    loadInitialData()
  }, [])

  useEffect(() => {
    // Processar mensagens WebSocket
    if (lastMessage) {
      handleWebSocketMessage(lastMessage)
    }
  }, [lastMessage])

  const loadInitialData = async () => {
    try {
      const [serverStatus, activeClients, recentEvents, networkStats] = await Promise.all([
        vpnAPI.getServerStatus(),
        vpnAPI.getActiveClients(),
        vpnAPI.getRecentEvents(),
        vpnAPI.getNetworkStats()
      ])

      setState(prev => ({
        ...prev,
        serverStatus,
        activeClients,
        recentEvents,
        networkStats,
        isLoading: false
      }))
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
      setState(prev => ({ ...prev, isLoading: false }))
    }
  }

  const handleWebSocketMessage = (message: any) => {
    const event = JSON.parse(message.data)

    switch (event.type) {
      case 'client_connected':
        setState(prev => ({
          ...prev,
          activeClients: [...prev.activeClients, event.data],
          recentEvents: [event, ...prev.recentEvents.slice(0, 49)]
        }))
        break

      case 'client_disconnected':
        setState(prev => ({
          ...prev,
          activeClients: prev.activeClients.filter(c => c.name !== event.data.name),
          recentEvents: [event, ...prev.recentEvents.slice(0, 49)]
        }))
        break

      case 'server_status_update':
        setState(prev => ({
          ...prev,
          serverStatus: event.data
        }))
        break
    }
  }

  return (
    <div className="vpn-dashboard">
      <div className="dashboard-header">
        <h1>VPN Dashboard</h1>
        <div className="connection-status">
          <span className={`status-indicator ${connectionStatus}`}>
            {connectionStatus === 'connected' ? '🟢' : '🔴'}
          </span>
          WebSocket: {connectionStatus}
        </div>
      </div>

      <div className="dashboard-grid">
        <ServerStatusCard serverStatus={state.serverStatus} />
        <ActiveClientsCard
          clients={state.activeClients}
          onDisconnectClient={handleDisconnectClient}
        />
        <NetworkStatsCard networkStats={state.networkStats} />
        <RecentEventsCard events={state.recentEvents} />
      </div>
    </div>
  )
}
```

#### **3.2 Componentes VPN Específicos**
```typescript
// src/components/VPN/ServerStatusCard.tsx
interface ServerStatusCardProps {
  serverStatus: VPNServerStatus | null
}

export const ServerStatusCard: React.FC<ServerStatusCardProps> = ({ serverStatus }) => {
  if (!serverStatus) return <div>Carregando status do servidor...</div>

  return (
    <div className="server-status-card">
      <h3>Status do Servidor OpenVPN</h3>
      <div className="status-grid">
        <div className="status-item">
          <span className="label">Uptime:</span>
          <span className="value">{formatUptime(serverStatus.uptime)}</span>
        </div>
        <div className="status-item">
          <span className="label">Clientes Ativos:</span>
          <span className="value">{serverStatus.activeClients}</span>
        </div>
        <div className="status-item">
          <span className="label">Total de Clientes:</span>
          <span className="value">{serverStatus.totalClients}</span>
        </div>
        <div className="status-item">
          <span className="label">Carga do Sistema:</span>
          <span className="value">{serverStatus.loadAverage.toFixed(2)}</span>
        </div>
        <div className="status-item">
          <span className="label">Uso de CPU:</span>
          <span className="value">{serverStatus.cpuUsage.toFixed(1)}%</span>
        </div>
        <div className="status-item">
          <span className="label">Uso de Memória:</span>
          <span className="value">{serverStatus.memoryUsage.toFixed(1)}%</span>
        </div>
      </div>

      <div className="bandwidth-stats">
        <h4>Tráfego de Rede</h4>
        <div className="bandwidth-item">
          <span>Entrada: {formatBytes(serverStatus.bytesIn)}</span>
        </div>
        <div className="bandwidth-item">
          <span>Saída: {formatBytes(serverStatus.bytesOut)}</span>
        </div>
      </div>
    </div>
  )
}

// src/components/VPN/ActiveClientsCard.tsx
interface ActiveClientsCardProps {
  clients: VPNClient[]
  onDisconnectClient: (clientName: string) => void
}

export const ActiveClientsCard: React.FC<ActiveClientsCardProps> = ({
  clients,
  onDisconnectClient
}) => {
  return (
    <div className="active-clients-card">
      <h3>Clientes Conectados ({clients.length})</h3>
      <div className="clients-list">
        {clients.map(client => (
          <div key={client.name} className="client-item">
            <div className="client-info">
              <div className="client-name">{client.name}</div>
              <div className="client-details">
                <span>IP VPN: {client.vpnAddress}</span>
                <span>IP Externo: {client.externalIP}</span>
                <span>Conectado: {formatDuration(client.connectedSince)}</span>
              </div>
            </div>
            <div className="client-stats">
              <div>↓ {formatBytes(client.sessionBytesIn)}</div>
              <div>↑ {formatBytes(client.sessionBytesOut)}</div>
            </div>
            <div className="client-actions">
              <button
                onClick={() => onDisconnectClient(client.name)}
                className="disconnect-btn"
              >
                Desconectar
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
```

#### **3.3 Serviços de API VPN**
```typescript
// src/services/vpnAPI.ts
class VPNAPIService {
  private baseURL = '/api/v1'

  async getServerStatus(): Promise<VPNServerStatus> {
    const response = await fetch(`${this.baseURL}/vpn/server/status`)
    return response.json()
  }

  async getActiveClients(): Promise<VPNClient[]> {
    const response = await fetch(`${this.baseURL}/vpn/clients/active`)
    return response.json()
  }

  async getClientDetails(clientName: string): Promise<VPNClientDetails> {
    const response = await fetch(`${this.baseURL}/vpn/clients/${clientName}`)
    return response.json()
  }

  async disconnectClient(clientName: string): Promise<void> {
    await fetch(`${this.baseURL}/vpn/clients/${clientName}/disconnect`, {
      method: 'POST'
    })
  }

  async getRecentEvents(): Promise<VPNEvent[]> {
    const response = await fetch(`${this.baseURL}/vpn/events/recent`)
    return response.json()
  }

  async getNetworkStats(): Promise<NetworkStats> {
    const response = await fetch(`${this.baseURL}/vpn/network/stats`)
    return response.json()
  }

  async generateClientConfig(clientName: string, deviceType: string): Promise<Blob> {
    const response = await fetch(`${this.baseURL}/vpn/clients/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ clientName, deviceType })
    })
    return response.blob()
  }

  async downloadSetupScript(scriptType: 'raspberry' | 'windows' | 'linux'): Promise<Blob> {
    const response = await fetch(`${this.baseURL}/setup/scripts/${scriptType}`)
    return response.blob()
  }
}

export const vpnAPI = new VPNAPIService()
```

### **4. Integração com Scripts de Setup**

#### **4.1 Modificação do Script Raspberry**
```bash
# Adicionar ao final de /root/VPN/OpenVPN/scripts/raspberry-setup.sh

# Registrar dispositivo no sistema VPN
register_device() {
    echo "🔗 Registrando dispositivo no sistema VPN..."

    DEVICE_INFO=$(cat << EOF
{
    "device_type": "raspberry",
    "hostname": "$(hostname)",
    "local_ip": "$(hostname -I | awk '{print $1}')",
    "mac_address": "$(cat /sys/class/net/eth0/address 2>/dev/null || echo 'unknown')",
    "os_version": "$(cat /etc/os-release | grep PRETTY_NAME | cut -d'=' -f2 | tr -d '\"')",
    "timestamp": "$(date -Iseconds)",
    "setup_script": "raspberry-setup.sh"
}
EOF
)

    # Tentar registrar no backend
    if curl -s -X POST https://vpn.evo-eden.site/api/v1/internal/device/register \
        -H "Content-Type: application/json" \
        -H "X-Internal-Token: vpn_internal_secret_2024" \
        -d "$DEVICE_INFO" > /dev/null 2>&1; then
        echo "✅ Dispositivo registrado com sucesso"
    else
        echo "⚠️ Falha ao registrar dispositivo (continuando instalação)"
    fi
}

# Configurar monitoramento local
setup_monitoring() {
    echo "📊 Configurando monitoramento local..."

    # Script de monitoramento
    cat > /usr/local/bin/vpn-monitor.sh << 'EOF'
#!/bin/bash
# Monitor VPN para Raspberry Pi

VPN_STATUS_URL="https://vpn.evo-eden.site/api/v1/internal/device/heartbeat"
DEVICE_ID="$(hostname)"

send_heartbeat() {
    HEARTBEAT_DATA=$(cat << EOL
{
    "device_id": "$DEVICE_ID",
    "timestamp": "$(date -Iseconds)",
    "vpn_status": "$(systemctl is-active openvpn@client || echo 'inactive')",
    "vpn_ip": "$(ip addr show tun0 2>/dev/null | grep 'inet ' | awk '{print $2}' | cut -d'/' -f1 || echo 'none')",
    "local_ip": "$(hostname -I | awk '{print $1}')",
    "uptime": "$(uptime -s)",
    "load_average": "$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | tr -d ',')"
}
EOL
)

    curl -s -X POST "$VPN_STATUS_URL" \
        -H "Content-Type: application/json" \
        -H "X-Internal-Token: vpn_internal_secret_2024" \
        -d "$HEARTBEAT_DATA" > /dev/null 2>&1
}

send_heartbeat
EOF

    chmod +x /usr/local/bin/vpn-monitor.sh

    # Cron job para heartbeat a cada 5 minutos
    echo "*/5 * * * * /usr/local/bin/vpn-monitor.sh" | crontab -

    echo "✅ Monitoramento configurado"
}

# Executar funções de integração
register_device
setup_monitoring

echo ""
echo "🎉 Setup Raspberry Pi concluído com integração VPN!"
echo "📊 Dashboard: https://vpn.evo-eden.site"
echo "🔍 Monitoramento: Ativo (heartbeat a cada 5min)"
```

#### **4.2 Modificação do Script Windows**
```powershell
# Adicionar ao final de /root/VPN/OpenVPN/scripts/windows-setup.ps1

# Função para registrar dispositivo
function Register-Device {
    Write-Host "🔗 Registrando dispositivo no sistema VPN..." -ForegroundColor Blue

    $deviceInfo = @{
        device_type = "windows"
        hostname = $env:COMPUTERNAME
        local_ip = (Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias "Ethernet*" | Select-Object -First 1).IPAddress
        os_version = (Get-WmiObject -Class Win32_OperatingSystem).Caption
        timestamp = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
        setup_script = "windows-setup.ps1"
    } | ConvertTo-Json

    try {
        $headers = @{
            "Content-Type" = "application/json"
            "X-Internal-Token" = "vpn_internal_secret_2024"
        }

        Invoke-RestMethod -Uri "https://vpn.evo-eden.site/api/v1/internal/device/register" `
                         -Method POST `
                         -Body $deviceInfo `
                         -Headers $headers
        Write-Host "✅ Dispositivo registrado com sucesso" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️ Falha ao registrar dispositivo (continuando instalação)" -ForegroundColor Yellow
    }
}

# Função para configurar monitoramento
function Setup-Monitoring {
    Write-Host "📊 Configurando monitoramento local..." -ForegroundColor Blue

    # Script de monitoramento PowerShell
    $monitorScript = @'
# Monitor VPN para Windows
$VPN_STATUS_URL = "https://vpn.evo-eden.site/api/v1/internal/device/heartbeat"
$DEVICE_ID = $env:COMPUTERNAME

$heartbeatData = @{
    device_id = $DEVICE_ID
    timestamp = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
    vpn_status = if (Get-Service "OpenVPNService" -ErrorAction SilentlyContinue) { "active" } else { "inactive" }
    local_ip = (Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias "Ethernet*" | Select-Object -First 1).IPAddress
    uptime = (Get-WmiObject -Class Win32_OperatingSystem).LastBootUpTime
} | ConvertTo-Json

try {
    $headers = @{
        "Content-Type" = "application/json"
        "X-Internal-Token" = "vpn_internal_secret_2024"
    }

    Invoke-RestMethod -Uri $VPN_STATUS_URL -Method POST -Body $heartbeatData -Headers $headers
}
catch {
    # Silenciar erros de heartbeat
}
'@

    $monitorScript | Out-File -FilePath "C:\Program Files\OpenVPN\vpn-monitor.ps1" -Encoding UTF8

    # Criar tarefa agendada para heartbeat
    $action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File `"C:\Program Files\OpenVPN\vpn-monitor.ps1`""
    $trigger = New-ScheduledTaskTrigger -RepetitionInterval (New-TimeSpan -Minutes 5) -Once -At (Get-Date)
    $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries

    Register-ScheduledTask -TaskName "VPN-Monitor" -Action $action -Trigger $trigger -Settings $settings -Force

    Write-Host "✅ Monitoramento configurado" -ForegroundColor Green
}

# Executar funções de integração
Register-Device
Setup-Monitoring

Write-Host ""
Write-Host "🎉 Setup Windows concluído com integração VPN!" -ForegroundColor Green
Write-Host "📊 Dashboard: https://vpn.evo-eden.site" -ForegroundColor Cyan
Write-Host "🔍 Monitoramento: Ativo (heartbeat a cada 5min)" -ForegroundColor Cyan
```

### **5. Endpoints Backend para Scripts**

#### **5.1 Handler para Scripts de Setup**
```go
// internal/handlers/setup_handler.go
type SetupHandler struct {
    db *gorm.DB
}

func (h *SetupHandler) GetRaspberryScript(c *gin.Context) {
    c.Header("Content-Disposition", "attachment; filename=raspberry-setup.sh")
    c.Header("Content-Type", "application/x-sh")
    c.File("/root/VPN/OpenVPN/scripts/raspberry-setup.sh")
}

func (h *SetupHandler) GetWindowsScript(c *gin.Context) {
    c.Header("Content-Disposition", "attachment; filename=windows-setup.ps1")
    c.Header("Content-Type", "application/x-powershell")
    c.File("/root/VPN/OpenVPN/scripts/windows-setup.ps1")
}

func (h *SetupHandler) RegisterDevice(c *gin.Context) {
    var device models.RegisteredDevice
    if err := c.ShouldBindJSON(&device); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    device.RegisteredAt = time.Now()
    if err := h.db.Create(&device).Error; err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to register device"})
        return
    }

    c.JSON(http.StatusOK, gin.H{"status": "Device registered successfully"})
}

func (h *SetupHandler) DeviceHeartbeat(c *gin.Context) {
    var heartbeat models.DeviceHeartbeat
    if err := c.ShouldBindJSON(&heartbeat); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    heartbeat.ReceivedAt = time.Now()
    if err := h.db.Create(&heartbeat).Error; err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save heartbeat"})
        return
    }

    c.JSON(http.StatusOK, gin.H{"status": "Heartbeat received"})
}
```

---

## 🎯 **PLANO DE IMPLEMENTAÇÃO FASE 5 - PASSO A PASSO**

### **📋 ORDEM DE IMPLEMENTAÇÃO OBRIGATÓRIA**

**🚨 IMPORTANTE: Implementar na ordem exata para evitar falhas**

#### **PASSO 1: Preparar Backend (PRIMEIRO)**
```bash
# 1.1 Implementar endpoints de integração no backend
# - Adicionar handlers para /api/v1/internal/events/*
# - Implementar middleware de autenticação interna
# - Adicionar rotas VPN para frontend

# 1.2 Testar endpoints internos
curl -X POST http://localhost:8080/api/v1/internal/events/connect \
  -H "Content-Type: application/json" \
  -H "X-Internal-Token: vpn_internal_secret_2024" \
  -d '{"client_name":"test","external_ip":"*******","vpn_ip":"***********","timestamp":"2024-01-01T00:00:00Z","event_type":"connect"}'

# 1.3 Verificar se backend aceita eventos
# Deve retornar: {"status": "success"}
```

#### **PASSO 2: Implementar Scripts OpenVPN (SEGUNDO)**
```bash
# 2.1 Criar scripts de eventos
# Executar script completo da seção 1.2

# 2.2 Testar scripts manualmente
/root/VPN/OpenVPN/scripts/client-connect.sh
# Verificar se notifica backend sem erro

# 2.3 Verificar logs
tail -f /root/VPN/OpenVPN/logs/events.log
```

#### **PASSO 3: Atualizar Configuração OpenVPN (TERCEIRO)**
```bash
# 3.1 Parar OpenVPN
pkill openvpn

# 3.2 Backup configuração atual
cp /etc/openvpn/server.conf /etc/openvpn/server.conf.backup

# 3.3 Adicionar configurações Fase 5
cat >> /etc/openvpn/server.conf << 'EOF'

# === CONFIGURAÇÕES FASE 5 ===
client-connect /root/VPN/OpenVPN/scripts/client-connect.sh
client-disconnect /root/VPN/OpenVPN/scripts/client-disconnect.sh
script-security 3
status-version 3
verb 4
management-log-cache 100
management-hold
EOF

# 3.4 Reiniciar OpenVPN
openvpn --daemon --config /etc/openvpn/server.conf --writepid /var/run/openvpn/server.pid

# 3.5 Verificar se management interface está ativa
netstat -tulpn | grep 7505
```

#### **PASSO 4: Testar Integração Completa (QUARTO)**
```bash
# 4.1 Criar cliente de teste
cd /root/VPN/OpenVPN/easy-rsa
./easyrsa build-client-full teste-fase5 nopass

# 4.2 Gerar arquivo .ovpn
cat > /tmp/teste-fase5.ovpn << EOF
client
dev tun
proto udp
remote $(curl -s ifconfig.me) 1194
resolv-retry infinite
nobind
persist-key
persist-tun
ca [inline]
cert [inline]
key [inline]
tls-auth [inline] 1
cipher AES-256-CBC
verb 3

<ca>
$(cat pki/ca.crt)
</ca>

<cert>
$(cat pki/issued/teste-fase5.crt)
</cert>

<key>
$(cat pki/private/teste-fase5.key)
</key>

<tls-auth>
$(cat pki/ta.key)
</tls-auth>
EOF

# 4.3 Testar conexão (em outro terminal/máquina)
# openvpn --config /tmp/teste-fase5.ovpn

# 4.4 Verificar se eventos são gerados
tail -f /root/VPN/OpenVPN/logs/events.log
# Deve mostrar CLIENT_CONNECT e CLIENT_DISCONNECT

# 4.5 Verificar se backend recebe eventos
docker service logs vpn-backend_vpn-backend --tail 10
# Deve mostrar logs de eventos recebidos
```

#### **PASSO 5: Integrar Frontend (QUINTO)**
```bash
# 5.1 Atualizar frontend para usar dados reais
# - Conectar WebSocket a wss://vpn.evo-eden.site/api/v1/ws
# - Substituir dados simulados por chamadas API reais
# - Implementar notificações em tempo real

# 5.2 Testar frontend integrado
curl https://vpn.evo-eden.site/api/v1/vpn/realtime-status
# Deve retornar dados reais da VPN

# 5.3 Verificar WebSocket
# Frontend deve mostrar conexões/desconexões em tempo real
```

### **🔧 SCRIPTS DE VERIFICAÇÃO**

#### **Script de Verificação Completa:**
```bash
#!/bin/bash
echo "=== VERIFICAÇÃO FASE 5 ==="

# 1. Backend endpoints
echo "1. Testando endpoints backend..."
CONNECT_TEST=$(curl -s -X POST http://localhost:8080/api/v1/internal/events/connect \
  -H "Content-Type: application/json" \
  -H "X-Internal-Token: vpn_internal_secret_2024" \
  -d '{"client_name":"test","external_ip":"*******","vpn_ip":"***********","timestamp":"2024-01-01T00:00:00Z","event_type":"connect"}')

if echo "$CONNECT_TEST" | grep -q "success"; then
    echo "✅ Backend endpoints: OK"
else
    echo "❌ Backend endpoints: FALHA"
fi

# 2. Scripts OpenVPN
echo "2. Verificando scripts OpenVPN..."
if [ -x "/root/VPN/OpenVPN/scripts/client-connect.sh" ] && [ -x "/root/VPN/OpenVPN/scripts/client-disconnect.sh" ]; then
    echo "✅ Scripts OpenVPN: OK"
else
    echo "❌ Scripts OpenVPN: FALHA"
fi

# 3. Configuração OpenVPN
echo "3. Verificando configuração OpenVPN..."
if grep -q "client-connect" /etc/openvpn/server.conf; then
    echo "✅ Configuração OpenVPN: OK"
else
    echo "❌ Configuração OpenVPN: FALHA"
fi

# 4. Management interface
echo "4. Verificando management interface..."
if netstat -tulpn | grep -q ":7505"; then
    echo "✅ Management interface: OK"
else
    echo "❌ Management interface: FALHA"
fi

# 5. OpenVPN rodando
echo "5. Verificando OpenVPN..."
if pgrep openvpn > /dev/null; then
    echo "✅ OpenVPN: OK"
else
    echo "❌ OpenVPN: FALHA"
fi

echo "=========================="
```

### **🎯 CRITÉRIOS DE SUCESSO FASE 5**

**✅ INTEGRAÇÃO COMPLETA QUANDO:**
1. **Backend recebe eventos OpenVPN** (connect/disconnect)
2. **Scripts OpenVPN executam sem erro**
3. **Management interface ativa** (porta 7505)
4. **Frontend mostra dados reais** (não simulados)
5. **WebSocket funciona** (notificações tempo real)
6. **Controle remoto funciona** (desconectar clientes via web)

**🚨 SINAIS DE FALHA:**
1. Scripts retornam erro ao notificar backend
2. Backend não recebe eventos (logs vazios)
3. Frontend ainda mostra dados simulados
4. Management interface não responde
5. WebSocket não conecta

---

**🎯 A Fase 5 criará um sistema VPN completamente integrado e unificado, onde OpenVPN, Backend e Frontend operam como uma solução única e coesa, com monitoramento em tempo real de todos os dispositivos conectados.**
