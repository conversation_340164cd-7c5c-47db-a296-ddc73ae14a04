version: "3.7"

services:
  vpn-frontend:
    image: vpn-frontend:20250814-200758
    networks:
      - redeinterna
    environment:
      - NODE_ENV=production
      - VITE_API_URL=https://vpn.evo-eden.site/api
      - VITE_WS_URL=wss://vpn.evo-eden.site/ws
    volumes:
      - /root/VPN/assets:/usr/share/nginx/html/company-assets:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.vpn-frontend.rule=Host(`vpn.evo-eden.site`)"
        - "traefik.http.routers.vpn-frontend.entrypoints=web"
        - "traefik.http.routers.vpn-frontend.middlewares=vpn-frontend-redirect"
        - "traefik.http.routers.vpn-frontend-secure.rule=Host(`vpn.evo-eden.site`)"
        - "traefik.http.routers.vpn-frontend-secure.entrypoints=websecure"
        - "traefik.http.routers.vpn-frontend-secure.tls=true"
        - "traefik.http.routers.vpn-frontend-secure.tls.certresolver=letsencryptresolver"
        - "traefik.http.middlewares.vpn-frontend-redirect.redirectscheme.scheme=https"
        - "traefik.http.middlewares.vpn-frontend-redirect.redirectscheme.permanent=true"
        - "traefik.http.middlewares.vpn-frontend-headers.headers.frameDeny=true"
        - "traefik.http.middlewares.vpn-frontend-headers.headers.sslRedirect=true"
        - "traefik.http.middlewares.vpn-frontend-headers.headers.browserXssFilter=true"
        - "traefik.http.middlewares.vpn-frontend-headers.headers.contentTypeNosniff=true"
        - "traefik.http.middlewares.vpn-frontend-headers.headers.forceSTSHeader=true"
        - "traefik.http.middlewares.vpn-frontend-headers.headers.stsSeconds=31536000"
        - "traefik.http.middlewares.vpn-frontend-headers.headers.stsIncludeSubdomains=true"
        - "traefik.http.middlewares.vpn-frontend-headers.headers.stsPreload=true"
        - "traefik.http.routers.vpn-frontend-secure.middlewares=vpn-frontend-headers"
        - "traefik.http.services.vpn-frontend.loadbalancer.server.port=80"

networks:
  redeinterna:
    external: true
    name: redeinterna


