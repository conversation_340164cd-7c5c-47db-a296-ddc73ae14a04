# Configurações de Desenvolvimento

# API Backend (Fase 4)
VITE_API_URL=http://localhost:8080/api
VITE_WS_URL=ws://localhost:8080/ws

# Configurações da Aplicação
VITE_APP_NAME=VPN Monitor (Dev)
VITE_APP_VERSION=3.0.0-dev
VITE_APP_DOMAIN=localhost:3000

# Configurações de Autenticação
VITE_AUTH_TOKEN_KEY=vpn_auth_token_dev
VITE_AUTH_REFRESH_INTERVAL=3600000

# Configurações de WebSocket
VITE_WS_RECONNECT_INTERVAL=3000
VITE_WS_MAX_RECONNECT_ATTEMPTS=5

# Configurações de Logs
VITE_LOG_LEVEL=debug
VITE_ENABLE_ANALYTICS=false

# Configurações de UI
VITE_THEME=dark
VITE_DEFAULT_LANGUAGE=pt-BR
VITE_TIMEZONE=America/Sao_Paulo

# Configurações de Segurança
VITE_ENABLE_DEVTOOLS=true
VITE_SECURE_COOKIES=false

# Credenciais padrão (apenas desenvolvimento)
VITE_DEFAULT_USERNAME=admin
VITE_DEFAULT_PASSWORD=VPNnbr5410!
