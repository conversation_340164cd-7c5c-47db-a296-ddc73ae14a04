@import "tailwindcss";

/* Reset básico */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Estilos base */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background-color: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8;
}

/* Animações personalizadas */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

/* Estilos para componentes de loading */
.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  border: 2px solid #e2e8f0;
  border-top-color: #3b82f6;
}
