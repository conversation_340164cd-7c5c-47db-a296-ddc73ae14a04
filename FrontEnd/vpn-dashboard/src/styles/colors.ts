// Paleta de cores dark baseada na logo da Evolution
export const evolutionColors = {
  // Cores primárias da logo
  primary: {
    green: '#4CAF50',      // Verde principal da logo
    darkGreen: '#2E7D32',  // Verde escuro
    lightGreen: '#81C784', // Verde claro
    blue: '#1976D2',       // Azul principal da logo
    darkBlue: '#0D47A1',   // Azul escuro
    lightBlue: '#42A5F5',  // Azul claro
  },

  // Cores neutras para tema dark
  neutral: {
    white: '#FFFFFF',
    lightGray: '#0a0e1a',    // Fundo muito escuro
    gray: 'rgba(255, 255, 255, 0.12)',  // Bordas escuras
    darkGray: 'rgba(255, 255, 255, 0.7)', // Texto secundário
    charcoal: '#1a1f2e',    // Cards e painéis
    black: '#242938',       // Superfícies intermediárias
  },

  // Cores de status
  status: {
    success: '#4CAF50',    // Verde para sucesso
    warning: '#FF9800',    // Laranja para avisos
    error: '#F44336',      // Vermelho para erros
    info: '#2196F3',       // Azul para informações
  },

  // Gradientes dark
  gradients: {
    primary: 'linear-gradient(135deg, #1976D2 0%, #4CAF50 100%)',
    secondary: 'linear-gradient(135deg, #2E7D32 0%, #0D47A1 100%)',
    light: 'linear-gradient(135deg, #1a1f2e 0%, #242938 100%)',
    dark: 'linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 100%)',
  },

  // Transparências
  alpha: {
    primary10: 'rgba(25, 118, 210, 0.1)',
    primary20: 'rgba(25, 118, 210, 0.2)',
    green10: 'rgba(76, 175, 80, 0.1)',
    green20: 'rgba(76, 175, 80, 0.2)',
    white90: 'rgba(255, 255, 255, 0.9)',
    white10: 'rgba(255, 255, 255, 0.1)',
    white05: 'rgba(255, 255, 255, 0.05)',
    black10: 'rgba(0, 0, 0, 0.1)',
    black20: 'rgba(0, 0, 0, 0.2)',
    black30: 'rgba(0, 0, 0, 0.3)',
  }
};

// Tema customizado dark para Material-UI
export const evolutionTheme = {
  palette: {
    mode: 'dark' as const,
    primary: {
      main: evolutionColors.primary.blue,
      dark: evolutionColors.primary.darkBlue,
      light: evolutionColors.primary.lightBlue,
    },
    secondary: {
      main: evolutionColors.primary.green,
      dark: evolutionColors.primary.darkGreen,
      light: evolutionColors.primary.lightGreen,
    },
    success: {
      main: evolutionColors.status.success,
    },
    warning: {
      main: evolutionColors.status.warning,
    },
    error: {
      main: evolutionColors.status.error,
    },
    info: {
      main: evolutionColors.status.info,
    },
    background: {
      default: evolutionColors.neutral.lightGray,
      paper: evolutionColors.neutral.charcoal,
    },
    text: {
      primary: evolutionColors.neutral.white,
      secondary: evolutionColors.neutral.darkGray,
    },
    divider: evolutionColors.neutral.gray,
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 600,
      color: evolutionColors.neutral.white,
    },
    h5: {
      fontWeight: 600,
      color: evolutionColors.neutral.white,
    },
    h6: {
      fontWeight: 600,
      color: evolutionColors.neutral.white,
    },
    body1: {
      color: evolutionColors.neutral.white,
    },
    body2: {
      color: evolutionColors.neutral.darkGray,
    },
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          backgroundColor: evolutionColors.neutral.lightGray,
          color: evolutionColors.neutral.white,
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: evolutionColors.neutral.charcoal,
          borderBottom: `1px solid ${evolutionColors.neutral.gray}`,
          color: evolutionColors.neutral.white,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          backgroundColor: evolutionColors.neutral.charcoal,
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
          border: `1px solid ${evolutionColors.neutral.gray}`,
          color: evolutionColors.neutral.white,
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundColor: evolutionColors.neutral.charcoal,
          color: evolutionColors.neutral.white,
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none' as const,
          fontWeight: 600,
        },
        contained: {
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
          '&:hover': {
            boxShadow: '0 6px 12px rgba(0, 0, 0, 0.4)',
          },
        },
      },
    },
    MuiTableContainer: {
      styleOverrides: {
        root: {
          backgroundColor: evolutionColors.neutral.charcoal,
        },
      },
    },
    MuiTable: {
      styleOverrides: {
        root: {
          '& .MuiTableHead-root': {
            backgroundColor: evolutionColors.neutral.black,
          },
          '& .MuiTableRow-root': {
            '&:nth-of-type(odd)': {
              backgroundColor: evolutionColors.alpha.white05,
            },
            '&:hover': {
              backgroundColor: evolutionColors.alpha.white10,
            },
          },
          '& .MuiTableCell-root': {
            borderColor: evolutionColors.neutral.gray,
            color: evolutionColors.neutral.white,
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          backgroundColor: evolutionColors.neutral.black,
          color: evolutionColors.neutral.white,
          border: `1px solid ${evolutionColors.neutral.gray}`,
        },
      },
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          color: evolutionColors.neutral.darkGray,
          '&:hover': {
            backgroundColor: evolutionColors.alpha.white10,
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            backgroundColor: evolutionColors.neutral.black,
            '& fieldset': {
              borderColor: evolutionColors.neutral.gray,
            },
            '&:hover fieldset': {
              borderColor: evolutionColors.neutral.darkGray,
            },
            '&.Mui-focused fieldset': {
              borderColor: evolutionColors.primary.blue,
            },
          },
          '& .MuiInputLabel-root': {
            color: evolutionColors.neutral.darkGray,
          },
          '& .MuiOutlinedInput-input': {
            color: evolutionColors.neutral.white,
          },
        },
      },
    },
  },
};
