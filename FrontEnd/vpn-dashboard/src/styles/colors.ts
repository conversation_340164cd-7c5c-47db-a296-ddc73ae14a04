// Paleta de cores baseada na logo da Evolution
export const evolutionColors = {
  // Cores primárias da logo
  primary: {
    green: '#4CAF50',      // Verde principal da logo
    darkGreen: '#2E7D32',  // Verde escuro
    lightGreen: '#81C784', // Verde claro
    blue: '#1976D2',       // Azul principal da logo
    darkBlue: '#0D47A1',   // Azul escuro
    lightBlue: '#42A5F5',  // Azul claro
  },
  
  // Cores neutras profissionais
  neutral: {
    white: '#FFFFFF',
    lightGray: '#F5F5F5',
    gray: '#E0E0E0',
    darkGray: '#757575',
    charcoal: '#424242',
    black: '#212121',
  },
  
  // Cores de status
  status: {
    success: '#4CAF50',    // Verde para sucesso
    warning: '#FF9800',    // Laranja para avisos
    error: '#F44336',      // Vermelho para erros
    info: '#2196F3',       // Azul para informações
  },
  
  // Gradientes
  gradients: {
    primary: 'linear-gradient(135deg, #1976D2 0%, #4CAF50 100%)',
    secondary: 'linear-gradient(135deg, #2E7D32 0%, #0D47A1 100%)',
    light: 'linear-gradient(135deg, #E3F2FD 0%, #E8F5E8 100%)',
  },
  
  // Transparências
  alpha: {
    primary10: 'rgba(25, 118, 210, 0.1)',
    primary20: 'rgba(25, 118, 210, 0.2)',
    green10: 'rgba(76, 175, 80, 0.1)',
    green20: 'rgba(76, 175, 80, 0.2)',
    white90: 'rgba(255, 255, 255, 0.9)',
    black10: 'rgba(0, 0, 0, 0.1)',
    black20: 'rgba(0, 0, 0, 0.2)',
  }
};

// Tema customizado para Material-UI
export const evolutionTheme = {
  palette: {
    primary: {
      main: evolutionColors.primary.blue,
      dark: evolutionColors.primary.darkBlue,
      light: evolutionColors.primary.lightBlue,
    },
    secondary: {
      main: evolutionColors.primary.green,
      dark: evolutionColors.primary.darkGreen,
      light: evolutionColors.primary.lightGreen,
    },
    success: {
      main: evolutionColors.status.success,
    },
    warning: {
      main: evolutionColors.status.warning,
    },
    error: {
      main: evolutionColors.status.error,
    },
    info: {
      main: evolutionColors.status.info,
    },
    background: {
      default: evolutionColors.neutral.lightGray,
      paper: evolutionColors.neutral.white,
    },
    text: {
      primary: evolutionColors.neutral.charcoal,
      secondary: evolutionColors.neutral.darkGray,
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 600,
      color: evolutionColors.neutral.charcoal,
    },
    h5: {
      fontWeight: 600,
      color: evolutionColors.neutral.charcoal,
    },
    h6: {
      fontWeight: 600,
      color: evolutionColors.neutral.charcoal,
    },
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          border: `1px solid ${evolutionColors.neutral.gray}`,
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none' as const,
          fontWeight: 600,
        },
      },
    },
  },
};
