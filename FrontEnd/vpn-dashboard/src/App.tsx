import { useState, useEffect } from 'react';
import { LoginPage } from './components/LoginPage';
import { ProfessionalDashboard } from './components/ProfessionalDashboard';
import { authService, type User } from './services/auth';
import { CssBaseline } from '@mui/material';
import './index.css';

function App() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Verificar se o usuário já está autenticado
    const currentUser = authService.getCurrentUser();
    setUser(currentUser);
    setIsLoading(false);
  }, []);

  const handleLogin = (loggedInUser: User) => {
    setUser(loggedInUser);
  };

  const handleLogout = () => {
    setUser(null);
  };

  if (isLoading) {
    return <div>Carregando...</div>;
  }

  return (
    <>
      <CssBaseline />
      {user ? (
        <ProfessionalDashboard user={user} onLogout={handleLogout} />
      ) : (
        <LoginPage onLogin={handleLogin} />
      )}
    </>
  );
}

export default App;
