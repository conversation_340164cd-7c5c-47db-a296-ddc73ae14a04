import { useState, useEffect } from 'react';
import { LoginPage } from './components/LoginPage';
import { ProfessionalDashboard } from './components/ProfessionalDashboard';
import { authService, type User } from './services/auth';
import { CssBaseline, ThemeProvider, createTheme } from '@mui/material';
import './index.css';

// Tema customizado baseado nas cores da logo
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2', // Azul principal
      light: '#42a5f5',
      dark: '#1565c0',
    },
    secondary: {
      main: '#00bcd4', // Ciano
      light: '#4dd0e1',
      dark: '#00acc1',
    },
    success: {
      main: '#4caf50', // Verde
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 700,
    },
    h6: {
      fontWeight: 600,
    },
  },
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 12,
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 12,
          },
        },
      },
    },
  },
});

function App() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Verificar se o usuário já está autenticado
    const currentUser = authService.getCurrentUser();
    setUser(currentUser);
    setIsLoading(false);
  }, []);

  const handleLogin = (loggedInUser: User) => {
    setUser(loggedInUser);
  };

  const handleLogout = () => {
    setUser(null);
  };

  if (isLoading) {
    return <div>Carregando...</div>;
  }

  return (
    <>
      <CssBaseline />
      {user ? (
        <ProfessionalDashboard user={user} onLogout={handleLogout} />
      ) : (
        <LoginPage onLogin={handleLogin} />
      )}
    </>
  );
}

export default App;
