import React, { useState } from 'react';
import {
  <PERSON>,
  CardContent,
  TextField,
  Button,
  Typography,
  InputAdornment,
  IconButton,
  Alert,
  CircularProgress,
  Container,
  Paper,
  Fade
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Person,
  Lock,
  VpnKey,
  Security
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { authService, type User } from '../services/auth';

interface LoginPageProps {
  onLogin: (user: User) => void;
}

export function LoginPage({ onLogin }: LoginPageProps) {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const response = await authService.login(username, password);
      if (response.success && response.user) {
        onLogin(response.user);
      } else {
        setError('Credenciais inválidas. Verifique seu login e senha.');
      }
    } catch (err) {
      setError('Erro ao conectar. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(135deg,
          #1565c0 0%,
          #0277bd 25%,
          #00838f 50%,
          #00695c 75%,
          #2e7d32 100%)`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 2,
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='20' cy='20' r='1.5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          zIndex: 0,
        }
      }}
    >
      <Container maxWidth="xs" sx={{ position: 'relative', zIndex: 1 }}>
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <Paper
            elevation={24}
            sx={{
              borderRadius: 3,
              overflow: 'hidden',
              background: 'rgba(255, 255, 255, 0.98)',
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              boxShadow: '0 16px 32px rgba(0, 0, 0, 0.15)',
            }}
          >
            {/* Header Section */}
            <Box
              sx={{
                background: `linear-gradient(135deg,
                  #1565c0 0%,
                  #0277bd 25%,
                  #00838f 50%,
                  #00695c 75%,
                  #2e7d32 100%)`,
                padding: 3,
                textAlign: 'center',
                position: 'relative',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  right: 0,
                  height: '1px',
                  background: 'rgba(255, 255, 255, 0.3)',
                }
              }}
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.3, duration: 0.5, type: "spring" }}
              >
                <Box
                  sx={{
                    width: 140,
                    height: 140,
                    margin: '0 auto 32px',
                    background: 'rgba(255, 255, 255, 0.95)',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backdropFilter: 'blur(10px)',
                    border: '3px solid rgba(255, 255, 255, 0.8)',
                    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                  }}
                >
                  <img
                    src="/assets/logo_sem_fundo_branco.png"
                    alt="Evolution Logo"
                    style={{
                      width: '110px',
                      height: '110px',
                      objectFit: 'contain',
                    }}
                  />
                </Box>
              </motion.div>

              <Typography
                variant="h3"
                sx={{
                  color: 'white',
                  fontWeight: 700,
                  marginBottom: 1,
                  textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)',
                  fontSize: '2.2rem',
                }}
              >
                VPN Evolution
              </Typography>
              <Typography
                variant="h6"
                sx={{
                  color: 'rgba(255, 255, 255, 0.95)',
                  fontWeight: 400,
                  fontSize: '1.1rem',
                }}
              >
                Sistema de Gestão dos Serviços VPN Evolution
              </Typography>
            </Box>

            {/* Form Section */}
            <CardContent sx={{ padding: 3 }}>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5, duration: 0.5 }}
              >
                <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
                  <TextField
                    fullWidth
                    id="username"
                    label="Email ou Login"
                    variant="outlined"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    required
                    size="medium"
                    sx={{
                      mb: 2.5,
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        height: '48px',
                        '&:hover fieldset': {
                          borderColor: '#1565c0',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#1565c0',
                        },
                      },
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Person sx={{ color: '#1565c0' }} />
                        </InputAdornment>
                      ),
                    }}
                  />

                  <TextField
                    fullWidth
                    id="password"
                    label="Senha"
                    type={showPassword ? 'text' : 'password'}
                    variant="outlined"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    size="medium"
                    sx={{
                      mb: 2.5,
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        height: '48px',
                        '&:hover fieldset': {
                          borderColor: '#1565c0',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#1565c0',
                        },
                      },
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Lock sx={{ color: '#1565c0' }} />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                            edge="end"
                            sx={{ color: '#1565c0' }}
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />

                  {error && (
                    <Fade in={!!error}>
                      <Alert
                        severity="error"
                        sx={{
                          mb: 2.5,
                          borderRadius: 2,
                        }}
                      >
                        {error}
                      </Alert>
                    </Fade>
                  )}

                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    disabled={isLoading}
                    sx={{
                      py: 1.2,
                      borderRadius: 2,
                      background: 'linear-gradient(135deg, #1565c0 0%, #0277bd 25%, #00838f 50%, #00695c 75%, #2e7d32 100%)',
                      fontSize: '1rem',
                      fontWeight: 600,
                      textTransform: 'none',
                      height: '48px',
                      boxShadow: '0 6px 12px rgba(21, 101, 192, 0.3)',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #0d47a1 0%, #01579b 25%, #006064 50%, #004d40 75%, #1b5e20 100%)',
                        boxShadow: '0 8px 16px rgba(21, 101, 192, 0.4)',
                        transform: 'translateY(-1px)',
                      },
                      '&:disabled': {
                        background: 'rgba(0, 0, 0, 0.12)',
                      },
                      transition: 'all 0.3s ease',
                    }}
                    startIcon={
                      isLoading ? (
                        <CircularProgress size={18} color="inherit" />
                      ) : (
                        <VpnKey />
                      )
                    }
                  >
                    {isLoading ? 'Entrando...' : 'Entrar'}
                  </Button>

                  <Box sx={{ textAlign: 'center', mt: 2.5 }}>
                    <Button
                      variant="text"
                      sx={{
                        color: '#1565c0',
                        textTransform: 'none',
                        fontSize: '0.9rem',
                        '&:hover': {
                          backgroundColor: 'rgba(21, 101, 192, 0.04)',
                        },
                      }}
                    >
                      Esqueci minha senha
                    </Button>
                  </Box>
                </Box>
              </motion.div>
            </CardContent>

            {/* Footer */}
            <Box
              sx={{
                background: 'rgba(21, 101, 192, 0.08)',
                padding: 1.5,
                textAlign: 'center',
                borderTop: '1px solid rgba(0, 0, 0, 0.08)',
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                <Security sx={{ color: '#1565c0', fontSize: 14 }} />
                <Typography variant="caption" sx={{ color: 'text.secondary', fontSize: '0.75rem' }}>
                  Conexão segura e criptografada
                </Typography>
              </Box>
            </Box>
          </Paper>
        </motion.div>
      </Container>
    </Box>
  );
}