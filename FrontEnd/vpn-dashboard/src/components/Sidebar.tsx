import { motion } from 'framer-motion';
import {
  Shield,
  BarChart3,
  Users,
  Network,
  FileText,
  AlertTriangle,
  Settings,
  Moon,
  Sun
} from 'lucide-react';
import { Button } from './ui/button';

interface SidebarProps {
  activeItem: string;
  onItemClick: (item: string) => void;
  isDarkMode: boolean;
  onToggleTheme: () => void;
}

const menuItems = [
  { id: 'overview', label: 'Visão Geral', icon: BarChart3 },
  { id: 'clients', label: 'Clientes', icon: Users },
  { id: 'network', label: 'Rede', icon: Network },
  { id: 'reports', label: 'Relatórios', icon: FileText },
  { id: 'alerts', label: 'Alertas', icon: AlertTriangle },
  { id: 'settings', label: 'Configurações', icon: Settings },
];

export function Sidebar({ activeItem, onItemClick, isDarkMode, onToggleTheme }: SidebarProps) {
  return (
    <motion.div
      initial={{ x: -250 }}
      animate={{ x: 0 }}
      transition={{ duration: 0.3 }}
      className="w-64 bg-slate-900 text-white h-screen fixed left-0 top-0 z-50 flex flex-col"
    >
      {/* Header */}
      <div className="p-6 border-b border-slate-700">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
            <Shield className="h-6 w-6 text-white" />
          </div>
          <div>
            <h2 className="text-lg font-bold text-white">VPN Monitor</h2>
            <p className="text-xs text-slate-400">Sistema de Monitoramento</p>
          </div>
        </div>
      </div>

      {/* Menu Items */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeItem === item.id;
            
            return (
              <li key={item.id}>
                <Button
                  variant="ghost"
                  className={`w-full justify-start text-left p-3 h-auto ${
                    isActive 
                      ? 'bg-blue-600 text-white hover:bg-blue-700' 
                      : 'text-slate-300 hover:bg-slate-800 hover:text-white'
                  }`}
                  onClick={() => onItemClick(item.id)}
                >
                  <Icon className="h-5 w-5 mr-3" />
                  {item.label}
                </Button>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-slate-700">
        <div className="flex items-center justify-between">
          <span className="text-sm text-slate-400">Tema</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleTheme}
            className="text-slate-300 hover:text-white"
          >
            {isDarkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
            <span className="ml-2 text-xs">
              {isDarkMode ? 'Escuro' : 'Claro'}
            </span>
          </Button>
        </div>
        <div className="mt-3 text-xs text-slate-500">
          Modo Escuro
        </div>
      </div>
    </motion.div>
  );
}
