import { useState, useEffect } from 'react';
import {
  Box,
  CssBaseline,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Card,
  CardContent,


  Avatar,
  Button,
  ThemeProvider,
  CircularProgress,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  NetworkCheck as NetworkIcon,
  Assessment as ReportsIcon,
  Warning as WarningIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Shield as ShieldIcon,
  TrendingUp as TrendingUpIcon,
  Computer as ComputerIcon,
  Router as RouterIcon,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, Legend } from 'recharts';
import { darkTheme } from '../theme';
// import { LoginPage } from './LoginPage';
import { authService, type User } from '../services/auth';
import type { DashboardMetrics } from '../types';

const drawerWidth = 280;

const menuItems = [
  { id: 'overview', label: 'Visão Geral', icon: DashboardIcon },
  { id: 'clients', label: 'Clientes', icon: PeopleIcon },
  { id: 'sessions', label: 'Sessões', icon: NetworkIcon },
  { id: 'reports', label: 'Relatórios', icon: ReportsIcon },
  { id: 'alerts', label: 'Alertas', icon: WarningIcon },
  { id: 'settings', label: 'Configurações', icon: SettingsIcon },
];

// Dados vazios para gráficos (serão preenchidos pela API)
const getEmptyActivityData = () => [
  { time: '00:00', value: 0 },
  { time: '04:00', value: 0 },
  { time: '08:00', value: 0 },
  { time: '12:00', value: 0 },
  { time: '16:00', value: 0 },
  { time: '20:00', value: 0 },
  { time: '24:00', value: 0 },
];

const getEmptyClientDistributionData = () => [
  { name: 'Nenhum cliente', value: 1, color: '#9e9e9e' },
];

export function Dashboard() {
  const [data, setData] = useState<DashboardMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [activeMenuItem, setActiveMenuItem] = useState('overview');
  const [mobileOpen, setMobileOpen] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      const user = await authService.getCurrentUser();
      if (user) {
        setIsAuthenticated(true);
        setCurrentUser(user);
      }
      setIsLoading(false);
    };

    checkAuth();
  }, []);

  useEffect(() => {
    if (isAuthenticated) {
      const fetchData = async () => {
        setIsLoading(true);

        try {
          // Buscar dados reais da API
          const token = localStorage.getItem('token');
          const response = await fetch('/api/v1/metrics/dashboard', {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          if (response.ok) {
            const realData = await response.json();
            setData(realData);
          } else {
            // Se API não estiver disponível, mostrar dados vazios
            const emptyData: DashboardMetrics = {
              activeClients: 0,
              activeClientsChange: 'Nenhum cliente conectado',
              dailySessions: 0,
              dailySessionsChange: 'Nenhuma sessão hoje',
              networkEvents: {
                total: 0,
                ssh: 0,
                vnc: 0,
                rdp: 0
              },
              systemUptime: {
                percentage: 0,
                consecutiveDays: 0
              },
              activeAlerts: {
                total: 0,
                critical: 0,
                warning: 0
              },
              lastUpdated: new Date()
            };
            setData(emptyData);
          }
        } catch (error) {
          console.error('Erro ao buscar dados:', error);
          // Dados vazios em caso de erro
          const emptyData: DashboardMetrics = {
            activeClients: 0,
            activeClientsChange: 'Erro ao carregar dados',
            dailySessions: 0,
            dailySessionsChange: 'Erro ao carregar dados',
            networkEvents: {
              total: 0,
              ssh: 0,
              vnc: 0,
              rdp: 0
            },
            systemUptime: {
              percentage: 0,
              consecutiveDays: 0
            },
            activeAlerts: {
              total: 0,
              critical: 0,
              warning: 0
            },
            lastUpdated: new Date()
          };
          setData(emptyData);
        } finally {
          setIsLoading(false);
        }
      };

      fetchData();
    }
  }, [isAuthenticated]);

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/v1/metrics/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      if (response.ok) {
        const realData = await response.json();
        setData(realData);
      }
    } catch (error) {
      console.error('Erro ao atualizar dados:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // const handleLogin = async (username: string, password: string): Promise<boolean> => {
  //   const result = await authService.login(username, password);
  //   if (result.success && result.user) {
  //     setIsAuthenticated(true);
  //     setCurrentUser(result.user);
  //     return true;
  //   }
  //   return false;
  // };



  const handleMenuItemClick = (item: string) => {
    setActiveMenuItem(item);
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  // Componente Sidebar
  const drawer = (
    <Box>
      <Box sx={{ p: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
        <Avatar sx={{ bgcolor: 'primary.main' }}>
          <ShieldIcon />
        </Avatar>
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            VPN Monitor
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Sistema de Monitoramento
          </Typography>
        </Box>
      </Box>
      <Divider />
      <List>
        {menuItems.map((item) => {
          const Icon = item.icon;
          return (
            <ListItem key={item.id} disablePadding>
              <ListItemButton
                selected={activeMenuItem === item.id}
                onClick={() => handleMenuItemClick(item.id)}
                sx={{
                  mx: 1,
                  borderRadius: 1,
                  '&.Mui-selected': {
                    backgroundColor: 'primary.main',
                    '&:hover': {
                      backgroundColor: 'primary.dark',
                    },
                  },
                }}
              >
                <ListItemIcon>
                  <Icon color={activeMenuItem === item.id ? 'inherit' : 'primary'} />
                </ListItemIcon>
                <ListItemText primary={item.label} />
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>
    </Box>
  );

  if (!isAuthenticated) {
    return <div>Dashboard antigo - não usado</div>;
  }

  if (isLoading && !data) {
    return (
      <ThemeProvider theme={darkTheme}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100vh',
            bgcolor: 'background.default',
          }}
        >
          <Box sx={{ textAlign: 'center' }}>
            <CircularProgress size={60} />
            <Typography variant="h6" sx={{ mt: 2 }}>
              Carregando dashboard...
            </Typography>
          </Box>
        </Box>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={darkTheme}>
      <Box sx={{ display: 'flex' }}>
        <CssBaseline />

        {/* AppBar */}
        <AppBar
          position="fixed"
          sx={{
            width: { sm: `calc(100% - ${drawerWidth}px)` },
            ml: { sm: `${drawerWidth}px` },
          }}
        >
          <Toolbar>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2, display: { sm: 'none' } }}
            >
              <MenuIcon />
            </IconButton>

            <Box sx={{ flexGrow: 1 }}>
              <Typography variant="h6" noWrap component="div">
                VPN Monitor
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Sistema de Gestão dos Serviços VPN - {new Date().toLocaleDateString('pt-BR')}, {new Date().toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box sx={{ textAlign: 'right' }}>
                <Typography variant="body2">Bem-vindo,</Typography>
                <Typography variant="subtitle2">{currentUser?.username}</Typography>
              </Box>
              <Button
                variant="contained"
                startIcon={<RefreshIcon />}
                onClick={handleRefresh}
                disabled={isLoading}
                size="small"
              >
                Atualizar
              </Button>
            </Box>
          </Toolbar>
        </AppBar>

        {/* Drawer */}
        <Box
          component="nav"
          sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
        >
          <Drawer
            variant="temporary"
            open={mobileOpen}
            onClose={handleDrawerToggle}
            ModalProps={{
              keepMounted: true,
            }}
            sx={{
              display: { xs: 'block', sm: 'none' },
              '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
            }}
          >
            {drawer}
          </Drawer>
          <Drawer
            variant="permanent"
            sx={{
              display: { xs: 'none', sm: 'block' },
              '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
            }}
            open
          >
            {drawer}
          </Drawer>
        </Box>

        {/* Main Content */}
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            p: 3,
            width: { sm: `calc(100% - ${drawerWidth}px)` },
          }}
        >
          <Toolbar />

          {/* Renderizar conteúdo baseado na aba ativa */}
          {renderActiveContent()}
        </Box>
      </Box>
    </ThemeProvider>
  );

  function renderActiveContent() {
    switch (activeMenuItem) {
      case 'overview':
        return renderOverviewContent();
      case 'clients':
        return renderClientsContent();
      case 'sessions':
        return renderSessionsContent();
      default:
        return renderOverviewContent();
    }
  }

  function renderOverviewContent() {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
        {/* Metrics Cards */}
        <Box sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: 3,
          mb: 4
        }}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="overline">
                      Clientes Ativos
                    </Typography>
                    <Typography variant="h4" component="div">
                      {data?.activeClients || 0}
                    </Typography>
                    <Typography variant="body2" color={data?.activeClients ? "success.main" : "text.secondary"}>
                      {data?.activeClientsChange || 'Nenhum cliente conectado'}
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'primary.main' }}>
                    <PeopleIcon />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>

            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="overline">
                      Sessões do Dia
                    </Typography>
                    <Typography variant="h4" component="div">
                      {data?.dailySessions || 0}
                    </Typography>
                    <Typography variant="body2" color={data?.dailySessions ? "success.main" : "text.secondary"}>
                      {data?.dailySessionsChange || 'Nenhuma sessão hoje'}
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'success.main' }}>
                    <TrendingUpIcon />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>

            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="overline">
                      Eventos de Rede
                    </Typography>
                    <Typography variant="h4" component="div">
                      {data?.networkEvents?.total || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {data?.networkEvents ?
                        `SSH ${data.networkEvents.ssh}, VNC ${data.networkEvents.vnc}, RDP ${data.networkEvents.rdp}` :
                        'Nenhum evento registrado'
                      }
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'info.main' }}>
                    <RouterIcon />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>

            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="overline">
                      Uptime
                    </Typography>
                    <Typography variant="h4" component="div">
                      {data?.systemUptime?.percentage || 0}%
                    </Typography>
                    <Typography variant="body2" color={data?.systemUptime?.percentage ? "success.main" : "text.secondary"}>
                      {data?.systemUptime?.consecutiveDays ?
                        `Ø ${data.systemUptime.consecutiveDays} dias consecutivos` :
                        'Sistema não monitorado'
                      }
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'success.main' }}>
                    <ComputerIcon />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>

            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="overline">
                      Alertas Ativos
                    </Typography>
                    <Typography variant="h4" component="div">
                      {data?.activeAlerts?.total || 0}
                    </Typography>
                    <Typography variant="body2" color={data?.activeAlerts?.total ? "warning.main" : "success.main"}>
                      {data?.activeAlerts?.total ?
                        `${data.activeAlerts.critical} críticos, ${data.activeAlerts.warning} avisos` :
                        'Nenhum alerta ativo'
                      }
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: 'warning.main' }}>
                    <WarningIcon />
                  </Avatar>
                </Box>
              </CardContent>
          </Card>
        </Box>

        {/* Charts */}
        <Box sx={{
          display: 'grid',
          gridTemplateColumns: { xs: '1fr', lg: '2fr 1fr' },
          gap: 3
        }}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Atividade em Tempo Real
                </Typography>
                <Box sx={{ height: 400 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={data?.activityData || getEmptyActivityData()}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#2d3843" />
                      <XAxis
                        dataKey="time"
                        stroke="#b2bac2"
                        fontSize={12}
                      />
                      <YAxis
                        stroke="#b2bac2"
                        fontSize={12}
                      />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: '#132f4c',
                          border: '1px solid #2d3843',
                          borderRadius: '8px',
                        }}
                      />
                      <Line
                        type="monotone"
                        dataKey="value"
                        stroke="#1976d2"
                        strokeWidth={3}
                        dot={false}
                        activeDot={{ r: 6, fill: '#1976d2' }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>

            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Distribuição de Clientes
                </Typography>
                <Box sx={{ height: 400 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={data?.clientDistributionData || getEmptyClientDistributionData()}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {(data?.clientDistributionData || getEmptyClientDistributionData()).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip
                        contentStyle={{
                          backgroundColor: '#132f4c',
                          border: '1px solid #2d3843',
                          borderRadius: '8px',
                        }}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
            </CardContent>
          </Card>
        </Box>
      </Box>
    );
  }

  function renderClientsContent() {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
        <Typography variant="h4" gutterBottom>
          Clientes VPN
        </Typography>

        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Lista de Clientes Conectados
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Nenhum cliente conectado no momento.
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              Para ver clientes aqui, conecte-se à VPN usando um certificado válido.
            </Typography>
          </CardContent>
        </Card>
      </Box>
    );
  }

  function renderSessionsContent() {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
        <Typography variant="h4" gutterBottom>
          Sessões VPN
        </Typography>

        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Histórico de Sessões
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Nenhuma sessão registrada ainda.
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              As sessões aparecerão aqui quando clientes se conectarem à VPN.
            </Typography>
          </CardContent>
        </Card>
      </Box>
    );
  }
}
