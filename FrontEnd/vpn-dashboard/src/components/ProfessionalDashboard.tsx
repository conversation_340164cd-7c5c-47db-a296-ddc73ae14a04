import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  AppBar,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Card,
  CardContent,


  Avatar,
  IconButton,
  Chip,
  CircularProgress,
  Button,
  Container,
} from '@mui/material';
import {
  VpnKey as VpnIcon,
  Computer as ComputerIcon,
  Security as SecurityIcon,
  Speed as SpeedIcon,
  Refresh as RefreshIcon,
  ExitToApp as LogoutIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  History as HistoryIcon,
} from '@mui/icons-material';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { evolutionTheme, evolutionColors } from '../styles/colors';
import { authService, type User } from '../services/auth';
import type { DashboardMetrics } from '../types';
import logoEvolution from '../assets/logo.png';

// Tema customizado baseado nas cores da Evolution
const theme = createTheme(evolutionTheme);

interface ProfessionalDashboardProps {
  user: User;
  onLogout: () => void;
}

export const ProfessionalDashboard: React.FC<ProfessionalDashboardProps> = ({ user, onLogout }) => {
  const [data, setData] = useState<DashboardMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'clients' | 'sessions'>('overview');

  useEffect(() => {
    fetchDashboardData();
    // Atualizar dados a cada 30 segundos
    const interval = setInterval(fetchDashboardData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchDashboardData = async () => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/v1/metrics/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      if (response.ok) {
        const realData = await response.json();
        setData(realData);
      } else {
        // Dados vazios se API não estiver disponível
        setData({
          activeClients: 0,
          activeClientsChange: 'API não disponível',
          dailySessions: 0,
          dailySessionsChange: 'API não disponível',
          networkEvents: { total: 0, ssh: 0, vnc: 0, rdp: 0 },
          systemUptime: { percentage: 0, consecutiveDays: 0 },
          activeAlerts: { total: 0, critical: 0, warning: 0 },
          lastUpdated: new Date()
        });
      }
    } catch (error) {
      console.error('Erro ao buscar dados:', error);
      setData({
        activeClients: 0,
        activeClientsChange: 'Erro de conexão',
        dailySessions: 0,
        dailySessionsChange: 'Erro de conexão',
        networkEvents: { total: 0, ssh: 0, vnc: 0, rdp: 0 },
        systemUptime: { percentage: 0, consecutiveDays: 0 },
        activeAlerts: { total: 0, critical: 0, warning: 0 },
        lastUpdated: new Date()
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchDashboardData();
  };

  const handleLogout = async () => {
    await authService.logout();
    onLogout();
  };

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ flexGrow: 1, bgcolor: evolutionColors.neutral.lightGray, minHeight: '100vh' }}>
        {/* Header */}
        <AppBar
          position="static"
          elevation={0}
          sx={{
            bgcolor: evolutionColors.neutral.charcoal,
            borderBottom: `1px solid ${evolutionColors.neutral.gray}`,
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',
          }}
        >
          <Toolbar>
            <Avatar
              src={logoEvolution}
              alt="Evolution"
              sx={{ mr: 2, width: 40, height: 40 }}
            />
            <Box sx={{ flexGrow: 1 }}>
              <Typography variant="h6" sx={{ color: evolutionColors.neutral.white, fontWeight: 600 }}>
                VPN Monitor
              </Typography>
              <Typography variant="caption" sx={{ color: evolutionColors.neutral.darkGray }}>
                Sistema de Gestão dos Serviços VPN Evolution
              </Typography>
            </Box>

            <Chip
              label={`Conectado como ${user.username}`}
              avatar={<Avatar sx={{ bgcolor: evolutionColors.primary.green }}>{user.username[0].toUpperCase()}</Avatar>}
              sx={{
                mr: 2,
                bgcolor: evolutionColors.neutral.black,
                color: evolutionColors.neutral.white,
                border: `1px solid ${evolutionColors.neutral.gray}`,
              }}
            />

            <IconButton
              onClick={handleRefresh}
              disabled={isLoading}
              sx={{
                color: evolutionColors.neutral.white,
                '&:hover': { bgcolor: evolutionColors.alpha.white10 }
              }}
            >
              <RefreshIcon />
            </IconButton>

            <IconButton
              onClick={handleLogout}
              sx={{
                color: evolutionColors.status.error,
                '&:hover': { bgcolor: 'rgba(244, 67, 54, 0.1)' }
              }}
            >
              <LogoutIcon />
            </IconButton>
          </Toolbar>
        </AppBar>

        {/* Navigation Tabs */}
        <Box sx={{
          bgcolor: evolutionColors.neutral.black,
          borderBottom: `1px solid ${evolutionColors.neutral.gray}`,
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
        }}>
          <Container maxWidth="xl">
            <Box sx={{ display: 'flex', gap: 1, py: 1 }}>
              {[
                { id: 'overview', label: 'Visão Geral', icon: TrendingUpIcon },
                { id: 'clients', label: 'Clientes VPN', icon: PeopleIcon },
                { id: 'sessions', label: 'Sessões', icon: HistoryIcon },
              ].map((tab) => (
                <Button
                  key={tab.id}
                  startIcon={<tab.icon />}
                  onClick={() => setActiveTab(tab.id as any)}
                  variant={activeTab === tab.id ? 'contained' : 'text'}
                  sx={{
                    borderRadius: 2,
                    textTransform: 'none',
                    fontWeight: 500,
                    color: activeTab === tab.id ? evolutionColors.neutral.white : evolutionColors.neutral.darkGray,
                    '&:hover': {
                      bgcolor: activeTab === tab.id ? evolutionColors.primary.darkBlue : evolutionColors.alpha.white10,
                    },
                    ...(activeTab === tab.id && {
                      bgcolor: evolutionColors.primary.blue,
                      color: evolutionColors.neutral.white,
                      '&:hover': { bgcolor: evolutionColors.primary.darkBlue }
                    })
                  }}
                >
                  {tab.label}
                </Button>
              ))}
            </Box>
          </Container>
        </Box>

        {/* Content */}
        <Container maxWidth="xl" sx={{ py: 3 }}>
          {isLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
              <CircularProgress size={60} />
            </Box>
          ) : (
            renderContent()
          )}
        </Container>
      </Box>
    </ThemeProvider>
  );

  function renderContent() {
    switch (activeTab) {
      case 'overview':
        return renderOverview();
      case 'clients':
        return renderClients();
      case 'sessions':
        return renderSessions();
      default:
        return renderOverview();
    }
  }

  function renderOverview() {
    return (
      <Box>
        {/* Métricas Principais */}
        <Box sx={{
          display: 'grid',
          gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr 1fr' },
          gap: 3,
          mb: 3
        }}>
          <MetricCard
            title="Clientes Ativos"
            value={data?.activeClients || 0}
            subtitle={data?.activeClientsChange || 'Sem dados'}
            icon={<ComputerIcon />}
            color={evolutionColors.primary.blue}
          />

          <MetricCard
            title="Sessões Hoje"
            value={data?.dailySessions || 0}
            subtitle={data?.dailySessionsChange || 'Sem dados'}
            icon={<VpnIcon />}
            color={evolutionColors.primary.green}
          />

          <MetricCard
            title="Uptime Sistema"
            value={`${data?.systemUptime?.percentage || 0}%`}
            subtitle={data?.systemUptime?.consecutiveDays ? `${data.systemUptime.consecutiveDays} dias` : 'Sem dados'}
            icon={<SecurityIcon />}
            color={evolutionColors.status.success}
          />

          <MetricCard
            title="Alertas Ativos"
            value={data?.activeAlerts?.total || 0}
            subtitle={data?.activeAlerts?.total ? `${data.activeAlerts.critical} críticos` : 'Nenhum alerta'}
            icon={<SpeedIcon />}
            color={data?.activeAlerts?.total ? evolutionColors.status.warning : evolutionColors.status.success}
          />
        </Box>

        {/* Gráficos */}
        <Box sx={{
          display: 'grid',
          gridTemplateColumns: { xs: '1fr', lg: '2fr 1fr' },
          gap: 3
        }}>
          {/* Gráfico de Atividade */}
          <Card sx={{ height: 400 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Atividade da Rede VPN
              </Typography>
              <Box sx={{ height: 320 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={getEmptyActivityData()}>
                    <CartesianGrid strokeDasharray="3 3" stroke={evolutionColors.neutral.gray} />
                    <XAxis dataKey="time" stroke={evolutionColors.neutral.darkGray} />
                    <YAxis stroke={evolutionColors.neutral.darkGray} />
                    <Tooltip />
                    <Line
                      type="monotone"
                      dataKey="value"
                      stroke={evolutionColors.primary.blue}
                      strokeWidth={3}
                      dot={{ fill: evolutionColors.primary.blue, strokeWidth: 2, r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>

          {/* Status do Sistema */}
          <Card sx={{ height: 400 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Status do Sistema
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
                <StatusItem
                  label="Servidor VPN"
                  status={data?.systemUptime?.percentage ? 'online' : 'offline'}
                />
                <StatusItem
                  label="Base de Dados"
                  status={data ? 'online' : 'offline'}
                />
                <StatusItem
                  label="Monitoramento"
                  status={data?.lastUpdated ? 'online' : 'offline'}
                />
                <StatusItem
                  label="Backup"
                  status="online"
                />
              </Box>

              {data?.lastUpdated && (
                <Typography variant="caption" sx={{ mt: 3, display: 'block', color: evolutionColors.neutral.darkGray }}>
                  Última atualização: {new Date(data.lastUpdated).toLocaleString('pt-BR')}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Box>
      </Box>
    );
  }

  function renderClients() {
    return (
      <Card>
        <CardContent>
          <Typography variant="h5" gutterBottom>
            Clientes VPN Conectados
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Lista de clientes atualmente conectados à VPN
          </Typography>
          
          {data?.activeClients === 0 ? (
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <ComputerIcon sx={{ fontSize: 64, color: evolutionColors.neutral.gray, mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                Nenhum cliente conectado
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Os clientes aparecerão aqui quando se conectarem à VPN
              </Typography>
            </Box>
          ) : (
            <Typography variant="body1">
              {data?.activeClients} clientes conectados
            </Typography>
          )}
        </CardContent>
      </Card>
    );
  }

  function renderSessions() {
    return (
      <Card>
        <CardContent>
          <Typography variant="h5" gutterBottom>
            Histórico de Sessões VPN
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Registro de todas as sessões VPN
          </Typography>
          
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <HistoryIcon sx={{ fontSize: 64, color: evolutionColors.neutral.gray, mb: 2 }} />
            <Typography variant="h6" color="text.secondary">
              Nenhuma sessão registrada
            </Typography>
            <Typography variant="body2" color="text.secondary">
              O histórico de sessões aparecerá aqui conforme os clientes se conectarem
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }
};

// Componente para cards de métricas
const MetricCard: React.FC<{
  title: string;
  value: string | number;
  subtitle: string;
  icon: React.ReactNode;
  color: string;
}> = ({ title, value, subtitle, icon, color }) => (
  <Card sx={{ height: '100%' }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Avatar sx={{ bgcolor: color, mr: 2 }}>
          {icon}
        </Avatar>
        <Typography variant="h6" component="div">
          {title}
        </Typography>
      </Box>
      <Typography variant="h4" component="div" sx={{ mb: 1, fontWeight: 600 }}>
        {value}
      </Typography>
      <Typography variant="body2" color="text.secondary">
        {subtitle}
      </Typography>
    </CardContent>
  </Card>
);

// Componente para status do sistema
const StatusItem: React.FC<{
  label: string;
  status: 'online' | 'offline';
}> = ({ label, status }) => (
  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
    <Typography variant="body1">{label}</Typography>
    <Chip 
      label={status === 'online' ? 'Online' : 'Offline'}
      color={status === 'online' ? 'success' : 'error'}
      size="small"
    />
  </Box>
);

// Dados vazios para o gráfico
const getEmptyActivityData = () => [
  { time: '00:00', value: 0 },
  { time: '04:00', value: 0 },
  { time: '08:00', value: 0 },
  { time: '12:00', value: 0 },
  { time: '16:00', value: 0 },
  { time: '20:00', value: 0 },
  { time: '24:00', value: 0 },
];
