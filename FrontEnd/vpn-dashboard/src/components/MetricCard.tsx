import { motion } from 'framer-motion';
import type { LucideIcon } from 'lucide-react';
import { Card, CardContent } from './ui/card';

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: LucideIcon;
  trend?: string;
  trendUp?: boolean;
  delay?: number;
}

export function MetricCard({ 
  title, 
  value, 
  subtitle, 
  icon: Icon, 
  trend, 
  trendUp, 
  delay = 0 
}: MetricCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
    >
      <Card className="bg-slate-800 border-slate-700 hover:bg-slate-750 transition-colors">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Icon className="h-4 w-4 text-slate-400" />
                <p className="text-sm font-medium text-slate-400">{title}</p>
              </div>
              
              <div className="space-y-1">
                <h3 className="text-2xl font-bold text-white">{value}</h3>
                {subtitle && (
                  <p className="text-xs text-slate-500">{subtitle}</p>
                )}
                {trend && (
                  <div className="flex items-center gap-1">
                    <span className={`text-xs font-medium ${
                      trendUp ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {trend}
                    </span>
                    <span className="text-xs text-slate-500">comparado a ontem</span>
                  </div>
                )}
              </div>
            </div>
            
            <div className="ml-4">
              <div className="w-12 h-12 bg-slate-700 rounded-lg flex items-center justify-center">
                <Icon className="h-6 w-6 text-blue-400" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
