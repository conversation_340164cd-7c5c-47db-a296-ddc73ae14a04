// Serviço de Autenticação para VPN Dashboard
// Credenciais padrão: admin / VPNnbr5410!

export interface User {
  id: string;
  username: string;
  role: string;
  lastLogin: Date;
}

export interface AuthResponse {
  success: boolean;
  user?: User;
  token?: string;
  message?: string;
}

class AuthService {
  private readonly STORAGE_KEY = 'vpn_auth_token';
  private readonly USER_KEY = 'vpn_user_data';
  
  // Credenciais padrão (em produção, isso viria do backend)
  private readonly DEFAULT_CREDENTIALS = {
    username: 'admin',
    password: 'VPNnbr5410!'
  };

  /**
   * Realiza login com credenciais
   */
  async login(username: string, password: string): Promise<AuthResponse> {
    try {
      // Simular delay de rede
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Verificar credenciais padrão
      if (username === this.DEFAULT_CREDENTIALS.username && 
          password === this.DEFAULT_CREDENTIALS.password) {
        
        const user: User = {
          id: '1',
          username: 'admin',
          role: 'administrator',
          lastLogin: new Date()
        };

        // Gerar token simples (em produção, viria do backend)
        const token = this.generateToken(user);

        // Armazenar no localStorage
        localStorage.setItem(this.STORAGE_KEY, token);
        localStorage.setItem(this.USER_KEY, JSON.stringify(user));

        return {
          success: true,
          user,
          token,
          message: 'Login realizado com sucesso'
        };
      }

      // TODO: Integrar com API do backend (Fase 4)
      // const response = await fetch('/api/auth/login', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ username, password })
      // });

      return {
        success: false,
        message: 'Credenciais inválidas'
      };

    } catch (error) {
      console.error('Erro no login:', error);
      return {
        success: false,
        message: 'Erro interno do servidor'
      };
    }
  }

  /**
   * Realiza logout
   */
  logout(): void {
    localStorage.removeItem(this.STORAGE_KEY);
    localStorage.removeItem(this.USER_KEY);
    
    // TODO: Invalidar token no backend
    // fetch('/api/auth/logout', { method: 'POST' });
  }

  /**
   * Verifica se usuário está autenticado
   */
  isAuthenticated(): boolean {
    const token = localStorage.getItem(this.STORAGE_KEY);
    const user = localStorage.getItem(this.USER_KEY);
    
    if (!token || !user) {
      return false;
    }

    try {
      // Verificar se token não expirou (simples verificação)
      const userData = JSON.parse(user);
      const lastLogin = new Date(userData.lastLogin);
      const now = new Date();
      const hoursDiff = (now.getTime() - lastLogin.getTime()) / (1000 * 60 * 60);
      
      // Token expira em 24 horas
      if (hoursDiff > 24) {
        this.logout();
        return false;
      }

      return true;
    } catch {
      this.logout();
      return false;
    }
  }

  /**
   * Retorna dados do usuário atual
   */
  getCurrentUser(): User | null {
    try {
      const userData = localStorage.getItem(this.USER_KEY);
      return userData ? JSON.parse(userData) : null;
    } catch {
      return null;
    }
  }

  /**
   * Retorna token de autenticação
   */
  getToken(): string | null {
    return localStorage.getItem(this.STORAGE_KEY);
  }

  /**
   * Gera token simples (em produção, viria do backend)
   */
  private generateToken(user: User): string {
    const payload = {
      userId: user.id,
      username: user.username,
      role: user.role,
      iat: Date.now()
    };
    
    // Token simples base64 (em produção seria JWT do backend)
    return btoa(JSON.stringify(payload));
  }

  /**
   * Verifica se usuário tem permissão específica
   */
  hasPermission(_permission: string): boolean {
    const user = this.getCurrentUser();
    if (!user) return false;

    // Admin tem todas as permissões
    if (user.role === 'administrator') return true;

    // Implementar lógica de permissões específicas
    return false;
  }

  /**
   * Atualiza último acesso
   */
  updateLastAccess(): void {
    const user = this.getCurrentUser();
    if (user) {
      user.lastLogin = new Date();
      localStorage.setItem(this.USER_KEY, JSON.stringify(user));
    }
  }
}

export const authService = new AuthService();
export default authService;
