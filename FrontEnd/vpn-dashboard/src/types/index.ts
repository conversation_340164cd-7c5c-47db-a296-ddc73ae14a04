// Tipos baseados na estrutura do PostgreSQL da Fase 2

export interface VPNClient {
  id: string;
  client_name: string;
  email: string;
  created_at: Date;
  is_active: boolean;
  last_connection?: Date;
  total_sessions: number;
  total_data_transfer: number;
}

export interface VPNSession {
  id: string;
  client_name: string;
  virtual_ip: string;
  real_ip: string;
  connected_at: Date;
  disconnected_at?: Date;
  duration_seconds: number;
  bytes_sent: number;
  bytes_received: number;
  is_active: boolean;
  status: 'connected' | 'disconnected' | 'timeout' | 'admin_disconnect';
  disconnect_reason?: string;
  client?: VPNClient;
}

export interface NetworkEvent {
  id: string;
  session_id: string;
  event_type: 'ssh' | 'vnc' | 'rdp' | 'http' | 'https' | 'other';
  source_ip: string;
  destination_ip: string;
  destination_port: number;
  detected_at: Date;
  details?: Record<string, any>;
  session?: VPNSession;
}

export interface SystemEvent {
  id: string;
  event_type: 'server_start' | 'server_stop' | 'client_connect' | 'client_disconnect' | 'error' | 'warning';
  severity: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  details?: Record<string, any>;
  occurred_at: Date;
  resolved_at?: Date;
}

export interface ServerReboot {
  id: string;
  reboot_detected_at: Date;
  downtime_seconds: number;
  reason?: string;
}

// Tipos para o Dashboard
export interface DashboardMetrics {
  activeClients: number;
  activeClientsChange: string;
  dailySessions: number;
  dailySessionsChange: string;
  networkEvents: NetworkEventsMetric;
  systemUptime: UptimeMetric;
  activeAlerts: AlertsMetric;
  activityData?: ActivityDataPoint[];
  clientDistributionData?: ClientDistributionData[];
  lastUpdated: Date;
}

export interface ActivityDataPoint {
  time: string;
  value: number;
}

export interface NetworkEventsMetric {
  total: number;
  ssh: number;
  vnc: number;
  rdp: number;
}

export interface UptimeMetric {
  percentage: number;
  consecutiveDays: number;
}

export interface AlertsMetric {
  total: number;
  critical: number;
  warning: number;
}

export interface RealtimeDataPoint {
  time: string;
  connections: number;
}

export interface ClientDistributionData {
  name: string;
  value: number;
  color: string;
}

// Tipos para API
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Tipos para filtros
export interface SessionFilters {
  client_name?: string;
  start_date?: string;
  end_date?: string;
  is_active?: boolean;
  limit?: number;
  offset?: number;
}

export interface EventFilters {
  event_type?: string;
  severity?: string;
  start_date?: string;
  end_date?: string;
  limit?: number;
  offset?: number;
}

// Tipos para WebSocket
export interface WebSocketMessage {
  type: 'session_update' | 'network_event' | 'system_event' | 'metrics_update';
  data: any;
  timestamp: Date;
}

// Tipos para configuração
export interface AppConfig {
  apiUrl: string;
  wsUrl: string;
  refreshInterval: number;
  theme: 'light' | 'dark';
  language: string;
}
