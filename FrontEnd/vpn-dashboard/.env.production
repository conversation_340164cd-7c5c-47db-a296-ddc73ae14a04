# Configurações de Produção - vpn.evo-eden.site

# API Backend (Fase 4)
VITE_API_URL=https://vpn.evo-eden.site/api
VITE_WS_URL=wss://vpn.evo-eden.site/ws

# Configurações da Aplicação
VITE_APP_NAME=VPN Monitor
VITE_APP_VERSION=3.0.0
VITE_APP_DOMAIN=vpn.evo-eden.site

# Configurações de Autenticação
VITE_AUTH_TOKEN_KEY=vpn_auth_token
VITE_AUTH_REFRESH_INTERVAL=3600000

# Configurações de WebSocket
VITE_WS_RECONNECT_INTERVAL=5000
VITE_WS_MAX_RECONNECT_ATTEMPTS=10

# Configurações de Logs
VITE_LOG_LEVEL=warn
VITE_ENABLE_ANALYTICS=false

# Configurações de UI
VITE_THEME=dark
VITE_DEFAULT_LANGUAGE=pt-BR
VITE_TIMEZONE=America/Sao_Paulo

# Configurações de Segurança
VITE_ENABLE_DEVTOOLS=false
VITE_SECURE_COOKIES=true
