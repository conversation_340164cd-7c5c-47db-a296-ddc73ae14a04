package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"vpn-backend/internal/auth"
	"vpn-backend/internal/config"
	"vpn-backend/internal/database"
	"vpn-backend/internal/handlers"
	"vpn-backend/internal/middleware"
	"vpn-backend/internal/openvpn"
	"vpn-backend/internal/websocket"

	"github.com/gin-gonic/gin"
)

func main() {
	log.Println("🚀 Iniciando VPN Backend Server...")

	// Carregar configurações
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("❌ Erro ao carregar configurações: %v", err)
	}

	// Configurar modo do Gin
	gin.SetMode(cfg.Server.Mode)

	// Conectar ao banco de dados
	db, err := database.NewDatabase(&cfg.Database)
	if err != nil {
		log.Fatalf("❌ Erro ao conectar com banco de dados: %v", err)
	}

	// Executar migrações
	if err := db.AutoMigrate(); err != nil {
		log.Fatalf("❌ Erro ao executar migrações: %v", err)
	}

	// Inicializar serviços
	jwtService := auth.NewJWTService(&cfg.JWT)
	passwordService := auth.NewPasswordService()
	vpnService := openvpn.NewOpenVPNService(db.GetDB(), cfg)

	// Inicializar WebSocket
	wsHub := websocket.NewHub(&cfg.WebSocket)
	notifier := websocket.NewNotifier(wsHub)

	// Inicializar handlers
	authHandler := handlers.NewAuthHandler(db.GetDB(), jwtService, passwordService)
	vpnHandler := handlers.NewVPNHandler(db.GetDB(), vpnService)
	eventsHandler := handlers.NewEventsHandler(db.GetDB())
	metricsHandler := handlers.NewMetricsHandler(db.GetDB(), vpnService)
	integrationHandler := handlers.NewIntegrationHandler(db.GetDB(), vpnService, notifier)

	// Configurar rotas
	router := setupRoutes(cfg, authHandler, vpnHandler, eventsHandler, metricsHandler,
		integrationHandler, jwtService, wsHub)

	// Iniciar serviços em background
	go wsHub.Run()
	go vpnService.Start()
	go notifier.StartPeriodicNotifications(vpnService)

	// Configurar servidor HTTP
	server := &http.Server{
		Addr:    cfg.Server.Host + ":" + cfg.Server.Port,
		Handler: router,
	}

	// Iniciar servidor em goroutine
	go func() {
		log.Printf("🌐 Servidor iniciado em %s:%s", cfg.Server.Host, cfg.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("❌ Erro ao iniciar servidor: %v", err)
		}
	}()

	// Aguardar sinal de interrupção
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("🛑 Parando servidor...")

	// Parar serviços
	vpnService.Stop()

	// Graceful shutdown do servidor HTTP
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Printf("❌ Erro ao parar servidor: %v", err)
	}

	// Fechar conexão com banco
	if err := db.Close(); err != nil {
		log.Printf("❌ Erro ao fechar banco de dados: %v", err)
	}

	log.Println("✅ Servidor parado com sucesso")
}

// setupRoutes configura as rotas da API
func setupRoutes(
	cfg *config.Config,
	authHandler *handlers.AuthHandler,
	vpnHandler *handlers.VPNHandler,
	eventsHandler *handlers.EventsHandler,
	metricsHandler *handlers.MetricsHandler,
	integrationHandler *handlers.IntegrationHandler,
	jwtService *auth.JWTService,
	wsHub *websocket.Hub,
) *gin.Engine {
	router := gin.New()

	// Middlewares globais
	router.Use(middleware.LoggingMiddleware())
	router.Use(middleware.SecurityMiddleware())
	router.Use(middleware.CORSMiddleware())
	router.Use(middleware.RequestIDMiddleware())
	router.Use(middleware.HealthCheckMiddleware())
	router.Use(gin.Recovery())

	// Middleware de filtro de IP VPN
	router.Use(middleware.VPNIPFilterMiddleware(&cfg.Security))

	// Rate limiting
	router.Use(middleware.RateLimitMiddleware(cfg.Security.RateLimit))

	// Rotas públicas
	public := router.Group("/api/v1")
	{
		// Autenticação
		auth := public.Group("/auth")
		{
			auth.POST("/login", authHandler.Login)
			auth.POST("/register", authHandler.Register)
		}

		// Health check
		public.GET("/health", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"status":    "healthy",
				"timestamp": time.Now().UTC(),
				"service":   "vpn-backend",
				"version":   "1.0.0",
			})
		})
	}

	// Rotas internas (para OpenVPN e scripts de setup)
	internal := router.Group("/api/v1/internal")
	internal.Use(middleware.InternalAuthMiddleware())
	{
		// Eventos OpenVPN
		events := internal.Group("/events")
		{
			events.POST("/connect", integrationHandler.HandleClientConnect)
			events.POST("/disconnect", integrationHandler.HandleClientDisconnect)
		}

		// Dispositivos registrados
		device := internal.Group("/device")
		{
			device.POST("/register", integrationHandler.RegisterDevice)
			device.POST("/heartbeat", integrationHandler.DeviceHeartbeat)
		}
	}

	// Rotas protegidas
	protected := router.Group("/api/v1")
	protected.Use(middleware.AuthMiddleware(jwtService))
	{
		// Autenticação (rotas protegidas)
		auth := protected.Group("/auth")
		{
			auth.POST("/refresh", authHandler.RefreshToken)
			auth.POST("/change-password", authHandler.ChangePassword)
			auth.GET("/profile", authHandler.GetProfile)
		}

		// VPN
		vpn := protected.Group("/vpn")
		{
			vpn.GET("/sessions/active", vpnHandler.GetActiveSessions)
			vpn.GET("/sessions/history", vpnHandler.GetSessionHistory)
			vpn.GET("/clients", vpnHandler.GetClients)
			vpn.GET("/clients/:name", vpnHandler.GetClientDetails)
			vpn.GET("/status/realtime", vpnHandler.GetRealtimeStatus)
			vpn.POST("/clients/:name/disconnect", vpnHandler.DisconnectClient)

			// Novas rotas de integração Fase 5
			vpn.GET("/realtime-status", integrationHandler.GetRealTimeStatus)
			vpn.GET("/clients/:name/details", integrationHandler.GetClientDetails)
		}

		// Eventos
		events := protected.Group("/events")
		{
			events.GET("/system", eventsHandler.GetSystemEvents)
			events.GET("/network", eventsHandler.GetNetworkEvents)
			events.GET("/stats", eventsHandler.GetEventStats)
			events.GET("/alerts", eventsHandler.GetRecentAlerts)
			events.POST("/system/:id/read", eventsHandler.MarkEventAsRead)
		}

		// Métricas
		metrics := protected.Group("/metrics")
		{
			metrics.GET("/connections", metricsHandler.GetConnectionMetrics)
			metrics.GET("/performance", metricsHandler.GetPerformanceMetrics)
			metrics.GET("/dashboard", metricsHandler.GetDashboardMetrics)
			metrics.GET("/health", metricsHandler.GetSystemHealth)
		}

		// WebSocket
		protected.GET("/ws", middleware.OptionalAuthMiddleware(jwtService), 
			func(c *gin.Context) {
				wsHub.HandleWebSocket(c)
			})
	}

	// Rotas administrativas
	admin := router.Group("/api/v1/admin")
	admin.Use(middleware.AuthMiddleware(jwtService))
	admin.Use(middleware.AdminMiddleware())
	{
		// Administração do sistema
		admin.GET("/stats", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"message": "Admin stats endpoint",
			})
		})
	}

	return router
}
