# Dockerfile para VPN Backend Go
# Multi-stage build para otimizar tamanho da imagem

# Stage 1: Build
FROM golang:1.23-alpine AS builder

# Instalar dependências necessárias
RUN apk add --no-cache git ca-certificates tzdata

# Definir diretório de trabalho
WORKDIR /app

# Copiar arquivos de dependências
COPY go.mod go.sum ./

# Download das dependências
RUN go mod download

# Copiar código fonte
COPY . .

# Build da aplicação
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o vpn-backend \
    cmd/server/main.go

# Stage 2: Runtime
FROM alpine:latest

# Instalar dependências runtime
RUN apk --no-cache add ca-certificates curl tzdata

# Criar usuário não-root
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Definir diretório de trabalho
WORKDIR /app

# Copiar binário do stage de build
COPY --from=builder /app/vpn-backend .

# Copiar arquivos de configuração
COPY --from=builder /app/configs ./configs

# Criar diretórios necessários
RUN mkdir -p logs && \
    chown -R appuser:appgroup /app

# Definir timezone
ENV TZ=America/Sao_Paulo

# Configurar usuário
USER appuser

# Expor porta
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/api/v1/health || exit 1

# Comando para iniciar aplicação
CMD ["./vpn-backend"]
