# 📚 Documentação Completa - Backend VPN Go

## 🎯 **VISÃO GERAL**

Este documento detalha a implementação completa do backend VPN desenvolvido em Go, baseado na Fase 4 do projeto. O backend foi projetado para monitoramento em tempo real de servidores OpenVPN com alta performance e segurança.

---

## 🏗️ **ARQUITETURA IMPLEMENTADA**

### **Stack Tecnológico**
- **Linguagem**: Go 1.23+
- **Framework Web**: Gin (alta performance)
- **Banco de Dados**: PostgreSQL com GORM
- **Autenticação**: JWT com refresh tokens
- **WebSocket**: Gorilla WebSocket
- **Containerização**: Docker + Docker Swarm
- **Proxy Reverso**: Traefik com SSL automático

### **Estrutura do Projeto**
```
/root/VPN/BackEnd/
├── cmd/server/main.go              # Aplicação principal
├── internal/
│   ├── auth/                       # Sistema de autenticação
│   │   ├── jwt.go                  # Serviço JWT
│   │   └── password.go             # Hash de senhas bcrypt
│   ├── config/config.go            # Configurações centralizadas
│   ├── database/database.go        # Conexão PostgreSQL
│   ├── handlers/                   # Handlers da API REST
│   │   ├── auth_handler.go         # Autenticação
│   │   ├── vpn_handler.go          # VPN endpoints
│   │   ├── events_handler.go       # Eventos sistema/rede
│   │   └── metrics_handler.go      # Métricas e dashboard
│   ├── middleware/                 # Middlewares de segurança
│   │   ├── auth.go                 # Middleware JWT
│   │   └── security.go             # Filtro IP VPN, CORS, Rate Limit
│   ├── models/models.go            # Modelos do banco de dados
│   ├── openvpn/                    # Sistema OpenVPN otimizado
│   │   ├── enhanced_log_parser.go  # Parser multi-arquivo
│   │   └── service.go              # Serviço com workers
│   └── websocket/                  # Sistema WebSocket
│       ├── hub.go                  # Hub WebSocket
│       └── notifier.go             # Notificações automáticas
├── configs/.env                    # Configurações ambiente
├── docker-compose.yml              # Configuração Docker Swarm
├── Dockerfile                      # Multi-stage build
├── deploy.sh                       # Script de deploy automatizado
└── go.mod                          # Dependências Go
```

---

## 🔧 **PROCESSO DE IMPLEMENTAÇÃO**

### **1. Configuração Inicial**

#### **1.1 Dependências Go**
```bash
# Dependências principais instaladas
go get github.com/gin-gonic/gin@latest
go get gorm.io/gorm@latest
go get gorm.io/driver/postgres@latest
go get github.com/golang-jwt/jwt/v5@latest
go get github.com/google/uuid@latest
go get github.com/joho/godotenv@latest
go get github.com/gorilla/websocket@latest
go get github.com/fsnotify/fsnotify@latest
go get golang.org/x/time@latest
```

#### **1.2 Configuração de Ambiente**
```bash
# Arquivo configs/.env criado com configurações seguras
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
GIN_MODE=release

# Banco PostgreSQL
DB_HOST=postgres
DB_PORT=5432
DB_USER=vpn_user
DB_PASSWORD=vpn_secure_password_2024
DB_NAME=vpn_monitoring

# JWT com chave segura
JWT_SECRET=vpn_jwt_super_secret_key_2024_secure_token_production
JWT_EXPIRY=24h

# Segurança VPN
VPN_NETWORK=********/24
ALLOWED_IPS=********/24,127.0.0.1/32,**********/12
RATE_LIMIT=1000
```

### **2. Sistema de Autenticação**

#### **2.1 JWT Service Implementado**
```go
// Funcionalidades implementadas:
- GenerateToken(userID uint, username, role string) (string, error)
- ValidateToken(tokenString string) (*Claims, error)
- RefreshToken(tokenString string) (string, error)
- ExtractClaims(tokenString string) (*Claims, error)

// Segurança:
- Tokens com expiração de 24h
- Refresh tokens automáticos
- Claims customizados com role-based access
- Chave secreta configurável
```

#### **2.2 Password Service**
```go
// Funcionalidades implementadas:
- HashPassword(password string) (string, error)
- CheckPassword(password, hash string) bool
- GenerateRandomPassword(length int) string

// Segurança:
- bcrypt com cost 12 (alta segurança)
- Validação de força de senha
- Geração de senhas aleatórias seguras
```

### **3. Sistema de Banco de Dados**

#### **3.1 Modelos Implementados**
```go
// Modelos principais criados:
type User struct {
    ID        uint      `gorm:"primaryKey"`
    Username  string    `gorm:"uniqueIndex;not null"`
    Email     string    `gorm:"uniqueIndex"`
    Password  string    `gorm:"not null"`
    Role      string    `gorm:"default:'user'"`
    IsActive  bool      `gorm:"default:true"`
    CreatedAt time.Time
    UpdatedAt time.Time
}

type VPNClient struct {
    ID           uint      `gorm:"primaryKey"`
    Name         string    `gorm:"uniqueIndex;not null"`
    Email        string
    IsActive     bool      `gorm:"default:true"`
    CreatedAt    time.Time
    LastSeen     *time.Time
    TotalBytesIn  int64    `gorm:"default:0"`
    TotalBytesOut int64    `gorm:"default:0"`
}

type VPNSession struct {
    ID            uint      `gorm:"primaryKey"`
    ClientName    string    `gorm:"index"`
    StartTime     time.Time
    EndTime       *time.Time
    Duration      int       // segundos
    BytesIn       int64
    BytesOut      int64
    ExternalIP    string
    VPNAddress    string
    DisconnectReason string
}

// + SystemEvent, NetworkEvent, ConnectionMetrics, PerformanceMetrics
```

#### **3.2 Migrações Automáticas**
```go
// Implementação de migrações sequenciais para evitar dependências circulares:
func (d *Database) AutoMigrate() error {
    // Migrar tabelas independentes primeiro
    err := d.DB.AutoMigrate(&models.User{})
    err = d.DB.AutoMigrate(&models.VPNClient{})
    
    // Migrar tabelas dependentes
    err = d.DB.AutoMigrate(&models.VPNSession{})
    err = d.DB.AutoMigrate(&models.SystemEvent{})
    err = d.DB.AutoMigrate(&models.NetworkEvent{})
    err = d.DB.AutoMigrate(&models.ConnectionMetrics{})
    err = d.DB.AutoMigrate(&models.PerformanceMetrics{})
    
    return d.createDefaultAdmin()
}
```

### **4. Sistema OpenVPN Otimizado**

#### **4.1 Parser Multi-Arquivo**
```go
// Funcionalidades implementadas:
- Monitoramento simultâneo: OpenVPN, syslog, auth.log, kern.log
- Leitura incremental com fsnotify
- Deduplicação MD5 (timestamp+cliente+ação+fonte)
- Processamento em lote (100 eventos/5s timeout)
- Intervalos adaptativos (10s-2min baseado na carga)

// 12 Patterns regex implementados:
- Conexões OpenVPN
- Desconexões OpenVPN
- Falhas de autenticação
- SSH login/failed
- Comandos sudo
- Reboots do sistema
- Reinicializações de serviços
```

#### **4.2 Workers Dedicados**
```go
// Workers implementados:
- LogWorker: Processa eventos de log
- NetworkWorker: Processa eventos de rede
- SystemWorker: Processa eventos do sistema
- MetricsWorker: Coleta métricas de performance

// Características:
- Pool de workers configurável
- Processamento paralelo
- Controle de recursos automático
- Recuperação de falhas
```

### **5. API REST Completa**

#### **5.1 Endpoints Implementados**
```go
// Autenticação
POST   /api/v1/auth/login
POST   /api/v1/auth/register
POST   /api/v1/auth/refresh
POST   /api/v1/auth/change-password
GET    /api/v1/auth/profile

// VPN
GET    /api/v1/vpn/sessions/active
GET    /api/v1/vpn/sessions/history
GET    /api/v1/vpn/clients
GET    /api/v1/vpn/clients/:name
GET    /api/v1/vpn/status/realtime
POST   /api/v1/vpn/clients/:name/disconnect

// Eventos
GET    /api/v1/events/system
GET    /api/v1/events/network
GET    /api/v1/events/stats
GET    /api/v1/events/alerts
POST   /api/v1/events/system/:id/read

// Métricas
GET    /api/v1/metrics/connections
GET    /api/v1/metrics/performance
GET    /api/v1/metrics/dashboard
GET    /api/v1/metrics/health

// WebSocket
GET    /api/v1/ws
```

### **6. Sistema WebSocket**

#### **6.1 Hub Multi-Conexão**
```go
// Funcionalidades implementadas:
- Suporte a múltiplas conexões simultâneas
- Broadcast por role (admin recebe mais notificações)
- Ping/Pong para manter conexões vivas
- Cleanup automático de conexões mortas
- Rate limiting por conexão
```

#### **6.2 Notificações Automáticas**
```go
// Tipos de notificação implementados:
- Conexões/desconexões VPN
- Alertas de segurança
- Métricas em tempo real
- Status do servidor
- Eventos críticos do sistema
```

---

## ⚠️ **ALERTAS CRÍTICOS E PREVENÇÃO DE FALHAS**

### **🚨 ALERTAS DE SEGURANÇA**

#### **1. Configuração JWT**
```bash
# ❌ NUNCA usar chaves fracas
JWT_SECRET=123456  # PERIGOSO!

# ✅ SEMPRE usar chaves fortes
JWT_SECRET=vpn_jwt_super_secret_key_2024_secure_token_production_$(openssl rand -hex 32)

# ⚠️ ALERTA: Alterar JWT_SECRET invalida todos os tokens existentes
```

#### **2. Filtro de IP VPN**
```go
// ⚠️ CRÍTICO: Configurar corretamente a rede VPN
VPN_NETWORK=********/24  // Deve corresponder à sua rede OpenVPN
ALLOWED_IPS=********/24,127.0.0.1/32,**********/12

// ❌ NUNCA permitir 0.0.0.0/0 em produção
// ✅ SEMPRE restringir às redes necessárias
```

#### **3. Credenciais do Banco**
```bash
# ❌ NUNCA usar senhas padrão
DB_PASSWORD=password  # PERIGOSO!

# ✅ SEMPRE usar senhas fortes
DB_PASSWORD=vpn_secure_password_2024_$(openssl rand -base64 32)

# ⚠️ ALERTA: Backup das credenciais em local seguro
```

### **🔧 PREVENÇÃO DE FALHAS TÉCNICAS**

#### **1. Problemas de Migração**
```go
// ❌ PROBLEMA: Referências circulares em modelos
type VPNClient struct {
    Sessions []VPNSession `gorm:"foreignKey:ClientName"`  // Pode causar erro
}

type VPNSession struct {
    Client *VPNClient `gorm:"foreignKey:ClientName"`     // Referência circular
}

// ✅ SOLUÇÃO: Remover referências circulares durante migração
// Comentar relacionamentos complexos até migração completa
```

#### **2. Problemas de Permissão**
```bash
# ❌ PROBLEMA: Backend não consegue ler logs do sistema
# Erro: permission denied /var/log/syslog

# ✅ SOLUÇÃO: Ajustar permissões
sudo chmod 644 /var/log/syslog /var/log/auth.log /var/log/kern.log

# ⚠️ ALERTA: Executar backend como root ou ajustar permissões
```

#### **3. Problemas de Conectividade**
```bash
# ❌ PROBLEMA: Backend não conecta com PostgreSQL
# Erro: connection refused

# ✅ VERIFICAÇÕES:
# 1. PostgreSQL está rodando?
sudo systemctl status postgresql

# 2. Rede Docker está correta?
docker network inspect redeinterna

# 3. Credenciais estão corretas?
psql -h postgres -U vpn_user -d vpn_monitoring
```

#### **4. Problemas de Health Check**
```bash
# ❌ PROBLEMA: Health check retorna 403
# Causa: Filtro de IP bloqueando localhost

# ✅ SOLUÇÃO: Permitir health checks locais
if c.Request.URL.Path == "/api/v1/health" {
    if clientIP == "127.0.0.1" || clientIP == "::1" {
        c.Next()
        return
    }
}
```

### **📊 MONITORAMENTO E ALERTAS**

#### **1. Métricas Críticas**
```go
// Monitorar sempre:
- CPU Usage > 80%
- Memory Usage > 85%
- Disk Space < 10%
- Database Connections > 80% do pool
- WebSocket Connections > limite configurado
- Rate Limit violations
- JWT token failures
- Database query timeouts
```

#### **2. Logs de Erro**
```bash
# Monitorar logs para:
grep -i "error\|fatal\|panic" /root/VPN/BackEnd/logs/server.log
grep -i "connection refused\|timeout" /root/VPN/BackEnd/logs/server.log
grep -i "authentication failed" /root/VPN/BackEnd/logs/server.log
```

#### **3. Alertas Automáticos**
```go
// Implementar alertas para:
- Falhas de conexão com banco > 5 em 1min
- Erros de parsing de logs > 10 em 5min
- WebSocket disconnections > 50% dos clientes
- Disk space < 1GB
- Memory usage > 90% por > 5min
```

---

## 🚀 **COMANDOS DE OPERAÇÃO**

### **Deploy e Atualização**
```bash
# Deploy inicial
cd /root/VPN/BackEnd
./deploy.sh

# Atualização do código
docker service update --image vpn-backend:latest vpn-backend_vpn-backend

# Rollback em caso de problema
docker service rollback vpn-backend_vpn-backend
```

### **Monitoramento**
```bash
# Status dos serviços
docker service ls | grep vpn

# Logs em tempo real
docker service logs -f vpn-backend_vpn-backend

# Métricas de recursos
docker stats $(docker ps | grep vpn-backend | awk '{print $1}')

# Health check
curl http://localhost:8080/api/v1/health
```

### **Troubleshooting**
```bash
# Verificar conectividade interna
docker exec <container> curl http://localhost:8080/api/v1/health

# Testar banco de dados
docker exec <container> nc -z postgres 5432

# Verificar logs de erro
docker service logs vpn-backend_vpn-backend | grep -i error

# Reiniciar serviço
docker service update --force vpn-backend_vpn-backend
```

---

## 🔧 **CONFIGURAÇÕES AVANÇADAS**

### **1. Otimização de Performance**

#### **1.1 Pool de Conexões PostgreSQL**
```go
// Configurações implementadas em database.go
config := &gorm.Config{
    Logger: logger.Default.LogMode(logger.Silent),
}

sqlDB, err := db.DB()
if err == nil {
    sqlDB.SetMaxIdleConns(5)     // Conexões idle
    sqlDB.SetMaxOpenConns(20)    // Máximo de conexões
    sqlDB.SetConnMaxLifetime(time.Hour) // Tempo de vida
}

// ⚠️ ALERTA: Ajustar conforme carga do sistema
// Monitorar: SHOW STAT_ACTIVITY; no PostgreSQL
```

#### **1.2 Cache e TTL**
```go
// Cache implementado com TTL de 5 minutos
type CacheEntry struct {
    Data      interface{}
    ExpiresAt time.Time
}

// ⚠️ ALERTA: Limpar cache periodicamente
// Monitorar uso de memória do cache
```

#### **1.3 Workers e Processamento**
```go
// Configurações de workers
WORKER_POOL_SIZE=10          // Número de workers
BATCH_SIZE=100              // Eventos por lote
COLLECTION_INTERVAL=30s     // Intervalo de coleta
MAX_CACHE_SIZE=1000        // Tamanho máximo do cache

// ⚠️ ALERTA: Ajustar conforme recursos disponíveis
// CPU cores * 2 = bom ponto de partida para workers
```

### **2. Configurações de Segurança Avançadas**

#### **2.1 Rate Limiting Detalhado**
```go
// Implementação por IP e por usuário
type RateLimiter struct {
    requests map[string][]time.Time
    mu       sync.RWMutex
    limit    int
    window   time.Duration
}

// Configurações por endpoint:
// /api/v1/auth/login: 5 req/min
// /api/v1/auth/*: 10 req/min
// /api/v1/*: 1000 req/min (rede VPN)

// ⚠️ ALERTA: Monitorar tentativas de brute force
```

#### **2.2 Headers de Segurança**
```go
// Headers implementados automaticamente:
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000; includeSubDomains
Content-Security-Policy: default-src 'self'

// ⚠️ ALERTA: Verificar compatibilidade com frontend
```

#### **2.3 Validação de Input**
```go
// Validações implementadas:
- Sanitização de SQL injection
- Validação de JWT tokens
- Verificação de tipos de dados
- Limitação de tamanho de payload
- Validação de caracteres especiais

// ⚠️ ALERTA: Sempre validar dados de entrada
```

### **3. Backup e Recuperação**

#### **3.1 Backup Automático do Banco**
```bash
# Script de backup (implementar como cron job)
#!/bin/bash
BACKUP_DIR="/root/VPN/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Backup do banco
docker exec postgres pg_dump -U vpn_user vpn_monitoring > \
    "$BACKUP_DIR/vpn_monitoring_$DATE.sql"

# Manter apenas últimos 7 dias
find "$BACKUP_DIR" -name "*.sql" -mtime +7 -delete

# ⚠️ ALERTA: Testar restauração regularmente
```

#### **3.2 Backup de Configurações**
```bash
# Backup de arquivos críticos
tar -czf "/root/VPN/backups/config_$DATE.tar.gz" \
    /root/VPN/BackEnd/configs/ \
    /root/VPN/BackEnd/docker-compose.yml \
    /root/VPN/BackEnd/Dockerfile

# ⚠️ ALERTA: Não incluir senhas em backups não criptografados
```

#### **3.3 Procedimento de Recuperação**
```bash
# 1. Parar serviços
docker service rm vpn-backend_vpn-backend

# 2. Restaurar banco
docker exec -i postgres psql -U vpn_user -d vpn_monitoring < backup.sql

# 3. Restaurar configurações
tar -xzf config_backup.tar.gz -C /

# 4. Reiniciar serviços
./deploy.sh

# ⚠️ ALERTA: Testar em ambiente de desenvolvimento primeiro
```

### **4. Monitoramento Avançado**

#### **4.1 Métricas Customizadas**
```go
// Métricas implementadas:
type Metrics struct {
    RequestsPerSecond    float64
    ErrorRate           float64
    ResponseTime        time.Duration
    ActiveConnections   int
    DatabaseConnections int
    MemoryUsage        float64
    CPUUsage           float64
    DiskUsage          float64
}

// ⚠️ ALERTA: Configurar alertas para valores críticos
```

#### **4.2 Health Checks Avançados**
```go
// Health checks implementados:
func (h *HealthHandler) DetailedHealthCheck() {
    checks := map[string]bool{
        "database":    checkDatabase(),
        "filesystem":  checkFilesystem(),
        "memory":      checkMemory(),
        "openvpn":     checkOpenVPNLogs(),
        "websocket":   checkWebSocketHub(),
    }

    // Retorna status detalhado de cada componente
}

// ⚠️ ALERTA: Implementar alertas para falhas de componentes
```

#### **4.3 Logs Estruturados**
```go
// Formato de log implementado:
{
    "timestamp": "2024-08-14T10:30:00Z",
    "level": "INFO",
    "component": "vpn-parser",
    "message": "Client connected",
    "client_name": "client1",
    "ip": "********",
    "duration": "150ms"
}

// ⚠️ ALERTA: Configurar rotação de logs
```

### **5. Procedimentos de Manutenção**

#### **5.1 Limpeza Automática**
```go
// Limpeza implementada automaticamente:
- Eventos sistema > 30 dias
- Eventos rede > 15 dias
- Métricas > 7 dias
- Logs aplicação > 30 dias
- Cache expirado > TTL

// ⚠️ ALERTA: Verificar espaço em disco regularmente
```

#### **5.2 Atualização de Dependências**
```bash
# Verificar atualizações
go list -u -m all

# Atualizar dependências
go get -u ./...
go mod tidy

# Testar após atualizações
go test ./...

# ⚠️ ALERTA: Testar em ambiente de desenvolvimento primeiro
```

#### **5.3 Rotação de Logs**
```bash
# Configurar logrotate
cat > /etc/logrotate.d/vpn-backend << EOF
/root/VPN/BackEnd/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    copytruncate
}
EOF

# ⚠️ ALERTA: Verificar se logrotate está funcionando
```

---

## 🚨 **PROCEDIMENTOS DE EMERGÊNCIA**

### **1. Falha Total do Sistema**
```bash
# 1. Verificar status dos containers
docker ps -a

# 2. Verificar logs de erro
docker service logs vpn-backend_vpn-backend | tail -100

# 3. Verificar recursos do sistema
df -h
free -h
top

# 4. Reiniciar serviços em ordem
docker service update --force vpn-backend_vpn-backend

# 5. Se necessário, rollback
docker service rollback vpn-backend_vpn-backend
```

### **2. Falha de Banco de Dados**
```bash
# 1. Verificar status PostgreSQL
docker exec postgres pg_isready

# 2. Verificar logs do banco
docker logs postgres

# 3. Verificar espaço em disco
df -h /var/lib/docker

# 4. Restaurar de backup se necessário
docker exec -i postgres psql -U vpn_user -d vpn_monitoring < latest_backup.sql
```

### **3. Falha de Segurança**
```bash
# 1. Verificar logs de acesso suspeito
grep "403\|401\|429" /root/VPN/BackEnd/logs/server.log

# 2. Verificar tentativas de brute force
grep "authentication failed" /root/VPN/BackEnd/logs/server.log

# 3. Bloquear IPs suspeitos (se necessário)
iptables -A INPUT -s <IP_SUSPEITO> -j DROP

# 4. Revogar tokens comprometidos
# Alterar JWT_SECRET força logout de todos os usuários
```

### **4. Falha de Performance**
```bash
# 1. Verificar uso de recursos
docker stats

# 2. Verificar conexões de banco
docker exec postgres psql -U postgres -c "SELECT count(*) FROM pg_stat_activity;"

# 3. Limpar cache se necessário
# Reiniciar serviço limpa cache automaticamente

# 4. Escalar horizontalmente se necessário
docker service scale vpn-backend_vpn-backend=2
```

---

## 📋 **CHECKLIST DE MANUTENÇÃO SEMANAL**

### **Verificações Obrigatórias**
- [ ] ✅ Verificar logs de erro
- [ ] ✅ Verificar espaço em disco (< 80%)
- [ ] ✅ Verificar uso de memória (< 85%)
- [ ] ✅ Verificar conexões de banco (< 80% do pool)
- [ ] ✅ Verificar backup automático funcionando
- [ ] ✅ Verificar certificados SSL (> 30 dias para expirar)
- [ ] ✅ Verificar health checks passando
- [ ] ✅ Verificar WebSocket funcionando
- [ ] ✅ Verificar rate limiting funcionando
- [ ] ✅ Verificar limpeza automática funcionando

### **Verificações de Segurança**
- [ ] ✅ Verificar tentativas de acesso não autorizado
- [ ] ✅ Verificar logs de autenticação
- [ ] ✅ Verificar integridade dos arquivos de configuração
- [ ] ✅ Verificar atualizações de segurança disponíveis
- [ ] ✅ Verificar força das senhas
- [ ] ✅ Verificar permissões de arquivos

### **Verificações de Performance**
- [ ] ✅ Verificar tempo de resposta das APIs (< 200ms)
- [ ] ✅ Verificar throughput de eventos processados
- [ ] ✅ Verificar latência do WebSocket
- [ ] ✅ Verificar eficiência do cache
- [ ] ✅ Verificar queries lentas no banco
- [ ] ✅ Verificar fragmentação do banco

---

## 📞 **CONTATOS E SUPORTE**

### **Documentação Adicional**
- **README.md**: Instruções básicas de uso
- **implementacao_fase4.md**: Especificações técnicas
- **docker-compose.yml**: Configuração de containers
- **Logs**: `/root/VPN/BackEnd/logs/`

### **Comandos de Diagnóstico Rápido**
```bash
# Status geral
./test-system.sh

# Logs de erro
docker service logs vpn-backend_vpn-backend | grep -i error

# Health check
curl http://localhost:8080/api/v1/health

# Métricas de sistema
docker stats --no-stream
```

---

**🎯 Esta documentação completa garante operação segura, monitoramento eficaz e manutenção adequada do backend VPN implementado na Fase 4.**

**📚 Mantenha esta documentação atualizada conforme mudanças no sistema.**
