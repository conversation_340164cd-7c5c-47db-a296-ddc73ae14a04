# VPN Backend - Sistema de Monitoramento OpenVPN

Backend Go otimizado para monitoramento em tempo real de servidores OpenVPN, baseado na implementação da Fase 4 do projeto VPN corporativo.

## 🚀 Características

### ⚡ Sistema de Coleta Otimizado
- **Parser multi-arquivo**: Monitora OpenVPN, syslog, auth.log simultaneamente
- **Deduplicação inteligente**: Hash MD5 para evitar eventos duplicados
- **Processamento em lote**: Buffers de 100 eventos com timeout de 5s
- **Leitura incremental**: Apenas linhas novas desde última posição
- **Intervalos adaptativos**: 10s-2min baseado na carga do sistema

### 📊 Monitoramento Avançado
- **Eventos OpenVPN**: Conexões, desconexões, autenticação, falhas
- **Eventos de rede**: SSH login/failed, comandos sudo
- **Eventos do sistema**: Reboots, reinicializações de serviços
- **Métricas em tempo real**: Eventos/segundo, erros, latência
- **WebSocket**: Notificações em tempo real

### 🔒 Segurança VPN
- **Filtro de IP**: Acesso restrito apenas a IPs da rede VPN
- **Autenticação JWT**: Tokens seguros com expiração
- **Rate limiting**: Proteção contra ataques
- **Headers de segurança**: XSS, CSRF, HSTS

## 📁 Estrutura do Projeto

```
/root/VPN/BackEnd/
├── cmd/server/           # Aplicação principal
├── internal/
│   ├── auth/            # Autenticação JWT e senhas
│   ├── config/          # Configurações
│   ├── database/        # Conexão PostgreSQL
│   ├── handlers/        # Handlers da API REST
│   ├── middleware/      # Middlewares de segurança
│   ├── models/          # Modelos do banco de dados
│   ├── openvpn/         # Parser e serviço OpenVPN
│   └── websocket/       # Sistema WebSocket
├── configs/             # Arquivos de configuração
├── scripts/             # Scripts de inicialização
└── docs/               # Documentação
```

## 🛠️ Instalação e Configuração

### Pré-requisitos
- Go 1.23+
- PostgreSQL 12+
- OpenVPN configurado
- Acesso root (para logs do sistema)

### 1. Configuração do Banco de Dados
```bash
# Criar usuário e banco PostgreSQL
sudo -u postgres psql << EOF
CREATE USER vpn_user WITH PASSWORD 'vpn_secure_password_2024';
CREATE DATABASE vpn_monitoring OWNER vpn_user;
GRANT ALL PRIVILEGES ON DATABASE vpn_monitoring TO vpn_user;
EOF
```

### 2. Configuração do Ambiente
```bash
cd /root/VPN/BackEnd
cp configs/.env.example configs/.env
# Editar configs/.env com suas configurações
```

### 3. Instalação das Dependências
```bash
go mod download
```

### 4. Compilação
```bash
go build -o bin/vpn-backend cmd/server/main.go
```

## 🚀 Execução

### Iniciar o Servidor
```bash
# Usando script automatizado (recomendado)
sudo ./scripts/start.sh

# Ou manualmente
sudo ./bin/vpn-backend
```

### Parar o Servidor
```bash
# Usando script
sudo ./scripts/stop.sh

# Ou Ctrl+C se rodando em foreground
```

## 📡 API Endpoints

### Autenticação
- `POST /api/v1/auth/login` - Login
- `POST /api/v1/auth/register` - Registro
- `POST /api/v1/auth/refresh` - Renovar token
- `GET /api/v1/auth/profile` - Perfil do usuário

### VPN
- `GET /api/v1/vpn/sessions/active` - Sessões ativas
- `GET /api/v1/vpn/sessions/history` - Histórico de sessões
- `GET /api/v1/vpn/clients` - Lista de clientes
- `GET /api/v1/vpn/status/realtime` - Status em tempo real

### Eventos
- `GET /api/v1/events/system` - Eventos do sistema
- `GET /api/v1/events/network` - Eventos de rede
- `GET /api/v1/events/stats` - Estatísticas de eventos
- `GET /api/v1/events/alerts` - Alertas recentes

### Métricas
- `GET /api/v1/metrics/connections` - Métricas de conexão
- `GET /api/v1/metrics/performance` - Métricas de performance
- `GET /api/v1/metrics/dashboard` - Métricas do dashboard
- `GET /api/v1/metrics/health` - Status de saúde

### WebSocket
- `GET /api/v1/ws` - Conexão WebSocket para notificações

## ⚙️ Configuração

### Arquivo .env Principal
```env
# Servidor
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
GIN_MODE=release

# Banco de Dados
DB_HOST=localhost
DB_PORT=5432
DB_USER=vpn_user
DB_PASSWORD=vpn_secure_password_2024
DB_NAME=vpn_monitoring

# OpenVPN
OPENVPN_LOG_FILE=/root/VPN/OpenVPN/logs/openvpn.log
OPENVPN_STATUS_FILE=/root/VPN/OpenVPN/logs/status.log

# Performance
COLLECTION_INTERVAL=30s
BATCH_SIZE=100
MAX_CACHE_SIZE=1000

# Segurança
VPN_NETWORK=********/24
ALLOWED_IPS=********/24,127.0.0.1/32
RATE_LIMIT=1000
```

## 📊 Monitoramento

### Logs
- **Aplicação**: `logs/server.log`
- **OpenVPN**: `/root/VPN/OpenVPN/logs/openvpn.log`
- **Sistema**: `/var/log/syslog`
- **Autenticação**: `/var/log/auth.log`

### Health Check
```bash
curl http://localhost:8080/api/v1/health
```

### Métricas em Tempo Real
```bash
# Via WebSocket
wscat -c ws://localhost:8080/api/v1/ws

# Via API REST
curl -H "Authorization: Bearer <token>" \
     http://localhost:8080/api/v1/metrics/dashboard
```

## 🔧 Troubleshooting

### Problemas Comuns

1. **Erro de permissão nos logs**
   ```bash
   sudo chmod 644 /var/log/syslog /var/log/auth.log
   ```

2. **PostgreSQL não conecta**
   ```bash
   sudo systemctl start postgresql
   sudo systemctl enable postgresql
   ```

3. **OpenVPN não encontrado**
   ```bash
   sudo systemctl start openvpn@server
   sudo systemctl enable openvpn@server
   ```

4. **Porta 8080 em uso**
   ```bash
   # Alterar SERVER_PORT no .env
   # Ou verificar processo usando a porta
   sudo netstat -tlnp | grep :8080
   ```

### Logs de Debug
```bash
# Habilitar logs detalhados
export GIN_MODE=debug
export LOG_LEVEL=debug
```

## 🔒 Segurança

### Configurações Importantes
- Alterar `JWT_SECRET` para valor único
- Configurar `ALLOWED_IPS` para sua rede VPN
- Usar HTTPS em produção
- Configurar firewall para porta 8080

### Usuário Padrão
- **Username**: admin
- **Password**: password
- **Role**: admin

⚠️ **IMPORTANTE**: Alterar senha padrão após primeiro login!

## 📈 Performance

### Otimizações Implementadas
- Pool de conexões PostgreSQL otimizado
- Processamento em lote de eventos
- Cache em memória com TTL
- Leitura incremental de arquivos
- Deduplicação de eventos
- Workers dedicados por tipo de evento

### Monitoramento de Recursos
- CPU e memória monitorados automaticamente
- Intervalos de coleta adaptativos
- Limpeza automática de cache
- Métricas de performance em tempo real

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto é parte do sistema VPN corporativo e está sob licença proprietária.

---

**Desenvolvido com ❤️ para monitoramento VPN corporativo**
