#!/bin/bash

# Script de deploy do VPN Backend para Docker Swarm
# Integração com Traefik e rede redeinterna existente

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🚀 Deploy VPN Backend para Docker Swarm"

# Verificar se está no diretório correto
if [[ ! -f "Dockerfile" ]]; then
    log_error "Dockerfile não encontrado. Execute este script no diretório /root/VPN/BackEnd"
    exit 1
fi

# Verificar se Docker Swarm está ativo
if ! docker info --format '{{.Swarm.LocalNodeState}}' | grep -q "active"; then
    log_error "Docker Swarm não está ativo"
    exit 1
fi

# Verificar se rede redeinterna existe
if ! docker network ls | grep -q "redeinterna"; then
    log_error "Rede 'redeinterna' não encontrada"
    log_info "Redes disponíveis:"
    docker network ls
    exit 1
fi

log_success "Pré-requisitos verificados"

# Parar serviço existente se estiver rodando
if docker service ls | grep -q "vpn-backend"; then
    log_info "Parando serviço vpn-backend existente..."
    docker service rm vpn-backend || true
    sleep 10
fi

# Build da imagem Docker
log_info "Construindo imagem Docker..."
docker build -t vpn-backend:latest . --no-cache

if [[ $? -ne 0 ]]; then
    log_error "Falha no build da imagem Docker"
    exit 1
fi

log_success "Imagem Docker construída com sucesso"

# Verificar se PostgreSQL está disponível na rede
log_info "Verificando conectividade com PostgreSQL..."

# Criar container temporário para testar conectividade
docker run --rm --network redeinterna alpine:latest sh -c "
    apk add --no-cache postgresql-client > /dev/null 2>&1
    if pg_isready -h postgres -p 5432 -U vpn_user > /dev/null 2>&1; then
        echo 'PostgreSQL acessível'
        exit 0
    else
        echo 'PostgreSQL não acessível'
        exit 1
    fi
" || {
    log_warning "PostgreSQL pode não estar acessível na rede redeinterna"
    log_info "Continuando com o deploy..."
}

# Verificar se logs OpenVPN existem
log_info "Verificando logs OpenVPN..."
if [[ ! -f "/root/VPN/OpenVPN/logs/openvpn.log" ]]; then
    log_warning "Log OpenVPN não encontrado, criando..."
    mkdir -p /root/VPN/OpenVPN/logs
    touch /root/VPN/OpenVPN/logs/openvpn.log
    touch /root/VPN/OpenVPN/logs/status.log
fi

# Verificar permissões dos logs do sistema
log_info "Verificando permissões dos logs do sistema..."
chmod 644 /var/log/syslog 2>/dev/null || log_warning "Não foi possível ajustar permissões do syslog"
chmod 644 /var/log/auth.log 2>/dev/null || log_warning "Não foi possível ajustar permissões do auth.log"

# Deploy do stack
log_info "Fazendo deploy do stack vpn-backend..."
docker stack deploy -c docker-compose.yml vpn-backend

if [[ $? -ne 0 ]]; then
    log_error "Falha no deploy do stack"
    exit 1
fi

log_success "Stack vpn-backend deployado com sucesso"

# Aguardar serviço ficar disponível
log_info "Aguardando serviço ficar disponível..."
for i in {1..60}; do
    if docker service ls | grep vpn-backend | grep -q "1/1"; then
        log_success "Serviço vpn-backend está rodando"
        break
    fi
    
    if [[ $i -eq 60 ]]; then
        log_error "Timeout aguardando serviço ficar disponível"
        log_info "Status atual:"
        docker service ls | grep vpn-backend
        docker service ps vpn-backend
        exit 1
    fi
    
    sleep 5
    echo -n "."
done

echo ""

# Verificar health check
log_info "Verificando health check..."
sleep 30

# Obter IP do container para teste
CONTAINER_ID=$(docker ps | grep vpn-backend | awk '{print $1}' | head -1)
if [[ -n "$CONTAINER_ID" ]]; then
    log_info "Testando health check interno..."
    if docker exec $CONTAINER_ID curl -f http://localhost:8080/api/v1/health > /dev/null 2>&1; then
        log_success "Health check interno OK"
    else
        log_warning "Health check interno falhou"
    fi
fi

# Mostrar status final
echo ""
log_info "Status dos serviços VPN:"
echo "Frontend: $(docker service ls | grep vpn-frontend | awk '{print $4}' || echo 'Não encontrado')"
echo "Backend:  $(docker service ls | grep vpn-backend | awk '{print $4}' || echo 'Não encontrado')"

echo ""
log_info "URLs de acesso:"
echo "Frontend: https://vpn.evo-eden.site"
echo "Backend API: https://vpn.evo-eden.site/api/v1/health"
echo "Backend WebSocket: wss://vpn.evo-eden.site/ws"

echo ""
log_info "Comandos úteis:"
echo "Ver logs: docker service logs -f vpn-backend_vpn-backend"
echo "Ver status: docker service ps vpn-backend_vpn-backend"
echo "Escalar: docker service scale vpn-backend_vpn-backend=2"
echo "Remover: docker stack rm vpn-backend"

log_success "Deploy concluído com sucesso!"

# Teste final de conectividade externa
log_info "Testando conectividade externa..."
sleep 10

if curl -k -f https://vpn.evo-eden.site/api/v1/health > /dev/null 2>&1; then
    log_success "✅ Backend acessível externamente via HTTPS"
else
    log_warning "⚠️ Backend pode não estar acessível externamente ainda"
    log_info "Aguarde alguns minutos para o Traefik configurar as rotas"
fi

echo ""
log_success "🎉 Deploy do VPN Backend concluído!"
echo "🌐 Acesse: https://vpn.evo-eden.site"
