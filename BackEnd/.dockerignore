# Arquivos e diretórios a serem ignorados no build Docker

# Binários compilados
bin/
*.exe
*.exe~
*.dll
*.so
*.dylib

# Arquivos de teste
*.test

# Arquivos de output
*.out

# Logs
logs/
*.log

# Arquivos temporários
*.tmp
*.temp

# Arquivos do sistema
.DS_Store
Thumbs.db

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# Git
.git/
.gitignore

# Docker
Dockerfile
.dockerignore
docker-compose*.yml

# Scripts
scripts/
README.md
*.md

# Documentação
docs/

# Arquivos de desenvolvimento
.env.local
.env.development
