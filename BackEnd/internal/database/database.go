package database

import (
	"fmt"
	"log"
	"time"

	"vpn-backend/internal/config"
	"vpn-backend/internal/models"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Database representa a conexão com o banco de dados
type Database struct {
	DB     *gorm.DB
	Config *config.DatabaseConfig
}

// NewDatabase cria uma nova instância de conexão com o banco
func NewDatabase(cfg *config.DatabaseConfig) (*Database, error) {
	dsn := fmt.Sprintf(
		"host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		cfg.Host, cfg.Port, cfg.User, cfg.Password, cfg.Name, cfg.SSLMode,
	)

	// Configurar logger do GORM
	gormLogger := logger.Default.LogMode(logger.Silent)
	if cfg.Host == "localhost" {
		gormLogger = logger.Default.LogMode(logger.Info)
	}

	// Conectar ao banco
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger,
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	})
	if err != nil {
		return nil, fmt.Errorf("erro ao conectar com o banco de dados: %v", err)
	}

	// Configurar pool de conexões
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("erro ao obter instância SQL: %v", err)
	}

	// Pool de conexões otimizado
	sqlDB.SetMaxIdleConns(5)
	sqlDB.SetMaxOpenConns(20)
	sqlDB.SetConnMaxLifetime(time.Hour)
	sqlDB.SetConnMaxIdleTime(10 * time.Minute)

	// Habilitar prepared statements para performance
	db.PrepareStmt = true

	database := &Database{
		DB:     db,
		Config: cfg,
	}

	log.Printf("✅ Conectado ao banco PostgreSQL: %s:%s/%s", cfg.Host, cfg.Port, cfg.Name)
	return database, nil
}

// AutoMigrate executa as migrações automáticas
func (d *Database) AutoMigrate() error {
	log.Println("🔄 Executando migrações do banco de dados...")

	// Migrar tabelas independentes primeiro
	err := d.DB.AutoMigrate(&models.User{})
	if err != nil {
		return fmt.Errorf("erro ao migrar tabela users: %v", err)
	}

	err = d.DB.AutoMigrate(&models.VPNClient{})
	if err != nil {
		return fmt.Errorf("erro ao migrar tabela vpn_clients: %v", err)
	}

	// Migrar tabelas que dependem de outras
	err = d.DB.AutoMigrate(&models.VPNSession{})
	if err != nil {
		return fmt.Errorf("erro ao migrar tabela vpn_sessions: %v", err)
	}

	err = d.DB.AutoMigrate(&models.SystemEvent{})
	if err != nil {
		return fmt.Errorf("erro ao migrar tabela system_events: %v", err)
	}

	err = d.DB.AutoMigrate(&models.NetworkEvent{})
	if err != nil {
		return fmt.Errorf("erro ao migrar tabela network_events: %v", err)
	}

	err = d.DB.AutoMigrate(&models.ConnectionMetrics{})
	if err != nil {
		return fmt.Errorf("erro ao migrar tabela connection_metrics: %v", err)
	}

	err = d.DB.AutoMigrate(&models.PerformanceMetrics{})
	if err != nil {
		return fmt.Errorf("erro ao migrar tabela performance_metrics: %v", err)
	}

	// Migrar novas tabelas da Fase 5
	err = d.DB.AutoMigrate(&models.VPNConnection{})
	if err != nil {
		return fmt.Errorf("erro ao migrar tabela vpn_connections: %v", err)
	}

	err = d.DB.AutoMigrate(&models.RegisteredDevice{})
	if err != nil {
		return fmt.Errorf("erro ao migrar tabela registered_devices: %v", err)
	}

	err = d.DB.AutoMigrate(&models.DeviceHeartbeat{})
	if err != nil {
		return fmt.Errorf("erro ao migrar tabela device_heartbeats: %v", err)
	}

	// Criar índices customizados para performance
	if err := d.createCustomIndexes(); err != nil {
		log.Printf("⚠️ Erro ao criar índices customizados: %v", err)
	}

	// Criar usuário admin padrão se não existir
	if err := d.createDefaultAdmin(); err != nil {
		log.Printf("⚠️ Erro ao criar usuário admin padrão: %v", err)
	}

	log.Println("✅ Migrações executadas com sucesso")
	return nil
}

// createCustomIndexes cria índices customizados para performance
func (d *Database) createCustomIndexes() error {
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_vpn_sessions_active ON vpn_sessions(status, connected_at) WHERE status = 'active'",
		"CREATE INDEX IF NOT EXISTS idx_vpn_sessions_client_time ON vpn_sessions(client_name, connected_at DESC)",
		"CREATE INDEX IF NOT EXISTS idx_system_events_time_severity ON system_events(occurred_at DESC, severity)",
		"CREATE INDEX IF NOT EXISTS idx_network_events_time_type ON network_events(occurred_at DESC, event_type)",
		"CREATE INDEX IF NOT EXISTS idx_network_events_client_success ON network_events(client_name, success, occurred_at DESC)",
		"CREATE INDEX IF NOT EXISTS idx_connection_metrics_timestamp ON connection_metrics(timestamp DESC)",
		"CREATE INDEX IF NOT EXISTS idx_performance_metrics_timestamp ON performance_metrics(timestamp DESC)",

		// Índices para novas tabelas da Fase 5
		"CREATE INDEX IF NOT EXISTS idx_vpn_connections_active ON vpn_connections(status, connected_at) WHERE status = 'connected'",
		"CREATE INDEX IF NOT EXISTS idx_vpn_connections_client_time ON vpn_connections(client_name, connected_at DESC)",
		"CREATE INDEX IF NOT EXISTS idx_registered_devices_type_status ON registered_devices(device_type, status)",
		"CREATE INDEX IF NOT EXISTS idx_registered_devices_hostname ON registered_devices(hostname)",
		"CREATE INDEX IF NOT EXISTS idx_device_heartbeats_device_time ON device_heartbeats(device_id, received_at DESC)",
	}

	for _, index := range indexes {
		if err := d.DB.Exec(index).Error; err != nil {
			log.Printf("⚠️ Erro ao criar índice: %s - %v", index, err)
		}
	}

	return nil
}

// createDefaultAdmin cria usuário admin padrão
func (d *Database) createDefaultAdmin() error {
	var count int64
	d.DB.Model(&models.User{}).Where("role = ?", "admin").Count(&count)
	
	if count == 0 {
		admin := &models.User{
			Username: "admin",
			Email:    "<EMAIL>",
			Password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
			Role:     "admin",
			Active:   true,
		}
		
		if err := d.DB.Create(admin).Error; err != nil {
			return fmt.Errorf("erro ao criar admin padrão: %v", err)
		}
		
		log.Println("✅ Usuário admin padrão criado (username: admin, password: password)")
	}
	
	return nil
}

// HealthCheck verifica a saúde da conexão com o banco
func (d *Database) HealthCheck() error {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return fmt.Errorf("erro ao obter instância SQL: %v", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("erro ao fazer ping no banco: %v", err)
	}

	return nil
}

// GetStats retorna estatísticas da conexão
func (d *Database) GetStats() map[string]interface{} {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return map[string]interface{}{"error": err.Error()}
	}

	stats := sqlDB.Stats()
	return map[string]interface{}{
		"open_connections":     stats.OpenConnections,
		"in_use":              stats.InUse,
		"idle":                stats.Idle,
		"wait_count":          stats.WaitCount,
		"wait_duration":       stats.WaitDuration.String(),
		"max_idle_closed":     stats.MaxIdleClosed,
		"max_idle_time_closed": stats.MaxIdleTimeClosed,
		"max_lifetime_closed": stats.MaxLifetimeClosed,
	}
}

// Close fecha a conexão com o banco
func (d *Database) Close() error {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// Transaction executa uma função dentro de uma transação
func (d *Database) Transaction(fn func(*gorm.DB) error) error {
	return d.DB.Transaction(fn)
}

// GetDB retorna a instância do GORM
func (d *Database) GetDB() *gorm.DB {
	return d.DB
}

// Métodos auxiliares para operações comuns

// GetActiveVPNSessions retorna sessões VPN ativas
func (d *Database) GetActiveVPNSessions() ([]models.VPNSession, error) {
	var sessions []models.VPNSession
	err := d.DB.Where("status = ?", "active").
		Preload("Client").
		Order("connected_at DESC").
		Find(&sessions).Error
	return sessions, err
}

// GetRecentSystemEvents retorna eventos recentes do sistema
func (d *Database) GetRecentSystemEvents(limit int) ([]models.SystemEvent, error) {
	var events []models.SystemEvent
	err := d.DB.Where("status = ?", "active").
		Order("occurred_at DESC").
		Limit(limit).
		Find(&events).Error
	return events, err
}

// GetNetworkEventsByClient retorna eventos de rede por cliente
func (d *Database) GetNetworkEventsByClient(clientName string, limit int) ([]models.NetworkEvent, error) {
	var events []models.NetworkEvent
	err := d.DB.Where("client_name = ?", clientName).
		Order("occurred_at DESC").
		Limit(limit).
		Find(&events).Error
	return events, err
}

// GetConnectionMetrics retorna métricas de conexão
func (d *Database) GetConnectionMetrics(hours int) ([]models.ConnectionMetrics, error) {
	var metrics []models.ConnectionMetrics
	since := time.Now().Add(-time.Duration(hours) * time.Hour)
	err := d.DB.Where("timestamp >= ?", since).
		Order("timestamp DESC").
		Find(&metrics).Error
	return metrics, err
}

// GetPerformanceMetrics retorna métricas de performance
func (d *Database) GetPerformanceMetrics(hours int) ([]models.PerformanceMetrics, error) {
	var metrics []models.PerformanceMetrics
	since := time.Now().Add(-time.Duration(hours) * time.Hour)
	err := d.DB.Where("timestamp >= ?", since).
		Order("timestamp DESC").
		Find(&metrics).Error
	return metrics, err
}

// CleanupOldData remove dados antigos para manter performance
func (d *Database) CleanupOldData() error {
	// Remover eventos de sistema mais antigos que 30 dias
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
	
	if err := d.DB.Where("occurred_at < ?", thirtyDaysAgo).Delete(&models.SystemEvent{}).Error; err != nil {
		log.Printf("⚠️ Erro ao limpar eventos antigos: %v", err)
	}
	
	// Remover eventos de rede mais antigos que 15 dias
	fifteenDaysAgo := time.Now().AddDate(0, 0, -15)
	
	if err := d.DB.Where("occurred_at < ?", fifteenDaysAgo).Delete(&models.NetworkEvent{}).Error; err != nil {
		log.Printf("⚠️ Erro ao limpar eventos de rede antigos: %v", err)
	}
	
	// Remover métricas mais antigas que 7 dias
	sevenDaysAgo := time.Now().AddDate(0, 0, -7)
	
	if err := d.DB.Where("timestamp < ?", sevenDaysAgo).Delete(&models.ConnectionMetrics{}).Error; err != nil {
		log.Printf("⚠️ Erro ao limpar métricas de conexão antigas: %v", err)
	}
	
	if err := d.DB.Where("timestamp < ?", sevenDaysAgo).Delete(&models.PerformanceMetrics{}).Error; err != nil {
		log.Printf("⚠️ Erro ao limpar métricas de performance antigas: %v", err)
	}
	
	log.Println("✅ Limpeza de dados antigos concluída")
	return nil
}
