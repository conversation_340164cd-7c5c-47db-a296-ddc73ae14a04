package auth

import (
	"errors"
	"unicode"

	"golang.org/x/crypto/bcrypt"
)

// PasswordService gerencia operações de senha
type PasswordService struct {
	cost int
}

// NewPasswordService cria uma nova instância do serviço de senha
func NewPasswordService() *PasswordService {
	return &PasswordService{
		cost: bcrypt.DefaultCost,
	}
}

// HashPassword gera um hash bcrypt da senha
func (p *PasswordService) HashPassword(password string) (string, error) {
	if err := p.ValidatePassword(password); err != nil {
		return "", err
	}

	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), p.cost)
	if err != nil {
		return "", err
	}

	return string(hashedBytes), nil
}

// VerifyPassword verifica se a senha corresponde ao hash
func (p *PasswordService) VerifyPassword(hashedPassword, password string) error {
	return bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
}

// ValidatePassword valida se a senha atende aos critérios de segurança
func (p *PasswordService) ValidatePassword(password string) error {
	if len(password) < 8 {
		return errors.New("senha deve ter pelo menos 8 caracteres")
	}

	if len(password) > 128 {
		return errors.New("senha deve ter no máximo 128 caracteres")
	}

	var (
		hasUpper   = false
		hasLower   = false
		hasNumber  = false
		hasSpecial = false
	)

	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsNumber(char):
			hasNumber = true
		case unicode.IsPunct(char) || unicode.IsSymbol(char):
			hasSpecial = true
		}
	}

	if !hasUpper {
		return errors.New("senha deve conter pelo menos uma letra maiúscula")
	}

	if !hasLower {
		return errors.New("senha deve conter pelo menos uma letra minúscula")
	}

	if !hasNumber {
		return errors.New("senha deve conter pelo menos um número")
	}

	if !hasSpecial {
		return errors.New("senha deve conter pelo menos um caractere especial")
	}

	return nil
}

// GenerateRandomPassword gera uma senha aleatória segura
func (p *PasswordService) GenerateRandomPassword(length int) string {
	if length < 8 {
		length = 12
	}

	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
	
	// Implementação simples de geração de senha
	// Em produção, usar crypto/rand para maior segurança
	password := make([]byte, length)
	for i := range password {
		password[i] = charset[i%len(charset)]
	}

	return string(password)
}

// IsPasswordStrong verifica se a senha é considerada forte
func (p *PasswordService) IsPasswordStrong(password string) bool {
	if err := p.ValidatePassword(password); err != nil {
		return false
	}

	// Critérios adicionais para senha forte
	if len(password) < 12 {
		return false
	}

	// Verificar se não contém sequências comuns
	commonSequences := []string{
		"123456", "abcdef", "qwerty", "password", "admin",
		"111111", "000000", "987654", "fedcba",
	}

	passwordLower := ""
	for _, r := range password {
		if unicode.IsLetter(r) {
			passwordLower += string(unicode.ToLower(r))
		} else {
			passwordLower += string(r)
		}
	}

	for _, seq := range commonSequences {
		if contains(passwordLower, seq) {
			return false
		}
	}

	return true
}

// contains verifica se uma string contém uma substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) && findSubstring(s, substr) != -1
}

// findSubstring encontra a posição de uma substring
func findSubstring(s, substr string) int {
	if len(substr) == 0 {
		return 0
	}
	if len(substr) > len(s) {
		return -1
	}

	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}
