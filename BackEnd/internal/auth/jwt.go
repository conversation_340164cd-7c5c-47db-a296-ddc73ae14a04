package auth

import (
	"errors"
	"time"

	"vpn-backend/internal/config"
	"vpn-backend/internal/models"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

// JWTService gerencia tokens JWT
type JWTService struct {
	secretKey []byte
	expiry    time.Duration
}

// Claims representa as claims do JWT
type Claims struct {
	UserID   uuid.UUID `json:"user_id"`
	Username string    `json:"username"`
	Email    string    `json:"email"`
	Role     string    `json:"role"`
	jwt.RegisteredClaims
}

// NewJWTService cria uma nova instância do serviço JWT
func NewJWTService(cfg *config.JWTConfig) *JWTService {
	return &JWTService{
		secretKey: []byte(cfg.Secret),
		expiry:    cfg.Expiry,
	}
}

// GenerateToken gera um novo token JWT para o usuário
func (j *JWTService) GenerateToken(user *models.User) (string, error) {
	now := time.Now()
	expirationTime := now.Add(j.expiry)

	claims := &Claims{
		UserID:   user.ID,
		Username: user.Username,
		Email:    user.Email,
		Role:     user.Role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "vpn-backend",
			Subject:   user.ID.String(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(j.secretKey)
	if err != nil {
		return "", err
	}

	return tokenString, nil
}

// ValidateToken valida um token JWT e retorna as claims
func (j *JWTService) ValidateToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// Verificar se o método de assinatura é o esperado
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("método de assinatura inválido")
		}
		return j.secretKey, nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("token inválido")
}

// RefreshToken gera um novo token baseado em um token válido
func (j *JWTService) RefreshToken(tokenString string) (string, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return "", err
	}

	// Verificar se o token não está muito próximo do vencimento
	if time.Until(claims.ExpiresAt.Time) < time.Hour {
		return "", errors.New("token muito próximo do vencimento para refresh")
	}

	// Criar novo token com as mesmas claims mas nova expiração
	now := time.Now()
	expirationTime := now.Add(j.expiry)

	newClaims := &Claims{
		UserID:   claims.UserID,
		Username: claims.Username,
		Email:    claims.Email,
		Role:     claims.Role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "vpn-backend",
			Subject:   claims.UserID.String(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, newClaims)
	return token.SignedString(j.secretKey)
}

// ExtractUserID extrai o ID do usuário do token
func (j *JWTService) ExtractUserID(tokenString string) (uuid.UUID, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return uuid.Nil, err
	}
	return claims.UserID, nil
}

// ExtractUserRole extrai o role do usuário do token
func (j *JWTService) ExtractUserRole(tokenString string) (string, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return "", err
	}
	return claims.Role, nil
}

// IsTokenExpired verifica se o token está expirado
func (j *JWTService) IsTokenExpired(tokenString string) bool {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return true
	}
	return time.Now().After(claims.ExpiresAt.Time)
}

// GetTokenExpiry retorna o tempo de expiração do token
func (j *JWTService) GetTokenExpiry(tokenString string) (time.Time, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return time.Time{}, err
	}
	return claims.ExpiresAt.Time, nil
}

// GetTokenClaims retorna todas as claims do token
func (j *JWTService) GetTokenClaims(tokenString string) (*Claims, error) {
	return j.ValidateToken(tokenString)
}
