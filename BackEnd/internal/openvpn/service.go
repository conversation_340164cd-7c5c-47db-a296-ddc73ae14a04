package openvpn

import (
	"context"
	"log"
	"sync"
	"time"

	"vpn-backend/internal/config"
	"vpn-backend/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// OpenVPNService otimizado com múltiplos arquivos e performance
type OpenVPNService struct {
	db     *gorm.DB
	parser *EnhancedLogParser

	// Canais otimizados para diferentes tipos de eventos
	logChannel     chan *OpenVPNLogEntry
	networkChannel chan *NetworkEvent
	systemChannel  chan *SystemEvent

	// Cache otimizado com TTL
	activeClients map[string]*StatusEntry
	recentEvents  []interface{}
	metrics       map[string]interface{}
	lastUpdate    time.Time
	mutex         sync.RWMutex

	// Controle de recursos
	ctx    context.Context
	cancel context.CancelFunc

	// Configurações adaptativas
	collectInterval time.Duration
	batchSize       int
	maxCacheSize    int

	// Métricas de performance
	eventsProcessed int64
	errorCount      int64
	lastProcessTime time.Time
}

// NewOpenVPNService cria uma nova instância do serviço
func NewOpenVPNService(db *gorm.DB, cfg *config.Config) *OpenVPNService {
	// Configurar arquivos de log para monitoramento
	logFiles := map[string]string{
		"openvpn_main":   cfg.OpenVPN.LogFile,
		"openvpn_status": cfg.OpenVPN.StatusFile,
		"system_log":     cfg.Logs.SystemLogFile,
		"auth_log":       cfg.Logs.AuthLogFile,
		"kern_log":       cfg.Logs.KernLogFile,
	}

	ctx, cancel := context.WithCancel(context.Background())

	service := &OpenVPNService{
		db:              db,
		parser:          NewEnhancedLogParser(logFiles, cfg.OpenVPN.StatusFile),
		logChannel:      make(chan *OpenVPNLogEntry, 2000),
		networkChannel:  make(chan *NetworkEvent, 1000),
		systemChannel:   make(chan *SystemEvent, 500),
		activeClients:   make(map[string]*StatusEntry),
		recentEvents:    make([]interface{}, 0, 1000),
		metrics:         make(map[string]interface{}),
		ctx:             ctx,
		cancel:          cancel,
		collectInterval: cfg.Performance.CollectionInterval,
		batchSize:       cfg.Performance.BatchSize,
		maxCacheSize:    cfg.Performance.MaxCacheSize,
	}

	return service
}

// Start inicia o serviço de monitoramento otimizado
func (s *OpenVPNService) Start() {
	log.Println("🚀 Iniciando serviço OpenVPN otimizado...")

	// Iniciar parser
	if err := s.parser.Start(); err != nil {
		log.Printf("❌ Erro ao iniciar parser: %v", err)
		return
	}

	// Iniciar workers para diferentes tipos de eventos
	go s.monitorMultipleLogs()
	go s.processLogEvents()
	go s.processNetworkEvents()
	go s.processSystemEvents()
	go s.updateMetrics()
	go s.cleanupCache()

	log.Println("✅ Serviço OpenVPN iniciado com sucesso")
}

// Stop para o serviço
func (s *OpenVPNService) Stop() {
	log.Println("🛑 Parando serviço OpenVPN...")
	s.cancel()
	s.parser.Stop()
	log.Println("✅ Serviço OpenVPN parado")
}

// monitorMultipleLogs monitora múltiplos arquivos de log simultaneamente
func (s *OpenVPNService) monitorMultipleLogs() {
	ticker := time.NewTicker(s.collectInterval)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			log.Println("🛑 Parando monitoramento de logs...")
			return
		case <-ticker.C:
			s.collectFromAllSources()
		}
	}
}

// collectFromAllSources coleta dados de todas as fontes
func (s *OpenVPNService) collectFromAllSources() {
	start := time.Now()

	// Atualizar status dos clientes
	s.updateClientStatus()

	// Atualizar métricas de performance
	s.lastProcessTime = time.Now()
	processingTime := s.lastProcessTime.Sub(start)

	// Ajustar intervalo baseado na performance
	s.adjustCollectionInterval(processingTime)
}

// updateClientStatus atualiza o status dos clientes conectados
func (s *OpenVPNService) updateClientStatus() {
	statusEntries, err := s.parser.ReadStatusFile()
	if err != nil {
		log.Printf("⚠️ Erro ao ler arquivo de status: %v", err)
		s.errorCount++
		return
	}

	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Atualizar cache de clientes ativos
	s.activeClients = make(map[string]*StatusEntry)
	for _, entry := range statusEntries {
		entryCopy := entry
		s.activeClients[entry.ClientName] = &entryCopy
	}

	// Atualizar métricas de conexão no banco
	go s.saveConnectionMetrics(len(statusEntries))
}

// adjustCollectionInterval ajusta o intervalo baseado na performance
func (s *OpenVPNService) adjustCollectionInterval(processingTime time.Duration) {
	// Se processamento demorou muito, aumentar intervalo
	if processingTime > 10*time.Second {
		newInterval := s.collectInterval * 2
		if newInterval <= 2*time.Minute {
			s.collectInterval = newInterval
			log.Printf("⚠️ Aumentando intervalo de coleta para %v", s.collectInterval)
		}
	} else if processingTime < 1*time.Second && s.collectInterval > 10*time.Second {
		// Se processamento foi rápido, diminuir intervalo
		newInterval := s.collectInterval / 2
		if newInterval >= 10*time.Second {
			s.collectInterval = newInterval
			log.Printf("✅ Diminuindo intervalo de coleta para %v", s.collectInterval)
		}
	}
}

// processLogEvents processa eventos de log OpenVPN em lotes
func (s *OpenVPNService) processLogEvents() {
	batch := make([]*OpenVPNLogEntry, 0, s.batchSize)
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			// Processar batch final antes de sair
			if len(batch) > 0 {
				s.saveBatchLogEvents(batch)
			}
			return
		case entry := <-s.parser.GetEventBuffer():
			batch = append(batch, entry)
			if len(batch) >= s.batchSize {
				s.saveBatchLogEvents(batch)
				batch = batch[:0] // Reset slice
			}
		case <-ticker.C:
			// Processar batch por timeout
			if len(batch) > 0 {
				s.saveBatchLogEvents(batch)
				batch = batch[:0]
			}
		}
	}
}

// processNetworkEvents processa eventos de rede em lotes
func (s *OpenVPNService) processNetworkEvents() {
	batch := make([]*NetworkEvent, 0, s.batchSize)
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			if len(batch) > 0 {
				s.saveBatchNetworkEvents(batch)
			}
			return
		case event := <-s.parser.GetNetworkBuffer():
			batch = append(batch, event)
			if len(batch) >= s.batchSize {
				s.saveBatchNetworkEvents(batch)
				batch = batch[:0]
			}
		case <-ticker.C:
			if len(batch) > 0 {
				s.saveBatchNetworkEvents(batch)
				batch = batch[:0]
			}
		}
	}
}

// processSystemEvents processa eventos do sistema em lotes
func (s *OpenVPNService) processSystemEvents() {
	batch := make([]*SystemEvent, 0, s.batchSize)
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			if len(batch) > 0 {
				s.saveBatchSystemEvents(batch)
			}
			return
		case event := <-s.parser.GetSystemBuffer():
			batch = append(batch, event)
			if len(batch) >= s.batchSize {
				s.saveBatchSystemEvents(batch)
				batch = batch[:0]
			}
		case <-ticker.C:
			if len(batch) > 0 {
				s.saveBatchSystemEvents(batch)
				batch = batch[:0]
			}
		}
	}
}

// GetActiveClients retorna clientes ativos do cache
func (s *OpenVPNService) GetActiveClients() map[string]*StatusEntry {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// Criar cópia para evitar race conditions
	result := make(map[string]*StatusEntry)
	for k, v := range s.activeClients {
		entryCopy := *v
		result[k] = &entryCopy
	}

	return result
}

// GetMetrics retorna métricas do serviço
func (s *OpenVPNService) GetMetrics() map[string]interface{} {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// Combinar métricas do parser e do serviço
	parserMetrics := s.parser.GetMetrics()
	
	result := make(map[string]interface{})
	for k, v := range parserMetrics {
		result[k] = v
	}
	
	result["active_clients"] = len(s.activeClients)
	result["cache_size"] = len(s.recentEvents)
	result["collection_interval"] = s.collectInterval.String()
	result["service_events_processed"] = s.eventsProcessed
	result["service_error_count"] = s.errorCount
	result["last_update"] = s.lastUpdate

	return result
}

// saveBatchLogEvents salva eventos de log em lote no banco
func (s *OpenVPNService) saveBatchLogEvents(batch []*OpenVPNLogEntry) {
	if len(batch) == 0 {
		return
	}

	// Converter para modelos do banco
	var sessions []models.VPNSession
	var events []models.SystemEvent

	for _, entry := range batch {
		switch entry.Action {
		case EventConnect:
			session := models.VPNSession{
				ID:          uuid.New(),
				ClientName:  entry.ClientName,
				RealIP:      entry.ClientIP,
				VirtualIP:   entry.VirtualIP,
				ConnectedAt: entry.Timestamp,
				Status:      "active",
			}
			sessions = append(sessions, session)

		case EventDisconnect:
			// Atualizar sessão existente
			s.db.Model(&models.VPNSession{}).
				Where("client_name = ? AND status = ?", entry.ClientName, "active").
				Updates(map[string]interface{}{
					"disconnected_at":    entry.Timestamp,
					"status":            "disconnected",
					"disconnect_reason": entry.DisconnectReason,
				})

		case EventAuthFail:
			event := models.SystemEvent{
				ID:         uuid.New(),
				EventType:  "AUTH_FAILURE",
				Severity:   "WARNING",
				Title:      "Falha de Autenticação",
				Message:    "Falha de autenticação para " + entry.ClientName + " de " + entry.ClientIP,
				ClientName: entry.ClientName,
				SourceIP:   entry.ClientIP,
				OccurredAt: entry.Timestamp,
				Status:     "active",
			}
			events = append(events, event)
		}
	}

	// Salvar em lote
	if len(sessions) > 0 {
		if err := s.db.CreateInBatches(sessions, 50).Error; err != nil {
			log.Printf("❌ Erro ao salvar sessões: %v", err)
			s.errorCount++
		}
	}

	if len(events) > 0 {
		if err := s.db.CreateInBatches(events, 50).Error; err != nil {
			log.Printf("❌ Erro ao salvar eventos: %v", err)
			s.errorCount++
		}
	}

	s.eventsProcessed += int64(len(batch))
	log.Printf("✅ Salvos %d eventos de log em lote", len(batch))
}

// saveBatchNetworkEvents salva eventos de rede em lote
func (s *OpenVPNService) saveBatchNetworkEvents(batch []*NetworkEvent) {
	if len(batch) == 0 {
		return
	}

	var networkEvents []models.NetworkEvent

	for _, event := range batch {
		networkEvent := models.NetworkEvent{
			ID:            uuid.New(),
			EventType:     event.EventType,
			SourceIP:      event.SourceIP,
			DestinationIP: event.DestinationIP,
			Port:          event.Port,
			Protocol:      event.Protocol,
			OccurredAt:    event.Timestamp,
			ClientName:    event.ClientName,
			Success:       event.Success,
			Command:       event.Command,
		}
		networkEvents = append(networkEvents, networkEvent)
	}

	if err := s.db.CreateInBatches(networkEvents, 50).Error; err != nil {
		log.Printf("❌ Erro ao salvar eventos de rede: %v", err)
		s.errorCount++
	} else {
		log.Printf("✅ Salvos %d eventos de rede em lote", len(batch))
	}

	s.eventsProcessed += int64(len(batch))
}

// saveBatchSystemEvents salva eventos do sistema em lote
func (s *OpenVPNService) saveBatchSystemEvents(batch []*SystemEvent) {
	if len(batch) == 0 {
		return
	}

	var systemEvents []models.SystemEvent

	for _, event := range batch {
		systemEvent := models.SystemEvent{
			ID:          uuid.New(),
			EventType:   event.EventType,
			Severity:    event.Severity,
			Title:       event.Component + " Event",
			Message:     event.Description,
			OccurredAt:  event.Timestamp,
			Status:      "active",
		}
		systemEvents = append(systemEvents, systemEvent)
	}

	if err := s.db.CreateInBatches(systemEvents, 50).Error; err != nil {
		log.Printf("❌ Erro ao salvar eventos do sistema: %v", err)
		s.errorCount++
	} else {
		log.Printf("✅ Salvos %d eventos do sistema em lote", len(batch))
	}

	s.eventsProcessed += int64(len(batch))
}

// saveConnectionMetrics salva métricas de conexão
func (s *OpenVPNService) saveConnectionMetrics(activeConnections int) {
	// Calcular métricas agregadas
	var totalConnections int64
	var totalBytesIn, totalBytesOut int64
	var avgSessionTime float64

	// Query para obter estatísticas
	s.db.Model(&models.VPNSession{}).Count(&totalConnections)

	s.db.Model(&models.VPNSession{}).
		Select("COALESCE(SUM(bytes_received), 0)").
		Scan(&totalBytesIn)

	s.db.Model(&models.VPNSession{}).
		Select("COALESCE(SUM(bytes_sent), 0)").
		Scan(&totalBytesOut)

	s.db.Model(&models.VPNSession{}).
		Where("disconnected_at IS NOT NULL").
		Select("COALESCE(AVG(duration), 0)").
		Scan(&avgSessionTime)

	metrics := models.ConnectionMetrics{
		ID:                uuid.New(),
		Timestamp:         time.Now(),
		ActiveConnections: activeConnections,
		TotalConnections:  int(totalConnections),
		TotalBytesIn:      totalBytesIn,
		TotalBytesOut:     totalBytesOut,
		AvgSessionTime:    avgSessionTime,
	}

	if err := s.db.Create(&metrics).Error; err != nil {
		log.Printf("⚠️ Erro ao salvar métricas de conexão: %v", err)
	}
}

// updateMetrics atualiza métricas de performance periodicamente
func (s *OpenVPNService) updateMetrics() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.calculateAndUpdateMetrics()
		}
	}
}

// calculateAndUpdateMetrics calcula e atualiza métricas
func (s *OpenVPNService) calculateAndUpdateMetrics() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	now := time.Now()
	timeSinceLastUpdate := now.Sub(s.lastUpdate)

	// Calcular eventos por segundo
	eventsPerSecond := float64(s.eventsProcessed) / timeSinceLastUpdate.Seconds()

	// Salvar métricas de performance no banco
	perfMetrics := models.PerformanceMetrics{
		ID:                uuid.New(),
		Timestamp:         now,
		EventsProcessed:   s.eventsProcessed,
		EventsPerSecond:   eventsPerSecond,
		ErrorCount:        s.errorCount,
		ProcessingLatency: float64(time.Since(s.lastProcessTime).Milliseconds()),
		CacheSize:         len(s.recentEvents),
		ActiveWorkers:     4, // log, network, system, metrics
	}

	if err := s.db.Create(&perfMetrics).Error; err != nil {
		log.Printf("⚠️ Erro ao salvar métricas de performance: %v", err)
	}

	// Atualizar métricas em cache
	s.metrics = map[string]interface{}{
		"events_processed":    s.eventsProcessed,
		"events_per_second":   eventsPerSecond,
		"error_count":         s.errorCount,
		"last_process_time":   s.lastProcessTime,
		"collection_interval": s.collectInterval,
		"active_clients":      len(s.activeClients),
		"cache_size":          len(s.recentEvents),
	}

	s.lastUpdate = now

	// Log métricas periodicamente
	log.Printf("📊 Métricas: %d eventos/s, %d erros, %d clientes ativos",
		int(eventsPerSecond), s.errorCount, len(s.activeClients))
}

// cleanupCache limpa cache antigo periodicamente
func (s *OpenVPNService) cleanupCache() {
	ticker := time.NewTicker(10 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.performCacheCleanup()
		}
	}
}

// performCacheCleanup remove dados antigos do cache
func (s *OpenVPNService) performCacheCleanup() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Limitar tamanho do cache de eventos recentes
	if len(s.recentEvents) > s.maxCacheSize {
		// Manter apenas os eventos mais recentes
		keepCount := s.maxCacheSize / 2
		s.recentEvents = s.recentEvents[len(s.recentEvents)-keepCount:]
		log.Printf("🧹 Cache limpo: mantidos %d eventos recentes", keepCount)
	}

	// Limpar clientes inativos há mais de 1 hora
	cutoff := time.Now().Add(-1 * time.Hour)
	for clientName, status := range s.activeClients {
		if status.LastActivity.Before(cutoff) {
			delete(s.activeClients, clientName)
		}
	}
}
