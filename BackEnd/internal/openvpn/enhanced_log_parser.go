package openvpn

import (
	"bufio"
	"context"
	"crypto/md5"
	"fmt"
	"log"
	"os"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
)

// Event types para diferentes tipos de eventos
const (
	EventConnect     = "CONNECT"
	EventDisconnect  = "DISCONNECT"
	EventAuth        = "AUTH"
	EventAuthFail    = "AUTH_FAIL"
	EventSSH         = "SSH"
	EventSudo        = "SUDO"
	EventReboot      = "REBOOT"
	EventServiceOp   = "SERVICE"
	EventNetworkOp   = "NETWORK"
)

// OpenVPNLogEntry representa uma entrada de log otimizada
type OpenVPNLogEntry struct {
	ID               string    `json:"id"`              // Hash único para deduplicação
	Timestamp        time.Time `json:"timestamp"`
	LogLevel         string    `json:"log_level"`
	Message          string    `json:"message"`
	ClientName       string    `json:"client_name,omitempty"`
	ClientIP         string    `json:"client_ip,omitempty"`
	VirtualIP        string    `json:"virtual_ip,omitempty"`
	Action           string    `json:"action"` // CONNECT, DISCONNECT, AUTH, etc.
	BytesSent        int64     `json:"bytes_sent,omitempty"`
	BytesReceived    int64     `json:"bytes_received,omitempty"`
	Source           string    `json:"source"`          // Arquivo de origem
	Severity         string    `json:"severity"`        // INFO, WARNING, ERROR
	DisconnectReason string    `json:"disconnect_reason,omitempty"`
	SessionDuration  int64     `json:"session_duration,omitempty"`
}

// StatusEntry otimizado com mais informações
type StatusEntry struct {
	ClientName      string    `json:"client_name"`
	RealAddress     string    `json:"real_address"`
	VirtualAddress  string    `json:"virtual_address"`
	BytesReceived   int64     `json:"bytes_received"`
	BytesSent       int64     `json:"bytes_sent"`
	ConnectedSince  time.Time `json:"connected_since"`
	LastActivity    time.Time `json:"last_activity"`
	ConnectionSpeed float64   `json:"connection_speed"` // bytes/sec
}

// NetworkEvent para eventos de rede detectados
type NetworkEvent struct {
	ID            string    `json:"id"`
	Timestamp     time.Time `json:"timestamp"`
	ClientName    string    `json:"client_name"`
	EventType     string    `json:"event_type"` // SSH, VNC, HTTP, FTP
	SourceIP      string    `json:"source_ip"`
	DestinationIP string    `json:"destination_ip"`
	Port          int       `json:"port"`
	Protocol      string    `json:"protocol"`
	Command       string    `json:"command,omitempty"` // Para SSH/sudo
	Success       bool      `json:"success"`
}

// SystemEvent para eventos do sistema
type SystemEvent struct {
	ID          string    `json:"id"`
	Timestamp   time.Time `json:"timestamp"`
	EventType   string    `json:"event_type"` // REBOOT, CRASH, SERVICE_RESTART
	Severity    string    `json:"severity"`   // INFO, WARNING, ERROR, CRITICAL
	Component   string    `json:"component"`  // OPENVPN, SYSTEM, DATABASE
	Description string    `json:"description"`
	Impact      string    `json:"impact"` // Número de clientes afetados
}

// EnhancedLogParser com suporte a múltiplos arquivos e otimizações
type EnhancedLogParser struct {
	// Arquivos de log monitorados
	LogFiles     map[string]string // nome -> caminho
	StatusFile   string

	// Sistema de patterns expandido
	patterns     map[string]*regexp.Regexp
	lastPosition map[string]int64 // Para leitura incremental

	// Sistema de deduplicação
	seenEvents map[string]time.Time
	eventTTL   time.Duration
	mutex      sync.RWMutex

	// Buffers para processamento em lote
	eventBuffer   chan *OpenVPNLogEntry
	networkBuffer chan *NetworkEvent
	systemBuffer  chan *SystemEvent
	batchSize     int
	batchTimeout  time.Duration

	// Controle de recursos
	ctx     context.Context
	cancel  context.CancelFunc
	watcher *fsnotify.Watcher

	// Métricas de performance
	eventsProcessed int64
	lastProcessTime time.Time
	errorCount      int64
}

// NewEnhancedLogParser cria uma nova instância do parser otimizado
func NewEnhancedLogParser(logFiles map[string]string, statusFile string) *EnhancedLogParser {
	ctx, cancel := context.WithCancel(context.Background())

	parser := &EnhancedLogParser{
		LogFiles:      logFiles,
		StatusFile:    statusFile,
		patterns:      make(map[string]*regexp.Regexp),
		lastPosition:  make(map[string]int64),
		seenEvents:    make(map[string]time.Time),
		eventTTL:      5 * time.Minute,
		eventBuffer:   make(chan *OpenVPNLogEntry, 1000),
		networkBuffer: make(chan *NetworkEvent, 500),
		systemBuffer:  make(chan *SystemEvent, 200),
		batchSize:     100,
		batchTimeout:  5 * time.Second,
		ctx:           ctx,
		cancel:        cancel,
	}

	// Inicializar patterns expandidos
	parser.initializePatterns()

	// Inicializar watcher de arquivos
	if err := parser.initializeWatcher(); err != nil {
		log.Printf("⚠️ Erro ao inicializar watcher: %v", err)
	}

	return parser
}

// initializePatterns configura todos os patterns de regex
func (p *EnhancedLogParser) initializePatterns() {
	p.patterns = map[string]*regexp.Regexp{
		// OpenVPN patterns
		"client_connect":    regexp.MustCompile(`(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) .* ([^/]+)/([0-9.]+) MULTI: Learn: ([0-9.]+) -> ([^/]+)/([0-9.]+)`),
		"client_disconnect": regexp.MustCompile(`(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) .* ([^/]+)/([0-9.]+) SIGTERM\[([^\]]+)\]`),
		"auth_success":      regexp.MustCompile(`(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) .* ([^/]+)/([0-9.]+) VERIFY OK: depth=0`),
		"auth_failure":      regexp.MustCompile(`(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) .* ([^/]+)/([0-9.]+) VERIFY ERROR`),
		"tls_handshake":     regexp.MustCompile(`(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) .* ([^/]+)/([0-9.]+) TLS: Initial packet from`),

		// Network events (de auth.log)
		"ssh_login":    regexp.MustCompile(`(\w{3} \d{2} \d{2}:\d{2}:\d{2}) .* sshd\[\d+\]: Accepted .* for (\w+) from ([0-9.]+)`),
		"ssh_failed":   regexp.MustCompile(`(\w{3} \d{2} \d{2}:\d{2}:\d{2}) .* sshd\[\d+\]: Failed .* for (\w+) from ([0-9.]+)`),
		"sudo_command": regexp.MustCompile(`(\w{3} \d{2} \d{2}:\d{2}:\d{2}) .* sudo: (\w+) : TTY=.* ; PWD=.* ; USER=.* ; COMMAND=(.+)`),

		// System events (de syslog)
		"system_reboot":   regexp.MustCompile(`(\w{3} \d{2} \d{2}:\d{2}:\d{2}) .* kernel: \[ *0\.000000\] Linux version`),
		"service_start":   regexp.MustCompile(`(\w{3} \d{2} \d{2}:\d{2}:\d{2}) .* systemd\[\d+\]: Started (.+)\.`),
		"service_stop":    regexp.MustCompile(`(\w{3} \d{2} \d{2}:\d{2}:\d{2}) .* systemd\[\d+\]: Stopped (.+)\.`),
		"openvpn_restart": regexp.MustCompile(`(\w{3} \d{2} \d{2}:\d{2}:\d{2}) .* systemd\[\d+\]: (Started|Stopped) OpenVPN`),

		// Timestamp patterns
		"timestamp_full":   regexp.MustCompile(`^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})`),
		"timestamp_syslog": regexp.MustCompile(`^(\w{3} \d{2} \d{2}:\d{2}:\d{2})`),
	}
}

// initializeWatcher configura o sistema de monitoramento de arquivos
func (p *EnhancedLogParser) initializeWatcher() error {
	var err error
	p.watcher, err = fsnotify.NewWatcher()
	if err != nil {
		return fmt.Errorf("erro ao criar watcher: %v", err)
	}

	// Adicionar todos os arquivos de log ao watcher
	for name, path := range p.LogFiles {
		if err := p.watcher.Add(path); err != nil {
			log.Printf("⚠️ Erro ao adicionar %s (%s) ao watcher: %v", name, path, err)
		} else {
			log.Printf("✅ Monitorando arquivo: %s (%s)", name, path)
		}
	}

	return nil
}

// generateEventID cria um ID único para deduplicação
func (p *EnhancedLogParser) generateEventID(timestamp time.Time, client, action, source string) string {
	data := fmt.Sprintf("%d_%s_%s_%s", timestamp.Unix(), client, action, source)
	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("%x", hash)
}

// isUniqueEvent verifica se o evento já foi processado
func (p *EnhancedLogParser) isUniqueEvent(eventID string) bool {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	// Limpar eventos antigos
	now := time.Now()
	for id, timestamp := range p.seenEvents {
		if now.Sub(timestamp) > p.eventTTL {
			delete(p.seenEvents, id)
		}
	}

	// Verificar se evento já existe
	if _, exists := p.seenEvents[eventID]; exists {
		return false
	}

	// Marcar como visto
	p.seenEvents[eventID] = now
	return true
}

// ParseLogLine analisa uma linha de log com sistema otimizado
func (p *EnhancedLogParser) ParseLogLine(line, source string) (*OpenVPNLogEntry, *NetworkEvent, *SystemEvent) {
	var logEntry *OpenVPNLogEntry
	var networkEvent *NetworkEvent
	var systemEvent *SystemEvent

	// Determinar tipo de timestamp baseado na fonte
	var timestamp time.Time
	var err error

	if strings.Contains(source, "openvpn") {
		if match := p.patterns["timestamp_full"].FindStringSubmatch(line); len(match) > 1 {
			timestamp, err = time.Parse("2006-01-02 15:04:05", match[1])
		}
	} else {
		if match := p.patterns["timestamp_syslog"].FindStringSubmatch(line); len(match) > 1 {
			// Para syslog, assumir ano atual
			currentYear := time.Now().Year()
			timestampStr := fmt.Sprintf("%d %s", currentYear, match[1])
			timestamp, err = time.Parse("2006 Jan 02 15:04:05", timestampStr)
		}
	}

	if err != nil {
		timestamp = time.Now()
	}

	// Processar eventos OpenVPN
	if strings.Contains(source, "openvpn") {
		logEntry = p.parseOpenVPNEvent(line, source, timestamp)
	}

	// Processar eventos de rede
	if strings.Contains(source, "auth") {
		networkEvent = p.parseNetworkEvent(line, source, timestamp)
	}

	// Processar eventos do sistema
	if strings.Contains(source, "syslog") {
		systemEvent = p.parseSystemEvent(line, source, timestamp)
	}

	return logEntry, networkEvent, systemEvent
}

// parseOpenVPNEvent processa eventos específicos do OpenVPN
func (p *EnhancedLogParser) parseOpenVPNEvent(line, source string, timestamp time.Time) *OpenVPNLogEntry {
	entry := &OpenVPNLogEntry{
		Timestamp: timestamp,
		Message:   line,
		Source:    source,
		Severity:  "INFO",
	}

	// Verificar padrões específicos
	if match := p.patterns["client_connect"].FindStringSubmatch(line); len(match) > 6 {
		entry.Action = EventConnect
		entry.ClientName = match[5]
		entry.ClientIP = match[2]
		entry.VirtualIP = match[6]
		entry.Severity = "INFO"
	} else if match := p.patterns["client_disconnect"].FindStringSubmatch(line); len(match) > 3 {
		entry.Action = EventDisconnect
		entry.ClientName = match[2]
		entry.ClientIP = match[3]
		if len(match) > 4 {
			entry.DisconnectReason = match[4]
		}
		entry.Severity = "INFO"
	} else if match := p.patterns["auth_success"].FindStringSubmatch(line); len(match) > 3 {
		entry.Action = EventAuth
		entry.ClientName = match[2]
		entry.ClientIP = match[3]
		entry.Severity = "INFO"
	} else if match := p.patterns["auth_failure"].FindStringSubmatch(line); len(match) > 3 {
		entry.Action = EventAuthFail
		entry.ClientName = match[2]
		entry.ClientIP = match[3]
		entry.Severity = "WARNING"
	} else {
		entry.Action = "LOG"
	}

	// Gerar ID único para deduplicação
	entry.ID = p.generateEventID(timestamp, entry.ClientName, entry.Action, source)

	// Verificar se é evento único
	if !p.isUniqueEvent(entry.ID) {
		return nil // Evento duplicado
	}

	return entry
}

// parseNetworkEvent processa eventos de rede (SSH, sudo, etc.)
func (p *EnhancedLogParser) parseNetworkEvent(line, source string, timestamp time.Time) *NetworkEvent {
	var event *NetworkEvent

	if match := p.patterns["ssh_login"].FindStringSubmatch(line); len(match) > 3 {
		event = &NetworkEvent{
			Timestamp:  timestamp,
			EventType:  EventSSH,
			ClientName: match[2],
			SourceIP:   match[3],
			Protocol:   "SSH",
			Port:       22,
			Success:    true,
		}
	} else if match := p.patterns["ssh_failed"].FindStringSubmatch(line); len(match) > 3 {
		event = &NetworkEvent{
			Timestamp:  timestamp,
			EventType:  EventSSH,
			ClientName: match[2],
			SourceIP:   match[3],
			Protocol:   "SSH",
			Port:       22,
			Success:    false,
		}
	} else if match := p.patterns["sudo_command"].FindStringSubmatch(line); len(match) > 3 {
		event = &NetworkEvent{
			Timestamp:  timestamp,
			EventType:  EventSudo,
			ClientName: match[2],
			Command:    match[3],
			Protocol:   "SUDO",
			Success:    true,
		}
	}

	if event != nil {
		event.ID = p.generateEventID(timestamp, event.ClientName, event.EventType, source)
		if !p.isUniqueEvent(event.ID) {
			return nil // Evento duplicado
		}
	}

	return event
}

// parseSystemEvent processa eventos do sistema
func (p *EnhancedLogParser) parseSystemEvent(line, source string, timestamp time.Time) *SystemEvent {
	var event *SystemEvent

	if match := p.patterns["system_reboot"].FindStringSubmatch(line); len(match) > 0 {
		event = &SystemEvent{
			Timestamp:   timestamp,
			EventType:   EventReboot,
			Severity:    "INFO",
			Component:   "SYSTEM",
			Description: "Sistema reinicializado",
			Impact:      "Todos os clientes desconectados",
		}
	} else if match := p.patterns["openvpn_restart"].FindStringSubmatch(line); len(match) > 2 {
		action := match[2]
		event = &SystemEvent{
			Timestamp:   timestamp,
			EventType:   EventServiceOp,
			Severity:    "WARNING",
			Component:   "OPENVPN",
			Description: fmt.Sprintf("Serviço OpenVPN %s", strings.ToLower(action)),
			Impact:      "Clientes VPN afetados",
		}
	} else if match := p.patterns["service_start"].FindStringSubmatch(line); len(match) > 2 {
		service := match[2]
		if strings.Contains(strings.ToLower(service), "openvpn") {
			event = &SystemEvent{
				Timestamp:   timestamp,
				EventType:   EventServiceOp,
				Severity:    "INFO",
				Component:   "OPENVPN",
				Description: fmt.Sprintf("Serviço iniciado: %s", service),
				Impact:      "Serviço VPN disponível",
			}
		}
	}

	if event != nil {
		event.ID = p.generateEventID(timestamp, event.Component, event.EventType, source)
		if !p.isUniqueEvent(event.ID) {
			return nil // Evento duplicado
		}
	}

	return event
}

// ReadStatusFile lê o arquivo de status do OpenVPN
func (p *EnhancedLogParser) ReadStatusFile() ([]StatusEntry, error) {
	file, err := os.Open(p.StatusFile)
	if err != nil {
		return nil, fmt.Errorf("erro ao abrir arquivo de status: %v", err)
	}
	defer file.Close()

	var entries []StatusEntry
	scanner := bufio.NewScanner(file)
	inClientList := false

	for scanner.Scan() {
		line := scanner.Text()

		if strings.Contains(line, "CLIENT_LIST") {
			inClientList = true
			continue
		}

		if strings.Contains(line, "ROUTING_TABLE") {
			inClientList = false
			break
		}

		if inClientList && line != "" {
			parts := strings.Split(line, ",")
			if len(parts) >= 5 {
				entry := StatusEntry{
					ClientName:     parts[0],
					RealAddress:    parts[1],
					VirtualAddress: parts[2],
				}

				// Parse bytes received/sent
				if len(parts) > 3 {
					if bytes, err := strconv.ParseInt(parts[3], 10, 64); err == nil {
						entry.BytesReceived = bytes
					}
				}
				if len(parts) > 4 {
					if bytes, err := strconv.ParseInt(parts[4], 10, 64); err == nil {
						entry.BytesSent = bytes
					}
				}

				// Parse connected since
				if len(parts) > 5 {
					if timestamp, err := time.Parse("Mon Jan 2 15:04:05 2006", parts[5]); err == nil {
						entry.ConnectedSince = timestamp
					}
				}

				entry.LastActivity = time.Now()
				entries = append(entries, entry)
			}
		}
	}

	return entries, scanner.Err()
}

// Start inicia o monitoramento de arquivos
func (p *EnhancedLogParser) Start() error {
	if p.watcher == nil {
		return fmt.Errorf("watcher não inicializado")
	}

	go p.watchFiles()
	log.Println("✅ Parser de logs iniciado")
	return nil
}

// Stop para o monitoramento
func (p *EnhancedLogParser) Stop() {
	if p.cancel != nil {
		p.cancel()
	}
	if p.watcher != nil {
		p.watcher.Close()
	}
	log.Println("🛑 Parser de logs parado")
}

// watchFiles monitora mudanças nos arquivos
func (p *EnhancedLogParser) watchFiles() {
	for {
		select {
		case <-p.ctx.Done():
			return
		case event, ok := <-p.watcher.Events:
			if !ok {
				return
			}
			if event.Op&fsnotify.Write == fsnotify.Write {
				p.processFileChange(event.Name)
			}
		case err, ok := <-p.watcher.Errors:
			if !ok {
				return
			}
			log.Printf("⚠️ Erro no watcher: %v", err)
		}
	}
}

// processFileChange processa mudanças em um arquivo
func (p *EnhancedLogParser) processFileChange(filename string) {
	// Encontrar o nome do arquivo no mapa
	var sourceName string
	for name, path := range p.LogFiles {
		if path == filename {
			sourceName = name
			break
		}
	}

	if sourceName == "" {
		return
	}

	file, err := os.Open(filename)
	if err != nil {
		log.Printf("⚠️ Erro ao abrir arquivo %s: %v", filename, err)
		return
	}
	defer file.Close()

	// Ir para a última posição lida
	lastPos := p.lastPosition[sourceName]
	file.Seek(lastPos, 0)

	scanner := bufio.NewScanner(file)
	linesProcessed := 0

	for scanner.Scan() && linesProcessed < p.batchSize {
		line := scanner.Text()
		if line == "" {
			continue
		}

		// Processar linha
		logEntry, networkEvent, systemEvent := p.ParseLogLine(line, sourceName)

		// Enviar eventos para canais
		if logEntry != nil {
			select {
			case p.eventBuffer <- logEntry:
				p.eventsProcessed++
			default:
				log.Println("⚠️ Buffer de eventos cheio")
			}
		}

		if networkEvent != nil {
			select {
			case p.networkBuffer <- networkEvent:
				p.eventsProcessed++
			default:
				log.Println("⚠️ Buffer de eventos de rede cheio")
			}
		}

		if systemEvent != nil {
			select {
			case p.systemBuffer <- systemEvent:
				p.eventsProcessed++
			default:
				log.Println("⚠️ Buffer de eventos do sistema cheio")
			}
		}

		linesProcessed++
	}

	// Atualizar posição
	currentPos, _ := file.Seek(0, 1)
	p.lastPosition[sourceName] = currentPos
}

// GetEventBuffer retorna o canal de eventos de log
func (p *EnhancedLogParser) GetEventBuffer() <-chan *OpenVPNLogEntry {
	return p.eventBuffer
}

// GetNetworkBuffer retorna o canal de eventos de rede
func (p *EnhancedLogParser) GetNetworkBuffer() <-chan *NetworkEvent {
	return p.networkBuffer
}

// GetSystemBuffer retorna o canal de eventos do sistema
func (p *EnhancedLogParser) GetSystemBuffer() <-chan *SystemEvent {
	return p.systemBuffer
}

// GetMetrics retorna métricas do parser
func (p *EnhancedLogParser) GetMetrics() map[string]interface{} {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	return map[string]interface{}{
		"events_processed": p.eventsProcessed,
		"error_count":      p.errorCount,
		"last_process_time": p.lastProcessTime,
		"seen_events_count": len(p.seenEvents),
	}
}
