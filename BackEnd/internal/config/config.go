package config

import (
	"log"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
)

// Config estrutura de configuração da aplicação
type Config struct {
	// Servidor
	Server ServerConfig `json:"server"`
	
	// Banco de dados
	Database DatabaseConfig `json:"database"`
	
	// JWT
	JWT JWTConfig `json:"jwt"`
	
	// OpenVPN
	OpenVPN OpenVPNConfig `json:"openvpn"`
	
	// Sistema de logs
	Logs LogsConfig `json:"logs"`
	
	// Performance
	Performance PerformanceConfig `json:"performance"`
	
	// Segurança
	Security SecurityConfig `json:"security"`
	
	// WebSocket
	WebSocket WebSocketConfig `json:"websocket"`
	
	// Monitoramento
	Monitoring MonitoringConfig `json:"monitoring"`
}

type ServerConfig struct {
	Host string `json:"host"`
	Port string `json:"port"`
	Mode string `json:"mode"`
}

type DatabaseConfig struct {
	Host     string `json:"host"`
	Port     string `json:"port"`
	User     string `json:"user"`
	Password string `json:"password"`
	Name     string `json:"name"`
	SSLMode  string `json:"ssl_mode"`
}

type JWTConfig struct {
	Secret string        `json:"secret"`
	Expiry time.Duration `json:"expiry"`
}

type OpenVPNConfig struct {
	LogFile    string `json:"log_file"`
	StatusFile string `json:"status_file"`
	ConfigDir  string `json:"config_dir"`
}

type LogsConfig struct {
	SystemLogFile string `json:"system_log_file"`
	AuthLogFile   string `json:"auth_log_file"`
	KernLogFile   string `json:"kern_log_file"`
}

type PerformanceConfig struct {
	CollectionInterval time.Duration `json:"collection_interval"`
	BatchSize          int           `json:"batch_size"`
	MaxCacheSize       int           `json:"max_cache_size"`
	WorkerPoolSize     int           `json:"worker_pool_size"`
}

type SecurityConfig struct {
	VPNNetwork   string   `json:"vpn_network"`
	AllowedIPs   []string `json:"allowed_ips"`
	RateLimit    int      `json:"rate_limit"`
}

type WebSocketConfig struct {
	ReadBuffer     int `json:"read_buffer"`
	WriteBuffer    int `json:"write_buffer"`
	MaxConnections int `json:"max_connections"`
}

type MonitoringConfig struct {
	MetricsEnabled       bool          `json:"metrics_enabled"`
	HealthCheckInterval  time.Duration `json:"health_check_interval"`
	LogLevel            string        `json:"log_level"`
}

// LoadConfig carrega as configurações do arquivo .env
func LoadConfig() (*Config, error) {
	// Carregar arquivo .env
	if err := godotenv.Load("configs/.env"); err != nil {
		log.Printf("⚠️ Arquivo .env não encontrado, usando variáveis de ambiente: %v", err)
	}

	config := &Config{
		Server: ServerConfig{
			Host: getEnv("SERVER_HOST", "0.0.0.0"),
			Port: getEnv("SERVER_PORT", "8080"),
			Mode: getEnv("GIN_MODE", "release"),
		},
		Database: DatabaseConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnv("DB_PORT", "5432"),
			User:     getEnv("DB_USER", "vpn_user"),
			Password: getEnv("DB_PASSWORD", "vpn_secure_password_2024"),
			Name:     getEnv("DB_NAME", "vpn_monitoring"),
			SSLMode:  getEnv("DB_SSLMODE", "disable"),
		},
		JWT: JWTConfig{
			Secret: getEnv("JWT_SECRET", "vpn_jwt_super_secret_key_2024_secure_token"),
			Expiry: parseDuration(getEnv("JWT_EXPIRY", "24h")),
		},
		OpenVPN: OpenVPNConfig{
			LogFile:    getEnv("OPENVPN_LOG_FILE", "/root/VPN/OpenVPN/logs/openvpn.log"),
			StatusFile: getEnv("OPENVPN_STATUS_FILE", "/root/VPN/OpenVPN/logs/status.log"),
			ConfigDir:  getEnv("OPENVPN_CONFIG_DIR", "/root/VPN/OpenVPN"),
		},
		Logs: LogsConfig{
			SystemLogFile: getEnv("SYSTEM_LOG_FILE", "/var/log/syslog"),
			AuthLogFile:   getEnv("AUTH_LOG_FILE", "/var/log/auth.log"),
			KernLogFile:   getEnv("KERN_LOG_FILE", "/var/log/kern.log"),
		},
		Performance: PerformanceConfig{
			CollectionInterval: parseDuration(getEnv("COLLECTION_INTERVAL", "30s")),
			BatchSize:          parseInt(getEnv("BATCH_SIZE", "100")),
			MaxCacheSize:       parseInt(getEnv("MAX_CACHE_SIZE", "1000")),
			WorkerPoolSize:     parseInt(getEnv("WORKER_POOL_SIZE", "10")),
		},
		Security: SecurityConfig{
			VPNNetwork: getEnv("VPN_NETWORK", "********/24"),
			AllowedIPs: parseStringSlice(getEnv("ALLOWED_IPS", "********/24,127.0.0.1/32")),
			RateLimit:  parseInt(getEnv("RATE_LIMIT", "1000")),
		},
		WebSocket: WebSocketConfig{
			ReadBuffer:     parseInt(getEnv("WS_READ_BUFFER", "1024")),
			WriteBuffer:    parseInt(getEnv("WS_WRITE_BUFFER", "1024")),
			MaxConnections: parseInt(getEnv("WS_MAX_CONNECTIONS", "100")),
		},
		Monitoring: MonitoringConfig{
			MetricsEnabled:      parseBool(getEnv("METRICS_ENABLED", "true")),
			HealthCheckInterval: parseDuration(getEnv("HEALTH_CHECK_INTERVAL", "30s")),
			LogLevel:           getEnv("LOG_LEVEL", "info"),
		},
	}

	return config, nil
}

// Funções auxiliares
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func parseInt(s string) int {
	if i, err := strconv.Atoi(s); err == nil {
		return i
	}
	return 0
}

func parseBool(s string) bool {
	if b, err := strconv.ParseBool(s); err == nil {
		return b
	}
	return false
}

func parseDuration(s string) time.Duration {
	if d, err := time.ParseDuration(s); err == nil {
		return d
	}
	return 0
}

func parseStringSlice(s string) []string {
	if s == "" {
		return []string{}
	}
	
	var result []string
	for _, item := range splitString(s, ",") {
		if trimmed := trimSpace(item); trimmed != "" {
			result = append(result, trimmed)
		}
	}
	return result
}

// Funções auxiliares simples para evitar dependências externas
func splitString(s, sep string) []string {
	if s == "" {
		return []string{}
	}
	
	var result []string
	start := 0
	for i := 0; i < len(s); i++ {
		if s[i:i+len(sep)] == sep {
			result = append(result, s[start:i])
			start = i + len(sep)
			i += len(sep) - 1
		}
	}
	result = append(result, s[start:])
	return result
}

func trimSpace(s string) string {
	start := 0
	end := len(s)
	
	for start < end && (s[start] == ' ' || s[start] == '\t' || s[start] == '\n' || s[start] == '\r') {
		start++
	}
	
	for end > start && (s[end-1] == ' ' || s[end-1] == '\t' || s[end-1] == '\n' || s[end-1] == '\r') {
		end--
	}
	
	return s[start:end]
}
