package handlers

import (
	"net/http"
	"strings"

	"vpn-backend/internal/auth"
	"vpn-backend/internal/models"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AuthHandler gerencia autenticação
type AuthHandler struct {
	db              *gorm.DB
	jwtService      *auth.JWTService
	passwordService *auth.PasswordService
}

// NewAuthHandler cria uma nova instância do handler
func NewAuthHandler(db *gorm.DB, jwtService *auth.JWTService, passwordService *auth.PasswordService) *AuthHandler {
	return &AuthHandler{
		db:              db,
		jwtService:      jwtService,
		passwordService: passwordService,
	}
}

// LoginRequest estrutura da requisição de login
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse estrutura da resposta de login
type LoginResponse struct {
	Token     string      `json:"token"`
	User      models.User `json:"user"`
	ExpiresAt string      `json:"expires_at"`
}

// RegisterRequest estrutura da requisição de registro
type RegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=8"`
	Role     string `json:"role,omitempty"`
}

// ChangePasswordRequest estrutura da requisição de mudança de senha
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,min=8"`
}

// Login autentica um usuário
// @Summary Login de usuário
// @Description Autentica um usuário e retorna um token JWT
// @Tags auth
// @Accept json
// @Produce json
// @Param login body LoginRequest true "Dados de login"
// @Success 200 {object} LoginResponse
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Router /auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Dados inválidos",
		})
		return
	}

	// Buscar usuário
	var user models.User
	if err := h.db.Where("username = ? AND active = ?", req.Username, true).First(&user).Error; err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Credenciais inválidas",
		})
		return
	}

	// Verificar senha
	if err := h.passwordService.VerifyPassword(user.Password, req.Password); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Credenciais inválidas",
		})
		return
	}

	// Gerar token
	token, err := h.jwtService.GenerateToken(&user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao gerar token",
		})
		return
	}

	// Obter expiração do token
	expiresAt, _ := h.jwtService.GetTokenExpiry(token)

	// Remover senha da resposta
	user.Password = ""

	c.JSON(http.StatusOK, LoginResponse{
		Token:     token,
		User:      user,
		ExpiresAt: expiresAt.Format("2006-01-02T15:04:05Z"),
	})
}

// Register registra um novo usuário
// @Summary Registro de usuário
// @Description Registra um novo usuário no sistema
// @Tags auth
// @Accept json
// @Produce json
// @Param register body RegisterRequest true "Dados de registro"
// @Success 201 {object} models.User
// @Failure 400 {object} map[string]string
// @Failure 409 {object} map[string]string
// @Router /auth/register [post]
func (h *AuthHandler) Register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Dados inválidos",
		})
		return
	}

	// Verificar se usuário já existe
	var existingUser models.User
	if err := h.db.Where("username = ? OR email = ?", req.Username, req.Email).First(&existingUser).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{
			"error": "Usuário ou email já existe",
		})
		return
	}

	// Hash da senha
	hashedPassword, err := h.passwordService.HashPassword(req.Password)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Definir role padrão
	role := req.Role
	if role == "" {
		role = "user"
	}

	// Criar usuário
	user := models.User{
		ID:       uuid.New(),
		Username: req.Username,
		Email:    req.Email,
		Password: hashedPassword,
		Role:     role,
		Active:   true,
	}

	if err := h.db.Create(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao criar usuário",
		})
		return
	}

	// Remover senha da resposta
	user.Password = ""

	c.JSON(http.StatusCreated, user)
}

// RefreshToken renova um token JWT
// @Summary Renovar token
// @Description Renova um token JWT válido
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Router /auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	// Obter token atual do header
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Token requerido",
		})
		return
	}

	// Extrair token
	tokenParts := strings.Split(authHeader, " ")
	if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Formato de token inválido",
		})
		return
	}

	currentToken := tokenParts[1]

	// Renovar token
	newToken, err := h.jwtService.RefreshToken(currentToken)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Erro ao renovar token",
		})
		return
	}

	// Obter expiração do novo token
	expiresAt, _ := h.jwtService.GetTokenExpiry(newToken)

	c.JSON(http.StatusOK, gin.H{
		"token":      newToken,
		"expires_at": expiresAt.Format("2006-01-02T15:04:05Z"),
	})
}

// ChangePassword altera a senha do usuário
// @Summary Alterar senha
// @Description Altera a senha do usuário autenticado
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param password body ChangePasswordRequest true "Dados de mudança de senha"
// @Success 200 {object} map[string]string
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Router /auth/change-password [post]
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Dados inválidos",
		})
		return
	}

	// Obter ID do usuário do contexto
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Usuário não autenticado",
		})
		return
	}

	// Buscar usuário
	var user models.User
	if err := h.db.Where("id = ?", userID).First(&user).Error; err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Usuário não encontrado",
		})
		return
	}

	// Verificar senha atual
	if err := h.passwordService.VerifyPassword(user.Password, req.CurrentPassword); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Senha atual incorreta",
		})
		return
	}

	// Hash da nova senha
	hashedPassword, err := h.passwordService.HashPassword(req.NewPassword)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Atualizar senha
	if err := h.db.Model(&user).Update("password", hashedPassword).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao atualizar senha",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Senha alterada com sucesso",
	})
}

// GetProfile retorna o perfil do usuário autenticado
// @Summary Obter perfil
// @Description Retorna o perfil do usuário autenticado
// @Tags auth
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.User
// @Failure 401 {object} map[string]string
// @Router /auth/profile [get]
func (h *AuthHandler) GetProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Usuário não autenticado",
		})
		return
	}

	var user models.User
	if err := h.db.Where("id = ?", userID).First(&user).Error; err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "Usuário não encontrado",
		})
		return
	}

	// Remover senha da resposta
	user.Password = ""

	c.JSON(http.StatusOK, user)
}
