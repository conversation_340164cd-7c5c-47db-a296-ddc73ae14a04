package handlers

import (
	"net/http"
	"strconv"
	"time"

	"vpn-backend/internal/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// EventsHandler gerencia eventos do sistema
type EventsHandler struct {
	db *gorm.DB
}

// NewEventsHandler cria uma nova instância do handler
func NewEventsHandler(db *gorm.DB) *EventsHandler {
	return &EventsHandler{
		db: db,
	}
}

// GetSystemEvents retorna eventos do sistema
// @Summary Obter eventos do sistema
// @Description Retorna lista de eventos do sistema com paginação e filtros
// @Tags events
// @Produce json
// @Security BearerAuth
// @Param page query int false "Página" default(1)
// @Param limit query int false "Limite por página" default(50)
// @Param severity query string false "Filtrar por severidade"
// @Param event_type query string false "Filtrar por tipo de evento"
// @Param hours query int false "Últimas N horas" default(24)
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]string
// @Router /events/system [get]
func (h *EventsHandler) GetSystemEvents(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))
	severity := c.Query("severity")
	eventType := c.Query("event_type")
	hours, _ := strconv.Atoi(c.DefaultQuery("hours", "24"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 50
	}

	offset := (page - 1) * limit
	since := time.Now().Add(-time.Duration(hours) * time.Hour)

	query := h.db.Model(&models.SystemEvent{}).
		Where("occurred_at >= ?", since)

	if severity != "" {
		query = query.Where("severity = ?", severity)
	}

	if eventType != "" {
		query = query.Where("event_type = ?", eventType)
	}

	var events []models.SystemEvent
	var total int64

	// Contar total
	query.Count(&total)

	// Buscar eventos
	err := query.Order("occurred_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&events).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar eventos do sistema",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"events": events,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + int64(limit) - 1) / int64(limit),
		},
		"filters": gin.H{
			"severity":   severity,
			"event_type": eventType,
			"hours":      hours,
		},
	})
}

// GetNetworkEvents retorna eventos de rede
// @Summary Obter eventos de rede
// @Description Retorna lista de eventos de rede com paginação e filtros
// @Tags events
// @Produce json
// @Security BearerAuth
// @Param page query int false "Página" default(1)
// @Param limit query int false "Limite por página" default(50)
// @Param client query string false "Filtrar por cliente"
// @Param event_type query string false "Filtrar por tipo de evento"
// @Param success query bool false "Filtrar por sucesso"
// @Param hours query int false "Últimas N horas" default(24)
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]string
// @Router /events/network [get]
func (h *EventsHandler) GetNetworkEvents(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))
	client := c.Query("client")
	eventType := c.Query("event_type")
	successStr := c.Query("success")
	hours, _ := strconv.Atoi(c.DefaultQuery("hours", "24"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 50
	}

	offset := (page - 1) * limit
	since := time.Now().Add(-time.Duration(hours) * time.Hour)

	query := h.db.Model(&models.NetworkEvent{}).
		Where("occurred_at >= ?", since)

	if client != "" {
		query = query.Where("client_name ILIKE ?", "%"+client+"%")
	}

	if eventType != "" {
		query = query.Where("event_type = ?", eventType)
	}

	if successStr != "" {
		success, _ := strconv.ParseBool(successStr)
		query = query.Where("success = ?", success)
	}

	var events []models.NetworkEvent
	var total int64

	// Contar total
	query.Count(&total)

	// Buscar eventos
	err := query.Order("occurred_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&events).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar eventos de rede",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"events": events,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": (total + int64(limit) - 1) / int64(limit),
		},
		"filters": gin.H{
			"client":     client,
			"event_type": eventType,
			"success":    successStr,
			"hours":      hours,
		},
	})
}

// GetEventStats retorna estatísticas de eventos
// @Summary Obter estatísticas de eventos
// @Description Retorna estatísticas agregadas de eventos
// @Tags events
// @Produce json
// @Security BearerAuth
// @Param hours query int false "Últimas N horas" default(24)
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]string
// @Router /events/stats [get]
func (h *EventsHandler) GetEventStats(c *gin.Context) {
	hours, _ := strconv.Atoi(c.DefaultQuery("hours", "24"))
	since := time.Now().Add(-time.Duration(hours) * time.Hour)

	// Estatísticas de eventos do sistema
	var systemStats []struct {
		Severity string `json:"severity"`
		Count    int64  `json:"count"`
	}

	h.db.Model(&models.SystemEvent{}).
		Where("occurred_at >= ?", since).
		Select("severity, COUNT(*) as count").
		Group("severity").
		Find(&systemStats)

	var systemEventTypes []struct {
		EventType string `json:"event_type"`
		Count     int64  `json:"count"`
	}

	h.db.Model(&models.SystemEvent{}).
		Where("occurred_at >= ?", since).
		Select("event_type, COUNT(*) as count").
		Group("event_type").
		Find(&systemEventTypes)

	// Estatísticas de eventos de rede
	var networkStats []struct {
		EventType string `json:"event_type"`
		Success   bool   `json:"success"`
		Count     int64  `json:"count"`
	}

	h.db.Model(&models.NetworkEvent{}).
		Where("occurred_at >= ?", since).
		Select("event_type, success, COUNT(*) as count").
		Group("event_type, success").
		Find(&networkStats)

	// Eventos por hora
	var eventsPerHour []struct {
		Hour  string `json:"hour"`
		Count int64  `json:"count"`
	}

	h.db.Model(&models.SystemEvent{}).
		Where("occurred_at >= ?", since).
		Select("DATE_TRUNC('hour', occurred_at) as hour, COUNT(*) as count").
		Group("hour").
		Order("hour").
		Find(&eventsPerHour)

	// Top clientes com mais eventos de rede
	var topClients []struct {
		ClientName string `json:"client_name"`
		Count      int64  `json:"count"`
	}

	h.db.Model(&models.NetworkEvent{}).
		Where("occurred_at >= ?", since).
		Select("client_name, COUNT(*) as count").
		Group("client_name").
		Order("count DESC").
		Limit(10).
		Find(&topClients)

	c.JSON(http.StatusOK, gin.H{
		"system_events": gin.H{
			"by_severity":   systemStats,
			"by_type":       systemEventTypes,
		},
		"network_events": gin.H{
			"by_type_success": networkStats,
			"top_clients":     topClients,
		},
		"timeline": gin.H{
			"events_per_hour": eventsPerHour,
		},
		"period": gin.H{
			"hours": hours,
			"since": since.Format("2006-01-02T15:04:05Z"),
		},
	})
}

// MarkEventAsRead marca um evento como lido
// @Summary Marcar evento como lido
// @Description Marca um evento do sistema como lido
// @Tags events
// @Produce json
// @Security BearerAuth
// @Param id path string true "ID do evento"
// @Success 200 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /events/system/{id}/read [post]
func (h *EventsHandler) MarkEventAsRead(c *gin.Context) {
	eventID := c.Param("id")

	var event models.SystemEvent
	err := h.db.Where("id = ?", eventID).First(&event).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Evento não encontrado",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Erro ao buscar evento",
			})
		}
		return
	}

	// Atualizar status para inactive (lido)
	err = h.db.Model(&event).Update("status", "inactive").Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao marcar evento como lido",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Evento marcado como lido",
	})
}

// GetRecentAlerts retorna alertas recentes
// @Summary Obter alertas recentes
// @Description Retorna lista de alertas recentes (eventos críticos e de warning)
// @Tags events
// @Produce json
// @Security BearerAuth
// @Param limit query int false "Limite de resultados" default(20)
// @Success 200 {array} models.SystemEvent
// @Failure 500 {object} map[string]string
// @Router /events/alerts [get]
func (h *EventsHandler) GetRecentAlerts(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if limit < 1 || limit > 100 {
		limit = 20
	}

	var alerts []models.SystemEvent
	err := h.db.Where("severity IN (?, ?) AND status = ?", "WARNING", "CRITICAL", "active").
		Order("occurred_at DESC").
		Limit(limit).
		Find(&alerts).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar alertas",
		})
		return
	}

	c.JSON(http.StatusOK, alerts)
}
