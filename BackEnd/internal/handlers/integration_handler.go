package handlers

import (
	"log"
	"net/http"
	"time"

	"vpn-backend/internal/models"
	"vpn-backend/internal/openvpn"
	"vpn-backend/internal/websocket"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// IntegrationHandler handler para integração OpenVPN (Fase 5)
type IntegrationHandler struct {
	db         *gorm.DB
	vpnService *openvpn.OpenVPNService
	notifier   *websocket.Notifier
}

// NewIntegrationHandler cria um novo handler de integração
func NewIntegrationHandler(db *gorm.DB, vpnService *openvpn.OpenVPNService, notifier *websocket.Notifier) *IntegrationHandler {
	return &IntegrationHandler{
		db:         db,
		vpnService: vpnService,
		notifier:   notifier,
	}
}

// HandleClientConnect processa eventos de conexão de clientes OpenVPN
func (h *IntegrationHandler) HandleClientConnect(c *gin.Context) {
	var event struct {
		ClientName  string `json:"client_name" binding:"required"`
		ExternalIP  string `json:"external_ip" binding:"required"`
		VpnIP       string `json:"vpn_ip" binding:"required"`
		Timestamp   string `json:"timestamp" binding:"required"`
		EventType   string `json:"event_type" binding:"required"`
	}

	if err := c.ShouldBindJSON(&event); err != nil {
		log.Printf("❌ Erro ao fazer bind do evento de conexão: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Dados inválidos",
			"details": err.Error(),
		})
		return
	}

	// Parse do timestamp
	timestamp, err := time.Parse(time.RFC3339, event.Timestamp)
	if err != nil {
		log.Printf("❌ Erro ao fazer parse do timestamp: %v", err)
		timestamp = time.Now()
	}

	// Criar registro de conexão
	connection := models.VPNConnection{
		ClientName:  event.ClientName,
		ExternalIP:  event.ExternalIP,
		VpnIP:       event.VpnIP,
		ConnectedAt: timestamp,
		Status:      models.StatusConnected,
	}

	if err := h.db.Create(&connection).Error; err != nil {
		log.Printf("❌ Erro ao salvar conexão VPN: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro interno do servidor",
		})
		return
	}

	// Criar evento do sistema
	systemEvent := models.SystemEvent{
		EventType:  models.EventTypeConnect,
		Severity:   models.SeverityInfo,
		Title:      "Cliente VPN Conectado",
		Message:    "Cliente " + event.ClientName + " conectou de " + event.ExternalIP + " com IP VPN " + event.VpnIP,
		Source:     "openvpn",
		ClientName: event.ClientName,
		SourceIP:   event.ExternalIP,
		OccurredAt: timestamp,
		Status:     models.StatusActive,
	}

	if err := h.db.Create(&systemEvent).Error; err != nil {
		log.Printf("⚠️ Erro ao salvar evento do sistema: %v", err)
	}

	// Notificar via WebSocket se disponível
	if h.notifier != nil {
		h.notifier.BroadcastEvent("client_connected", map[string]interface{}{
			"client_name": event.ClientName,
			"external_ip": event.ExternalIP,
			"vpn_ip":      event.VpnIP,
			"timestamp":   timestamp,
		})
	}

	log.Printf("✅ Cliente VPN conectado: %s (%s -> %s)", event.ClientName, event.ExternalIP, event.VpnIP)

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Evento de conexão processado",
		"connection_id": connection.ID,
	})
}

// HandleClientDisconnect processa eventos de desconexão de clientes OpenVPN
func (h *IntegrationHandler) HandleClientDisconnect(c *gin.Context) {
	var event struct {
		ClientName     string `json:"client_name" binding:"required"`
		BytesReceived  int64  `json:"bytes_received"`
		BytesSent      int64  `json:"bytes_sent"`
		Duration       int    `json:"duration"`
		Timestamp      string `json:"timestamp" binding:"required"`
		EventType      string `json:"event_type" binding:"required"`
	}

	if err := c.ShouldBindJSON(&event); err != nil {
		log.Printf("❌ Erro ao fazer bind do evento de desconexão: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Dados inválidos",
			"details": err.Error(),
		})
		return
	}

	// Parse do timestamp
	timestamp, err := time.Parse(time.RFC3339, event.Timestamp)
	if err != nil {
		log.Printf("❌ Erro ao fazer parse do timestamp: %v", err)
		timestamp = time.Now()
	}

	// Atualizar conexão existente
	result := h.db.Model(&models.VPNConnection{}).
		Where("client_name = ? AND status = ?", event.ClientName, models.StatusConnected).
		Updates(map[string]interface{}{
			"status":          models.StatusDisconnected,
			"disconnected_at": timestamp,
			"bytes_received":  event.BytesReceived,
			"bytes_sent":      event.BytesSent,
			"duration":        event.Duration,
		})

	if result.Error != nil {
		log.Printf("❌ Erro ao atualizar conexão VPN: %v", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro interno do servidor",
		})
		return
	}

	// Criar evento do sistema
	systemEvent := models.SystemEvent{
		EventType:  models.EventTypeDisconnect,
		Severity:   models.SeverityInfo,
		Title:      "Cliente VPN Desconectado",
		Message:    "Cliente " + event.ClientName + " desconectou após " + time.Duration(event.Duration*int(time.Second)).String(),
		Source:     "openvpn",
		ClientName: event.ClientName,
		OccurredAt: timestamp,
		Status:     models.StatusActive,
	}

	if err := h.db.Create(&systemEvent).Error; err != nil {
		log.Printf("⚠️ Erro ao salvar evento do sistema: %v", err)
	}

	// Notificar via WebSocket se disponível
	if h.notifier != nil {
		h.notifier.BroadcastEvent("client_disconnected", map[string]interface{}{
			"client_name":     event.ClientName,
			"bytes_received":  event.BytesReceived,
			"bytes_sent":      event.BytesSent,
			"duration":        event.Duration,
			"timestamp":       timestamp,
		})
	}

	log.Printf("✅ Cliente VPN desconectado: %s (RX: %d, TX: %d, Duração: %ds)", 
		event.ClientName, event.BytesReceived, event.BytesSent, event.Duration)

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Evento de desconexão processado",
		"updated_connections": result.RowsAffected,
	})
}

// GetRealTimeStatus retorna status em tempo real da VPN
func (h *IntegrationHandler) GetRealTimeStatus(c *gin.Context) {
	var activeConnections []models.VPNConnection
	if err := h.db.Where("status = ?", models.StatusConnected).Find(&activeConnections).Error; err != nil {
		log.Printf("❌ Erro ao buscar conexões ativas: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro interno do servidor",
		})
		return
	}

	// Calcular estatísticas
	var totalBytesIn, totalBytesOut int64
	for _, conn := range activeConnections {
		totalBytesIn += conn.BytesReceived
		totalBytesOut += conn.BytesSent
	}

	status := gin.H{
		"server_status":       "running",
		"active_connections":  len(activeConnections),
		"total_bytes_in":      totalBytesIn,
		"total_bytes_out":     totalBytesOut,
		"clients":             activeConnections,
		"timestamp":           time.Now(),
		"uptime":              time.Since(time.Now().Add(-24 * time.Hour)), // Placeholder
	}

	c.JSON(http.StatusOK, status)
}

// GetClientDetails retorna detalhes de um cliente específico
func (h *IntegrationHandler) GetClientDetails(c *gin.Context) {
	clientName := c.Param("name")
	if clientName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Nome do cliente requerido",
		})
		return
	}

	var connections []models.VPNConnection
	if err := h.db.Where("client_name = ?", clientName).
		Order("connected_at DESC").
		Limit(10).
		Find(&connections).Error; err != nil {
		log.Printf("❌ Erro ao buscar conexões do cliente: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro interno do servidor",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"client_name": clientName,
		"connections": connections,
		"total_connections": len(connections),
	})
}

// DisconnectClient desconecta um cliente via management interface
func (h *IntegrationHandler) DisconnectClient(c *gin.Context) {
	clientName := c.Param("name")
	if clientName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Nome do cliente requerido",
		})
		return
	}

	// Usar management interface para desconectar (implementação futura)
	// Por enquanto, apenas marcar como desconectado no banco
	result := h.db.Model(&models.VPNConnection{}).
		Where("client_name = ? AND status = ?", clientName, models.StatusConnected).
		Updates(map[string]interface{}{
			"status":          models.StatusDisconnected,
			"disconnected_at": time.Now(),
		})

	if result.Error != nil {
		log.Printf("❌ Erro ao desconectar cliente: %v", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro interno do servidor",
		})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Cliente não encontrado ou já desconectado",
		})
		return
	}

	log.Printf("✅ Cliente desconectado via API: %s", clientName)

	c.JSON(http.StatusOK, gin.H{
		"status": "success",
		"message": "Cliente desconectado",
		"client": clientName,
	})
}

// RegisterDevice registra um novo dispositivo via scripts de setup
func (h *IntegrationHandler) RegisterDevice(c *gin.Context) {
	var device models.RegisteredDevice
	if err := c.ShouldBindJSON(&device); err != nil {
		log.Printf("❌ Erro ao fazer bind do dispositivo: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Dados inválidos",
			"details": err.Error(),
		})
		return
	}

	device.RegisteredAt = time.Now()
	device.Status = models.StatusActive

	if err := h.db.Create(&device).Error; err != nil {
		log.Printf("❌ Erro ao registrar dispositivo: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao registrar dispositivo",
		})
		return
	}

	log.Printf("✅ Dispositivo registrado: %s (%s)", device.Hostname, device.DeviceType)

	c.JSON(http.StatusOK, gin.H{
		"status": "success",
		"message": "Dispositivo registrado com sucesso",
		"device_id": device.ID,
	})
}

// DeviceHeartbeat processa heartbeat de dispositivos
func (h *IntegrationHandler) DeviceHeartbeat(c *gin.Context) {
	var heartbeat models.DeviceHeartbeat
	if err := c.ShouldBindJSON(&heartbeat); err != nil {
		log.Printf("❌ Erro ao fazer bind do heartbeat: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Dados inválidos",
			"details": err.Error(),
		})
		return
	}

	heartbeat.ReceivedAt = time.Now()

	if err := h.db.Create(&heartbeat).Error; err != nil {
		log.Printf("❌ Erro ao salvar heartbeat: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao salvar heartbeat",
		})
		return
	}

	// Atualizar last_seen do dispositivo
	h.db.Model(&models.RegisteredDevice{}).
		Where("hostname = ?", heartbeat.DeviceID).
		Update("last_seen", time.Now())

	c.JSON(http.StatusOK, gin.H{
		"status": "success",
		"message": "Heartbeat recebido",
	})
}
