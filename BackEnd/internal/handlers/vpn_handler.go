package handlers

import (
	"net/http"
	"strconv"
	"time"

	"vpn-backend/internal/models"
	"vpn-backend/internal/openvpn"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// VPNHandler gerencia operações VPN
type VPNHandler struct {
	db         *gorm.DB
	vpnService *openvpn.OpenVPNService
}

// NewVPNHandler cria uma nova instância do handler
func NewVPNHandler(db *gorm.DB, vpnService *openvpn.OpenVPNService) *VPNHandler {
	return &VPNHandler{
		db:         db,
		vpnService: vpnService,
	}
}

// GetActiveSessions retorna sessões VPN ativas
// @Summary Obter sessões ativas
// @Description Retorna lista de sessões VPN ativas
// @Tags vpn
// @Produce json
// @Security BearerAuth
// @Success 200 {array} models.VPNSession
// @Failure 500 {object} map[string]string
// @Router /vpn/sessions/active [get]
func (h *VPNHandler) GetActiveSessions(c *gin.Context) {
	var sessions []models.VPNSession
	
	err := h.db.Where("status = ?", "active").
		Preload("Client").
		Order("connected_at DESC").
		Find(&sessions).Error
		
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar sessões ativas",
		})
		return
	}

	c.JSON(http.StatusOK, sessions)
}

// GetSessionHistory retorna histórico de sessões
// @Summary Obter histórico de sessões
// @Description Retorna histórico de sessões VPN com paginação
// @Tags vpn
// @Produce json
// @Security BearerAuth
// @Param page query int false "Página" default(1)
// @Param limit query int false "Limite por página" default(50)
// @Param client query string false "Filtrar por cliente"
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]string
// @Router /vpn/sessions/history [get]
func (h *VPNHandler) GetSessionHistory(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))
	client := c.Query("client")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 50
	}

	offset := (page - 1) * limit

	query := h.db.Model(&models.VPNSession{}).Preload("Client")
	
	if client != "" {
		query = query.Where("client_name ILIKE ?", "%"+client+"%")
	}

	var sessions []models.VPNSession
	var total int64

	// Contar total
	query.Count(&total)

	// Buscar sessões
	err := query.Order("connected_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&sessions).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar histórico de sessões",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"sessions": sessions,
		"pagination": gin.H{
			"page":       page,
			"limit":      limit,
			"total":      total,
			"total_pages": (total + int64(limit) - 1) / int64(limit),
		},
	})
}

// GetClients retorna lista de clientes VPN
// @Summary Obter clientes VPN
// @Description Retorna lista de clientes VPN cadastrados
// @Tags vpn
// @Produce json
// @Security BearerAuth
// @Success 200 {array} models.VPNClient
// @Failure 500 {object} map[string]string
// @Router /vpn/clients [get]
func (h *VPNHandler) GetClients(c *gin.Context) {
	var clients []models.VPNClient
	
	err := h.db.Order("name ASC").Find(&clients).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar clientes",
		})
		return
	}

	c.JSON(http.StatusOK, clients)
}

// GetClientDetails retorna detalhes de um cliente específico
// @Summary Obter detalhes do cliente
// @Description Retorna detalhes de um cliente VPN específico
// @Tags vpn
// @Produce json
// @Security BearerAuth
// @Param name path string true "Nome do cliente"
// @Success 200 {object} map[string]interface{}
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /vpn/clients/{name} [get]
func (h *VPNHandler) GetClientDetails(c *gin.Context) {
	clientName := c.Param("name")

	var client models.VPNClient
	err := h.db.Where("name = ?", clientName).
		Preload("Sessions", func(db *gorm.DB) *gorm.DB {
			return db.Order("connected_at DESC").Limit(10)
		}).
		First(&client).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Cliente não encontrado",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Erro ao buscar cliente",
			})
		}
		return
	}

	// Buscar estatísticas do cliente
	var stats struct {
		TotalSessions    int64   `json:"total_sessions"`
		TotalBytesIn     int64   `json:"total_bytes_in"`
		TotalBytesOut    int64   `json:"total_bytes_out"`
		AvgSessionTime   float64 `json:"avg_session_time"`
		LastConnection   *time.Time `json:"last_connection"`
	}

	h.db.Model(&models.VPNSession{}).
		Where("client_name = ?", clientName).
		Count(&stats.TotalSessions)

	h.db.Model(&models.VPNSession{}).
		Where("client_name = ?", clientName).
		Select("COALESCE(SUM(bytes_received), 0)").
		Scan(&stats.TotalBytesIn)

	h.db.Model(&models.VPNSession{}).
		Where("client_name = ?", clientName).
		Select("COALESCE(SUM(bytes_sent), 0)").
		Scan(&stats.TotalBytesOut)

	h.db.Model(&models.VPNSession{}).
		Where("client_name = ? AND disconnected_at IS NOT NULL", clientName).
		Select("COALESCE(AVG(duration), 0)").
		Scan(&stats.AvgSessionTime)

	h.db.Model(&models.VPNSession{}).
		Where("client_name = ?", clientName).
		Select("MAX(connected_at)").
		Scan(&stats.LastConnection)

	c.JSON(http.StatusOK, gin.H{
		"client": client,
		"stats":  stats,
	})
}

// GetRealtimeStatus retorna status em tempo real
// @Summary Obter status em tempo real
// @Description Retorna status em tempo real dos clientes conectados
// @Tags vpn
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]string
// @Router /vpn/status/realtime [get]
func (h *VPNHandler) GetRealtimeStatus(c *gin.Context) {
	// Obter clientes ativos do cache do serviço
	activeClients := h.vpnService.GetActiveClients()

	// Obter métricas do serviço
	metrics := h.vpnService.GetMetrics()

	// Buscar estatísticas gerais do banco
	var totalClients int64
	var totalSessions int64
	var activeSessions int64

	h.db.Model(&models.VPNClient{}).Count(&totalClients)
	h.db.Model(&models.VPNSession{}).Count(&totalSessions)
	h.db.Model(&models.VPNSession{}).Where("status = ?", "active").Count(&activeSessions)

	c.JSON(http.StatusOK, gin.H{
		"active_clients": activeClients,
		"metrics":        metrics,
		"summary": gin.H{
			"total_clients":   totalClients,
			"total_sessions":  totalSessions,
			"active_sessions": activeSessions,
			"connected_now":   len(activeClients),
		},
		"timestamp": time.Now().UTC(),
	})
}

// DisconnectClient desconecta um cliente específico
// @Summary Desconectar cliente
// @Description Desconecta um cliente VPN específico
// @Tags vpn
// @Produce json
// @Security BearerAuth
// @Param name path string true "Nome do cliente"
// @Success 200 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /vpn/clients/{name}/disconnect [post]
func (h *VPNHandler) DisconnectClient(c *gin.Context) {
	clientName := c.Param("name")

	// Verificar se cliente está conectado
	var session models.VPNSession
	err := h.db.Where("client_name = ? AND status = ?", clientName, "active").
		First(&session).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Cliente não está conectado",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Erro ao verificar status do cliente",
			})
		}
		return
	}

	// Atualizar sessão como desconectada
	now := time.Now()
	err = h.db.Model(&session).Updates(map[string]interface{}{
		"disconnected_at":    now,
		"status":            "disconnected",
		"disconnect_reason": "Desconectado pelo administrador",
	}).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao desconectar cliente",
		})
		return
	}

	// TODO: Implementar comando real de desconexão via management interface
	// Por enquanto, apenas atualizar no banco

	c.JSON(http.StatusOK, gin.H{
		"message": "Cliente desconectado com sucesso",
		"client":  clientName,
	})
}
