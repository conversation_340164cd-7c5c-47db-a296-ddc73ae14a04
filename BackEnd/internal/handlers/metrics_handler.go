package handlers

import (
	"net/http"
	"strconv"
	"time"

	"vpn-backend/internal/models"
	"vpn-backend/internal/openvpn"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// MetricsHandler gerencia métricas do sistema
type MetricsHandler struct {
	db         *gorm.DB
	vpnService *openvpn.OpenVPNService
}

// NewMetricsHandler cria uma nova instância do handler
func NewMetricsHandler(db *gorm.DB, vpnService *openvpn.OpenVPNService) *MetricsHandler {
	return &MetricsHandler{
		db:         db,
		vpnService: vpnService,
	}
}

// GetConnectionMetrics retorna métricas de conexão
// @Summary Obter métricas de conexão
// @Description Retorna métricas de conexão VPN ao longo do tempo
// @Tags metrics
// @Produce json
// @Security BearerAuth
// @Param hours query int false "Últimas N horas" default(24)
// @Param interval query string false "Intervalo de agregação" default("1h")
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]string
// @Router /metrics/connections [get]
func (h *MetricsHandler) GetConnectionMetrics(c *gin.Context) {
	hours, _ := strconv.Atoi(c.DefaultQuery("hours", "24"))
	interval := c.DefaultQuery("interval", "1h")

	since := time.Now().Add(-time.Duration(hours) * time.Hour)

	var metrics []models.ConnectionMetrics
	err := h.db.Where("timestamp >= ?", since).
		Order("timestamp ASC").
		Find(&metrics).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar métricas de conexão",
		})
		return
	}

	// Calcular estatísticas agregadas
	var totalConnections int64
	var maxActiveConnections int
	var totalBytesIn, totalBytesOut int64

	for _, metric := range metrics {
		totalConnections += int64(metric.TotalConnections)
		if metric.ActiveConnections > maxActiveConnections {
			maxActiveConnections = metric.ActiveConnections
		}
		totalBytesIn += metric.TotalBytesIn
		totalBytesOut += metric.TotalBytesOut
	}

	// Métricas atuais do serviço
	currentMetrics := h.vpnService.GetMetrics()

	c.JSON(http.StatusOK, gin.H{
		"metrics": metrics,
		"summary": gin.H{
			"total_connections":     totalConnections,
			"max_active_connections": maxActiveConnections,
			"total_bytes_in":        totalBytesIn,
			"total_bytes_out":       totalBytesOut,
			"current_active":        currentMetrics["active_clients"],
		},
		"period": gin.H{
			"hours":    hours,
			"interval": interval,
			"since":    since.Format("2006-01-02T15:04:05Z"),
		},
	})
}

// GetPerformanceMetrics retorna métricas de performance
// @Summary Obter métricas de performance
// @Description Retorna métricas de performance do sistema
// @Tags metrics
// @Produce json
// @Security BearerAuth
// @Param hours query int false "Últimas N horas" default(24)
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]string
// @Router /metrics/performance [get]
func (h *MetricsHandler) GetPerformanceMetrics(c *gin.Context) {
	hours, _ := strconv.Atoi(c.DefaultQuery("hours", "24"))
	since := time.Now().Add(-time.Duration(hours) * time.Hour)

	var metrics []models.PerformanceMetrics
	err := h.db.Where("timestamp >= ?", since).
		Order("timestamp ASC").
		Find(&metrics).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Erro ao buscar métricas de performance",
		})
		return
	}

	// Calcular estatísticas
	var totalEvents int64
	var maxEventsPerSecond float64
	var totalErrors int64
	var avgLatency float64

	if len(metrics) > 0 {
		for _, metric := range metrics {
			totalEvents += metric.EventsProcessed
			if metric.EventsPerSecond > maxEventsPerSecond {
				maxEventsPerSecond = metric.EventsPerSecond
			}
			totalErrors += metric.ErrorCount
			avgLatency += metric.ProcessingLatency
		}
		avgLatency = avgLatency / float64(len(metrics))
	}

	// Métricas atuais do serviço
	currentMetrics := h.vpnService.GetMetrics()

	c.JSON(http.StatusOK, gin.H{
		"metrics": metrics,
		"summary": gin.H{
			"total_events":         totalEvents,
			"max_events_per_second": maxEventsPerSecond,
			"total_errors":         totalErrors,
			"avg_latency_ms":       avgLatency,
			"current_events_per_second": currentMetrics["events_per_second"],
			"current_error_count":  currentMetrics["error_count"],
		},
		"period": gin.H{
			"hours": hours,
			"since": since.Format("2006-01-02T15:04:05Z"),
		},
	})
}

// GetDashboardMetrics retorna métricas para dashboard
// @Summary Obter métricas do dashboard
// @Description Retorna métricas resumidas para o dashboard principal
// @Tags metrics
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]string
// @Router /metrics/dashboard [get]
func (h *MetricsHandler) GetDashboardMetrics(c *gin.Context) {
	now := time.Now()
	last24h := now.Add(-24 * time.Hour)
	last7d := now.Add(-7 * 24 * time.Hour)

	// Métricas atuais do serviço
	serviceMetrics := h.vpnService.GetMetrics()
	activeClients := h.vpnService.GetActiveClients()

	// Estatísticas de sessões
	var sessionStats struct {
		ActiveSessions   int64 `json:"active_sessions"`
		TotalSessions24h int64 `json:"total_sessions_24h"`
		TotalSessions7d  int64 `json:"total_sessions_7d"`
	}

	h.db.Model(&models.VPNSession{}).
		Where("status = ?", "active").
		Count(&sessionStats.ActiveSessions)

	h.db.Model(&models.VPNSession{}).
		Where("connected_at >= ?", last24h).
		Count(&sessionStats.TotalSessions24h)

	h.db.Model(&models.VPNSession{}).
		Where("connected_at >= ?", last7d).
		Count(&sessionStats.TotalSessions7d)

	// Estatísticas de eventos
	var eventStats struct {
		SystemEvents24h  int64 `json:"system_events_24h"`
		NetworkEvents24h int64 `json:"network_events_24h"`
		CriticalEvents   int64 `json:"critical_events"`
		WarningEvents    int64 `json:"warning_events"`
	}

	h.db.Model(&models.SystemEvent{}).
		Where("occurred_at >= ?", last24h).
		Count(&eventStats.SystemEvents24h)

	h.db.Model(&models.NetworkEvent{}).
		Where("occurred_at >= ?", last24h).
		Count(&eventStats.NetworkEvents24h)

	h.db.Model(&models.SystemEvent{}).
		Where("occurred_at >= ? AND severity = ? AND status = ?", last24h, "CRITICAL", "active").
		Count(&eventStats.CriticalEvents)

	h.db.Model(&models.SystemEvent{}).
		Where("occurred_at >= ? AND severity = ? AND status = ?", last24h, "WARNING", "active").
		Count(&eventStats.WarningEvents)

	// Estatísticas de tráfego
	var trafficStats struct {
		TotalBytesIn  int64 `json:"total_bytes_in"`
		TotalBytesOut int64 `json:"total_bytes_out"`
	}

	h.db.Model(&models.VPNSession{}).
		Where("connected_at >= ?", last24h).
		Select("COALESCE(SUM(bytes_received), 0)").
		Scan(&trafficStats.TotalBytesIn)

	h.db.Model(&models.VPNSession{}).
		Where("connected_at >= ?", last24h).
		Select("COALESCE(SUM(bytes_sent), 0)").
		Scan(&trafficStats.TotalBytesOut)

	// Top clientes por tráfego (últimas 24h)
	var topClients []struct {
		ClientName    string `json:"client_name"`
		TotalBytes    int64  `json:"total_bytes"`
		SessionCount  int64  `json:"session_count"`
	}

	h.db.Model(&models.VPNSession{}).
		Where("connected_at >= ?", last24h).
		Select("client_name, SUM(bytes_received + bytes_sent) as total_bytes, COUNT(*) as session_count").
		Group("client_name").
		Order("total_bytes DESC").
		Limit(5).
		Find(&topClients)

	// Eventos recentes por hora (últimas 24h)
	var eventsTimeline []struct {
		Hour  string `json:"hour"`
		Count int64  `json:"count"`
	}

	h.db.Model(&models.SystemEvent{}).
		Where("occurred_at >= ?", last24h).
		Select("DATE_TRUNC('hour', occurred_at) as hour, COUNT(*) as count").
		Group("hour").
		Order("hour").
		Find(&eventsTimeline)

	// Status de saúde do sistema
	healthStatus := "healthy"
	if eventStats.CriticalEvents > 0 {
		healthStatus = "critical"
	} else if eventStats.WarningEvents > 5 {
		healthStatus = "warning"
	}

	c.JSON(http.StatusOK, gin.H{
		"overview": gin.H{
			"active_clients":     len(activeClients),
			"active_sessions":    sessionStats.ActiveSessions,
			"events_per_second":  serviceMetrics["events_per_second"],
			"system_health":      healthStatus,
		},
		"sessions": sessionStats,
		"events":   eventStats,
		"traffic":  trafficStats,
		"service_metrics": gin.H{
			"collection_interval": serviceMetrics["collection_interval"],
			"cache_size":         serviceMetrics["cache_size"],
			"error_count":        serviceMetrics["error_count"],
			"last_update":        serviceMetrics["last_update"],
		},
		"top_clients":      topClients,
		"events_timeline":  eventsTimeline,
		"timestamp":        now.UTC(),
	})
}

// GetSystemHealth retorna status de saúde do sistema
// @Summary Obter status de saúde
// @Description Retorna status de saúde detalhado do sistema
// @Tags metrics
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]string
// @Router /metrics/health [get]
func (h *MetricsHandler) GetSystemHealth(c *gin.Context) {
	now := time.Now()
	
	// Verificar saúde do banco de dados
	sqlDB, err := h.db.DB()
	dbHealthy := true
	if err != nil || sqlDB.Ping() != nil {
		dbHealthy = false
	}

	// Métricas do serviço VPN
	serviceMetrics := h.vpnService.GetMetrics()
	
	// Verificar se há eventos críticos recentes
	var criticalEvents int64
	h.db.Model(&models.SystemEvent{}).
		Where("occurred_at >= ? AND severity = ? AND status = ?", 
			now.Add(-1*time.Hour), "CRITICAL", "active").
		Count(&criticalEvents)

	// Verificar se o serviço está processando eventos
	lastProcessTime, ok := serviceMetrics["last_process_time"].(time.Time)
	serviceActive := ok && time.Since(lastProcessTime) < 5*time.Minute

	// Determinar status geral
	overallStatus := "healthy"
	if !dbHealthy || criticalEvents > 0 {
		overallStatus = "critical"
	} else if !serviceActive {
		overallStatus = "warning"
	}

	c.JSON(http.StatusOK, gin.H{
		"status": overallStatus,
		"timestamp": now.UTC(),
		"components": gin.H{
			"database": gin.H{
				"status": map[bool]string{true: "healthy", false: "critical"}[dbHealthy],
				"healthy": dbHealthy,
			},
			"vpn_service": gin.H{
				"status": map[bool]string{true: "healthy", false: "warning"}[serviceActive],
				"active": serviceActive,
				"last_process": lastProcessTime,
			},
			"log_parser": gin.H{
				"status": "healthy",
				"events_processed": serviceMetrics["events_processed"],
				"error_count": serviceMetrics["error_count"],
			},
		},
		"alerts": gin.H{
			"critical_events": criticalEvents,
		},
		"uptime": gin.H{
			"service_start": serviceMetrics["last_update"],
		},
	})
}
