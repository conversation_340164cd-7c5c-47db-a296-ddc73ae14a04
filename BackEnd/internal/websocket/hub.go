package websocket

import (
	"encoding/json"
	"log"
	"net/http"
	"sync"
	"time"

	"vpn-backend/internal/config"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

// Hub gerencia conexões WebSocket
type Hub struct {
	// Conexões registradas
	clients map[*Client]bool

	// Canal para registrar clientes
	register chan *Client

	// Canal para desregistrar clientes
	unregister chan *Client

	// Canal para broadcast de mensagens
	broadcast chan []byte

	// Configurações
	config *config.WebSocketConfig

	// Mutex para thread safety
	mutex sync.RWMutex
}

// Client representa uma conexão WebSocket
type Client struct {
	// Conexão WebSocket
	conn *websocket.Conn

	// Canal para enviar mensagens
	send chan []byte

	// Hub que gerencia este cliente
	hub *Hub

	// ID único do cliente
	id string

	// Informações do usuário (se autenticado)
	userID   string
	username string
	role     string
}

// Message representa uma mensagem WebSocket
type Message struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
	Source    string      `json:"source,omitempty"`
}

// Upgrader para WebSocket
var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// Permitir apenas origens da VPN
		origin := r.Header.Get("Origin")
		allowedOrigins := []string{
			"https://********",
			"https://localhost",
			"https://127.0.0.1",
		}
		
		for _, allowed := range allowedOrigins {
			if origin == allowed {
				return true
			}
		}
		return false
	},
}

// NewHub cria um novo hub WebSocket
func NewHub(cfg *config.WebSocketConfig) *Hub {
	return &Hub{
		clients:    make(map[*Client]bool),
		register:   make(chan *Client),
		unregister: make(chan *Client),
		broadcast:  make(chan []byte, 256),
		config:     cfg,
	}
}

// Run inicia o hub WebSocket
func (h *Hub) Run() {
	log.Println("🚀 Iniciando hub WebSocket...")
	
	for {
		select {
		case client := <-h.register:
			h.mutex.Lock()
			h.clients[client] = true
			h.mutex.Unlock()
			
			log.Printf("✅ Cliente WebSocket conectado: %s (total: %d)", 
				client.id, len(h.clients))
			
			// Enviar mensagem de boas-vindas
			welcome := Message{
				Type:      "welcome",
				Data:      map[string]string{"message": "Conectado ao VPN Backend"},
				Timestamp: time.Now(),
				Source:    "system",
			}
			client.SendMessage(welcome)

		case client := <-h.unregister:
			h.mutex.Lock()
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				close(client.send)
			}
			h.mutex.Unlock()
			
			log.Printf("🔌 Cliente WebSocket desconectado: %s (total: %d)", 
				client.id, len(h.clients))

		case message := <-h.broadcast:
			h.mutex.RLock()
			for client := range h.clients {
				select {
				case client.send <- message:
				default:
					close(client.send)
					delete(h.clients, client)
				}
			}
			h.mutex.RUnlock()
		}
	}
}

// HandleWebSocket gerencia conexões WebSocket
func (h *Hub) HandleWebSocket(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("❌ Erro ao fazer upgrade WebSocket: %v", err)
		return
	}

	// Criar cliente
	client := &Client{
		conn: conn,
		send: make(chan []byte, 256),
		hub:  h,
		id:   generateClientID(),
	}

	// Obter informações do usuário se autenticado
	if userID, exists := c.Get("user_id"); exists {
		client.userID = userID.(string)
	}
	if username, exists := c.Get("username"); exists {
		client.username = username.(string)
	}
	if role, exists := c.Get("role"); exists {
		client.role = role.(string)
	}

	// Registrar cliente
	h.register <- client

	// Iniciar goroutines para leitura e escrita
	go client.writePump()
	go client.readPump()
}

// BroadcastMessage envia mensagem para todos os clientes
func (h *Hub) BroadcastMessage(msg Message) {
	data, err := json.Marshal(msg)
	if err != nil {
		log.Printf("❌ Erro ao serializar mensagem: %v", err)
		return
	}

	select {
	case h.broadcast <- data:
	default:
		log.Println("⚠️ Canal de broadcast cheio, mensagem descartada")
	}
}

// BroadcastToRole envia mensagem apenas para usuários com role específico
func (h *Hub) BroadcastToRole(msg Message, role string) {
	data, err := json.Marshal(msg)
	if err != nil {
		log.Printf("❌ Erro ao serializar mensagem: %v", err)
		return
	}

	h.mutex.RLock()
	defer h.mutex.RUnlock()

	for client := range h.clients {
		if client.role == role {
			select {
			case client.send <- data:
			default:
				close(client.send)
				delete(h.clients, client)
			}
		}
	}
}

// GetConnectedClients retorna número de clientes conectados
func (h *Hub) GetConnectedClients() int {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return len(h.clients)
}

// SendMessage envia mensagem para este cliente específico
func (c *Client) SendMessage(msg Message) {
	data, err := json.Marshal(msg)
	if err != nil {
		log.Printf("❌ Erro ao serializar mensagem: %v", err)
		return
	}

	select {
	case c.send <- data:
	default:
		close(c.send)
		delete(c.hub.clients, c)
	}
}

// readPump lê mensagens do cliente
func (c *Client) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	// Configurar timeouts
	c.conn.SetReadLimit(512)
	c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("❌ Erro WebSocket: %v", err)
			}
			break
		}

		// Processar mensagem recebida
		c.handleMessage(message)
	}
}

// writePump escreve mensagens para o cliente
func (c *Client) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// Adicionar mensagens enfileiradas
			n := len(c.send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage processa mensagens recebidas do cliente
func (c *Client) handleMessage(message []byte) {
	var msg Message
	if err := json.Unmarshal(message, &msg); err != nil {
		log.Printf("❌ Erro ao deserializar mensagem: %v", err)
		return
	}

	// Processar diferentes tipos de mensagem
	switch msg.Type {
	case "ping":
		response := Message{
			Type:      "pong",
			Data:      map[string]string{"message": "pong"},
			Timestamp: time.Now(),
			Source:    "system",
		}
		c.SendMessage(response)

	case "subscribe":
		// Implementar sistema de subscrição para diferentes tipos de eventos
		log.Printf("📡 Cliente %s se inscreveu em: %v", c.id, msg.Data)

	default:
		log.Printf("⚠️ Tipo de mensagem desconhecido: %s", msg.Type)
	}
}

// generateClientID gera um ID único para o cliente
func generateClientID() string {
	return time.Now().Format("20060102150405") + "-" + 
		   string(rune(time.Now().Nanosecond()%26+65))
}
