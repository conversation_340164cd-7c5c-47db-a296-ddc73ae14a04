package websocket

import (
	"log"
	"time"

	"vpn-backend/internal/models"
	"vpn-backend/internal/openvpn"
)

// Notifier gerencia notificações em tempo real
type Notifier struct {
	hub *Hub
}

// NewNotifier cria uma nova instância do notificador
func NewNotifier(hub *Hub) *Notifier {
	return &Notifier{
		hub: hub,
	}
}

// NotifyVPNConnection notifica sobre nova conexão VPN
func (n *Notifier) NotifyVPNConnection(session *models.VPNSession) {
	msg := Message{
		Type: "vpn_connection",
		Data: map[string]interface{}{
			"event":       "client_connected",
			"client_name": session.ClientName,
			"real_ip":     session.RealIP,
			"virtual_ip":  session.VirtualIP,
			"connected_at": session.ConnectedAt,
		},
		Timestamp: time.Now(),
		Source:    "vpn",
	}

	n.hub.BroadcastMessage(msg)
	log.Printf("📡 Notificação enviada: Cliente %s conectado", session.ClientName)
}

// NotifyVPNDisconnection notifica sobre desconexão VPN
func (n *Notifier) NotifyVPNDisconnection(session *models.VPNSession) {
	msg := Message{
		Type: "vpn_disconnection",
		Data: map[string]interface{}{
			"event":             "client_disconnected",
			"client_name":       session.ClientName,
			"real_ip":           session.RealIP,
			"virtual_ip":        session.VirtualIP,
			"disconnected_at":   session.DisconnectedAt,
			"disconnect_reason": session.DisconnectReason,
			"duration":          session.Duration,
		},
		Timestamp: time.Now(),
		Source:    "vpn",
	}

	n.hub.BroadcastMessage(msg)
	log.Printf("📡 Notificação enviada: Cliente %s desconectado", session.ClientName)
}

// NotifySystemEvent notifica sobre eventos do sistema
func (n *Notifier) NotifySystemEvent(event *models.SystemEvent) {
	msg := Message{
		Type: "system_event",
		Data: map[string]interface{}{
			"id":          event.ID,
			"event_type":  event.EventType,
			"severity":    event.Severity,
			"title":       event.Title,
			"message":     event.Message,
			"source":      event.Source,
			"client_name": event.ClientName,
			"source_ip":   event.SourceIP,
			"occurred_at": event.OccurredAt,
		},
		Timestamp: time.Now(),
		Source:    "system",
	}

	// Enviar para todos se crítico, apenas admins se warning
	if event.Severity == "CRITICAL" {
		n.hub.BroadcastMessage(msg)
	} else if event.Severity == "WARNING" {
		n.hub.BroadcastToRole(msg, "admin")
	}

	log.Printf("📡 Notificação enviada: Evento %s (%s)", event.EventType, event.Severity)
}

// NotifyNetworkEvent notifica sobre eventos de rede
func (n *Notifier) NotifyNetworkEvent(event *models.NetworkEvent) {
	// Notificar apenas eventos importantes (falhas de SSH, comandos sudo)
	if event.EventType == "SSH" && !event.Success {
		msg := Message{
			Type: "network_event",
			Data: map[string]interface{}{
				"event":       "ssh_failed",
				"client_name": event.ClientName,
				"source_ip":   event.SourceIP,
				"protocol":    event.Protocol,
				"port":        event.Port,
				"occurred_at": event.OccurredAt,
			},
			Timestamp: time.Now(),
			Source:    "network",
		}

		n.hub.BroadcastToRole(msg, "admin")
		log.Printf("📡 Notificação enviada: Falha SSH de %s", event.ClientName)
	}

	if event.EventType == "SUDO" {
		msg := Message{
			Type: "network_event",
			Data: map[string]interface{}{
				"event":       "sudo_command",
				"client_name": event.ClientName,
				"command":     event.Command,
				"occurred_at": event.OccurredAt,
			},
			Timestamp: time.Now(),
			Source:    "network",
		}

		n.hub.BroadcastToRole(msg, "admin")
		log.Printf("📡 Notificação enviada: Comando sudo de %s", event.ClientName)
	}
}

// NotifyMetricsUpdate notifica sobre atualização de métricas
func (n *Notifier) NotifyMetricsUpdate(metrics map[string]interface{}) {
	msg := Message{
		Type: "metrics_update",
		Data: map[string]interface{}{
			"active_clients":     metrics["active_clients"],
			"events_per_second":  metrics["events_per_second"],
			"error_count":        metrics["error_count"],
			"collection_interval": metrics["collection_interval"],
			"cache_size":         metrics["cache_size"],
		},
		Timestamp: time.Now(),
		Source:    "metrics",
	}

	n.hub.BroadcastMessage(msg)
}

// NotifyAlert notifica sobre alertas críticos
func (n *Notifier) NotifyAlert(title, message, severity string) {
	msg := Message{
		Type: "alert",
		Data: map[string]interface{}{
			"title":    title,
			"message":  message,
			"severity": severity,
		},
		Timestamp: time.Now(),
		Source:    "alert",
	}

	if severity == "CRITICAL" {
		n.hub.BroadcastMessage(msg)
	} else {
		n.hub.BroadcastToRole(msg, "admin")
	}

	log.Printf("🚨 Alerta enviado: %s (%s)", title, severity)
}

// NotifyStatusUpdate notifica sobre mudanças de status
func (n *Notifier) NotifyStatusUpdate(activeClients map[string]*openvpn.StatusEntry) {
	// Preparar dados dos clientes ativos
	clientsData := make([]map[string]interface{}, 0, len(activeClients))
	
	for name, status := range activeClients {
		clientsData = append(clientsData, map[string]interface{}{
			"client_name":      name,
			"real_address":     status.RealAddress,
			"virtual_address":  status.VirtualAddress,
			"bytes_received":   status.BytesReceived,
			"bytes_sent":       status.BytesSent,
			"connected_since":  status.ConnectedSince,
			"last_activity":    status.LastActivity,
			"connection_speed": status.ConnectionSpeed,
		})
	}

	msg := Message{
		Type: "status_update",
		Data: map[string]interface{}{
			"active_clients": clientsData,
			"total_active":   len(activeClients),
		},
		Timestamp: time.Now(),
		Source:    "status",
	}

	n.hub.BroadcastMessage(msg)
}

// NotifyServiceStatus notifica sobre status do serviço
func (n *Notifier) NotifyServiceStatus(status string, details map[string]interface{}) {
	msg := Message{
		Type: "service_status",
		Data: map[string]interface{}{
			"status":  status,
			"details": details,
		},
		Timestamp: time.Now(),
		Source:    "service",
	}

	if status == "critical" || status == "warning" {
		n.hub.BroadcastToRole(msg, "admin")
	} else {
		n.hub.BroadcastMessage(msg)
	}

	log.Printf("📡 Status do serviço notificado: %s", status)
}

// StartPeriodicNotifications inicia notificações periódicas
func (n *Notifier) StartPeriodicNotifications(vpnService *openvpn.OpenVPNService) {
	// Notificar métricas a cada minuto
	metricsTicker := time.NewTicker(1 * time.Minute)
	go func() {
		for range metricsTicker.C {
			metrics := vpnService.GetMetrics()
			n.NotifyMetricsUpdate(metrics)
		}
	}()

	// Notificar status a cada 30 segundos
	statusTicker := time.NewTicker(30 * time.Second)
	go func() {
		for range statusTicker.C {
			activeClients := vpnService.GetActiveClients()
			n.NotifyStatusUpdate(activeClients)
		}
	}()

	log.Println("📡 Notificações periódicas iniciadas")
}

// BroadcastEvent envia um evento genérico via WebSocket
func (n *Notifier) BroadcastEvent(eventType string, data map[string]interface{}) {
	msg := Message{
		Type:      eventType,
		Data:      data,
		Timestamp: time.Now(),
		Source:    "integration",
	}

	n.hub.BroadcastMessage(msg)
	log.Printf("📡 Evento enviado: %s", eventType)
}

// GetHubStats retorna estatísticas do hub WebSocket
func (n *Notifier) GetHubStats() map[string]interface{} {
	return map[string]interface{}{
		"connected_clients": n.hub.GetConnectedClients(),
		"timestamp":        time.Now(),
	}
}
