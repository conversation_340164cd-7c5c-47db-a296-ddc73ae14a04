package middleware

import (
	"fmt"
	"net"
	"net/http"
	"strings"
	"time"

	"vpn-backend/internal/config"

	"github.com/gin-gonic/gin"
	"golang.org/x/time/rate"
)

// SecurityMiddleware middleware de segurança geral
func SecurityMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Headers de segurança
		c.<PERSON>("X-Content-Type-Options", "nosniff")
		c.<PERSON><PERSON>("X-Frame-Options", "DENY")
		c.<PERSON><PERSON>("X-XSS-Protection", "1; mode=block")
		c.<PERSON><PERSON>("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		c.<PERSON><PERSON>("Content-Security-Policy", "default-src 'self'")
		c.<PERSON>("Referrer-Policy", "strict-origin-when-cross-origin")

		c.Next()
	}
}

// VPNIPFilterMiddleware middleware para filtrar apenas IPs da VPN
func VPNIPFilterMiddleware(cfg *config.SecurityConfig) gin.HandlerFunc {
	// Parse das redes permitidas
	var allowedNetworks []*net.IPNet
	for _, cidr := range cfg.AllowedIPs {
		_, network, err := net.ParseCIDR(cidr)
		if err != nil {
			continue
		}
		allowedNetworks = append(allowedNetworks, network)
	}

	return func(c *gin.Context) {
		clientIP := getClientIP(c)

		// Permitir health checks locais (Docker health check)
		if c.Request.URL.Path == "/api/v1/health" {
			if clientIP == "127.0.0.1" || clientIP == "::1" || clientIP == "localhost" {
				c.Next()
				return
			}
		}

		// Permitir endpoints internos com token válido (OpenVPN scripts)
		if strings.HasPrefix(c.Request.URL.Path, "/api/v1/internal/") {
			token := c.GetHeader("X-Internal-Token")
			if token == "vpn_internal_secret_2024" {
				c.Next()
				return
			}
		}

		// Verificar se IP está nas redes permitidas
		allowed := false
		ip := net.ParseIP(clientIP)
		if ip != nil {
			for _, network := range allowedNetworks {
				if network.Contains(ip) {
					allowed = true
					break
				}
			}
		}

		if !allowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Acesso negado: IP não autorizado",
				"ip":    clientIP,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RateLimitMiddleware middleware de rate limiting
func RateLimitMiddleware(requestsPerMinute int) gin.HandlerFunc {
	// Criar limitador de taxa
	limiter := rate.NewLimiter(rate.Every(time.Minute/time.Duration(requestsPerMinute)), requestsPerMinute)

	return func(c *gin.Context) {
		if !limiter.Allow() {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Muitas requisições. Tente novamente mais tarde.",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// CORSMiddleware middleware de CORS
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		
		// Lista de origens permitidas (apenas VPN)
		allowedOrigins := []string{
			"https://********",
			"https://localhost",
			"https://127.0.0.1",
		}

		allowed := false
		for _, allowedOrigin := range allowedOrigins {
			if origin == allowedOrigin {
				allowed = true
				break
			}
		}

		if allowed {
			c.Header("Access-Control-Allow-Origin", origin)
		}

		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// LoggingMiddleware middleware de logging personalizado
func LoggingMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("[VPN-BACKEND] %v | %3d | %13v | %15s | %-7s %#v\n%s",
			param.TimeStamp.Format("2006/01/02 - 15:04:05"),
			param.StatusCode,
			param.Latency,
			param.ClientIP,
			param.Method,
			param.Path,
			param.ErrorMessage,
		)
	})
}

// RequestIDMiddleware adiciona ID único para cada requisição
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := generateRequestID()
		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)
		c.Next()
	}
}

// HealthCheckMiddleware middleware para health check
func HealthCheckMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.URL.Path == "/health" {
			c.JSON(http.StatusOK, gin.H{
				"status":    "healthy",
				"timestamp": time.Now().UTC(),
				"service":   "vpn-backend",
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// getClientIP extrai o IP real do cliente
func getClientIP(c *gin.Context) string {
	// Verificar headers de proxy
	if ip := c.GetHeader("X-Forwarded-For"); ip != "" {
		ips := strings.Split(ip, ",")
		return strings.TrimSpace(ips[0])
	}
	
	if ip := c.GetHeader("X-Real-IP"); ip != "" {
		return ip
	}
	
	if ip := c.GetHeader("X-Client-IP"); ip != "" {
		return ip
	}
	
	// Fallback para IP da conexão
	ip, _, _ := net.SplitHostPort(c.Request.RemoteAddr)
	return ip
}

// generateRequestID gera um ID único para a requisição
func generateRequestID() string {
	// Implementação simples usando timestamp + random
	return fmt.Sprintf("%d-%d", time.Now().UnixNano(), time.Now().Nanosecond()%1000000)
}
