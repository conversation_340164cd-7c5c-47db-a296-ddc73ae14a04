package middleware

import (
	"net/http"
	"strings"

	"vpn-backend/internal/auth"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware middleware de autenticação JWT
func AuthMiddleware(jwtService *auth.JWTService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extrair token do header Authorization
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Token de autorização requerido",
			})
			c.Abort()
			return
		}

		// Verificar formato Bearer
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Formato de token inválido",
			})
			c.Abort()
			return
		}

		token := tokenParts[1]

		// Validar token
		claims, err := jwtService.ValidateToken(token)
		if err != nil {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"error": "Token inválido",
			})
			c.Abort()
			return
		}

		// Adicionar informações do usuário ao contexto
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("email", claims.Email)
		c.Set("role", claims.Role)

		c.Next()
	}
}

// AdminMiddleware middleware para verificar se o usuário é admin
func AdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("role")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Informações de usuário não encontradas",
			})
			c.Abort()
			return
		}

		if role != "admin" {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Acesso negado: privilégios de administrador requeridos",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// OptionalAuthMiddleware middleware de autenticação opcional
func OptionalAuthMiddleware(jwtService *auth.JWTService) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.Next()
			return
		}

		token := tokenParts[1]
		claims, err := jwtService.ValidateToken(token)
		if err != nil {
			c.Next()
			return
		}

		// Adicionar informações do usuário ao contexto se token válido
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("email", claims.Email)
		c.Set("role", claims.Role)

		c.Next()
	}
}

// InternalAuthMiddleware middleware de autenticação para endpoints internos (OpenVPN)
func InternalAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Verificar token interno no header X-Internal-Token
		token := c.GetHeader("X-Internal-Token")
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Token interno requerido",
				"code":  "INTERNAL_TOKEN_MISSING",
			})
			c.Abort()
			return
		}

		// Verificar se o token é válido (token fixo para OpenVPN)
		expectedToken := "vpn_internal_secret_2024"
		if token != expectedToken {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Token interno inválido",
				"code":  "INTERNAL_TOKEN_INVALID",
			})
			c.Abort()
			return
		}

		// Marcar como requisição interna
		c.Set("internal_request", true)
		c.Set("source", "openvpn")

		c.Next()
	}
}
