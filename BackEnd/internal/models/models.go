package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// User representa um usuário do sistema
type User struct {
	ID        uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	Username  string    `gorm:"uniqueIndex;not null" json:"username"`
	Email     string    `gorm:"uniqueIndex;not null" json:"email"`
	Password  string    `gorm:"not null" json:"-"`
	Role      string    `gorm:"default:'user'" json:"role"`
	Active    bool      `gorm:"default:true" json:"active"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// VPNClient representa um cliente VPN
type VPNClient struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	Name        string    `gorm:"uniqueIndex;not null" json:"name"`
	Email       string    `gorm:"uniqueIndex" json:"email"`
	Certificate string    `gorm:"type:text" json:"certificate,omitempty"`
	PrivateKey  string    `gorm:"type:text" json:"-"`
	Status      string    `gorm:"default:'active'" json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	
	// Relacionamentos (removido para evitar referência circular na migração)
	// Sessions []VPNSession `gorm:"foreignKey:ClientName;references:Name" json:"sessions,omitempty"`
}

// VPNSession representa uma sessão de conexão VPN
type VPNSession struct {
	ID               uuid.UUID  `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	ClientName       string     `gorm:"index;not null" json:"client_name"`
	RealIP           string     `gorm:"index" json:"real_ip"`
	VirtualIP        string     `gorm:"index" json:"virtual_ip"`
	ConnectedAt      time.Time  `gorm:"index" json:"connected_at"`
	DisconnectedAt   *time.Time `gorm:"index" json:"disconnected_at,omitempty"`
	BytesReceived    int64      `gorm:"default:0" json:"bytes_received"`
	BytesSent        int64      `gorm:"default:0" json:"bytes_sent"`
	Status           string     `gorm:"default:'active';index" json:"status"`
	DisconnectReason string     `json:"disconnect_reason,omitempty"`
	Duration         int64      `json:"duration,omitempty"` // em segundos
	CreatedAt        time.Time  `json:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at"`
	
	// Relacionamentos (removido para evitar referência circular na migração)
	// Client       *VPNClient     `gorm:"foreignKey:ClientName;references:Name" json:"client,omitempty"`
	// NetworkEvents []NetworkEvent `gorm:"foreignKey:ClientName;references:ClientName" json:"network_events,omitempty"`
}

// SystemEvent representa eventos do sistema
type SystemEvent struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	EventType   string    `gorm:"index;not null" json:"event_type"`
	Severity    string    `gorm:"index;default:'info'" json:"severity"`
	Title       string    `gorm:"not null" json:"title"`
	Message     string    `gorm:"type:text" json:"message"`
	Source      string    `gorm:"index" json:"source"`
	ClientName  string    `gorm:"index" json:"client_name,omitempty"`
	SourceIP    string    `gorm:"index" json:"source_ip,omitempty"`
	OccurredAt  time.Time `gorm:"index" json:"occurred_at"`
	Status      string    `gorm:"default:'active';index" json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// NetworkEvent representa eventos de rede detectados
type NetworkEvent struct {
	ID            uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	EventType     string    `gorm:"index;not null" json:"event_type"`
	SourceIP      string    `gorm:"index" json:"source_ip"`
	DestinationIP string    `gorm:"index" json:"destination_ip"`
	Port          int       `gorm:"index" json:"port"`
	Protocol      string    `gorm:"index" json:"protocol"`
	OccurredAt    time.Time `gorm:"index" json:"occurred_at"`
	ClientName    string    `gorm:"index" json:"client_name"`
	Success       bool      `gorm:"index" json:"success"`
	Command       string    `gorm:"type:text" json:"command,omitempty"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	
	// Relacionamentos (removido para evitar referência circular na migração)
	// Session *VPNSession `gorm:"foreignKey:ClientName;references:ClientName" json:"session,omitempty"`
}

// ConnectionMetrics representa métricas de conexão
type ConnectionMetrics struct {
	ID                uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	Timestamp         time.Time `gorm:"index" json:"timestamp"`
	ActiveConnections int       `json:"active_connections"`
	TotalConnections  int       `json:"total_connections"`
	TotalBytesIn      int64     `json:"total_bytes_in"`
	TotalBytesOut     int64     `json:"total_bytes_out"`
	AvgSessionTime    float64   `json:"avg_session_time"`
	CreatedAt         time.Time `json:"created_at"`
}

// PerformanceMetrics representa métricas de performance do sistema
type PerformanceMetrics struct {
	ID                uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	Timestamp         time.Time `gorm:"index" json:"timestamp"`
	EventsProcessed   int64     `json:"events_processed"`
	EventsPerSecond   float64   `json:"events_per_second"`
	ErrorCount        int64     `json:"error_count"`
	ProcessingLatency float64   `json:"processing_latency"` // em milissegundos
	CacheSize         int       `json:"cache_size"`
	ActiveWorkers     int       `json:"active_workers"`
	CPUUsage          float64   `json:"cpu_usage"`
	MemoryUsage       float64   `json:"memory_usage"`
	CreatedAt         time.Time `json:"created_at"`
}

// BeforeCreate hook para definir ID antes de criar
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.ID == uuid.Nil {
		u.ID = uuid.New()
	}
	return nil
}

func (c *VPNClient) BeforeCreate(tx *gorm.DB) error {
	if c.ID == uuid.Nil {
		c.ID = uuid.New()
	}
	return nil
}

func (s *VPNSession) BeforeCreate(tx *gorm.DB) error {
	if s.ID == uuid.Nil {
		s.ID = uuid.New()
	}
	return nil
}

func (e *SystemEvent) BeforeCreate(tx *gorm.DB) error {
	if e.ID == uuid.Nil {
		e.ID = uuid.New()
	}
	return nil
}

func (n *NetworkEvent) BeforeCreate(tx *gorm.DB) error {
	if n.ID == uuid.Nil {
		n.ID = uuid.New()
	}
	return nil
}

func (m *ConnectionMetrics) BeforeCreate(tx *gorm.DB) error {
	if m.ID == uuid.Nil {
		m.ID = uuid.New()
	}
	return nil
}

func (p *PerformanceMetrics) BeforeCreate(tx *gorm.DB) error {
	if p.ID == uuid.Nil {
		p.ID = uuid.New()
	}
	return nil
}

// AfterUpdate hook para calcular duração da sessão
func (s *VPNSession) AfterUpdate(tx *gorm.DB) error {
	if s.DisconnectedAt != nil && s.Status == "disconnected" {
		s.Duration = int64(s.DisconnectedAt.Sub(s.ConnectedAt).Seconds())
		tx.Model(s).UpdateColumn("duration", s.Duration)
	}
	return nil
}

// Métodos auxiliares
func (s *VPNSession) IsActive() bool {
	return s.Status == "active" && s.DisconnectedAt == nil
}

func (s *VPNSession) GetDuration() time.Duration {
	if s.DisconnectedAt != nil {
		return s.DisconnectedAt.Sub(s.ConnectedAt)
	}
	return time.Since(s.ConnectedAt)
}

func (e *SystemEvent) IsActive() bool {
	return e.Status == "active"
}

func (n *NetworkEvent) IsSuccessful() bool {
	return n.Success
}

// VPNConnection representa uma conexão VPN ativa (para integração Fase 5)
type VPNConnection struct {
	ID             uuid.UUID  `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	ClientName     string     `gorm:"index;not null" json:"client_name"`
	ExternalIP     string     `gorm:"index" json:"external_ip"`
	VpnIP          string     `gorm:"index" json:"vpn_ip"`
	ConnectedAt    time.Time  `gorm:"index" json:"connected_at"`
	DisconnectedAt *time.Time `gorm:"index" json:"disconnected_at,omitempty"`
	Status         string     `gorm:"default:'connected';index" json:"status"`
	BytesReceived  int64      `gorm:"default:0" json:"bytes_received"`
	BytesSent      int64      `gorm:"default:0" json:"bytes_sent"`
	Duration       int        `json:"duration,omitempty"` // em segundos
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`
}

// RegisteredDevice representa um dispositivo registrado via scripts de setup
type RegisteredDevice struct {
	ID           uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	DeviceType   string    `gorm:"index;not null" json:"device_type"` // raspberry, windows, linux
	Hostname     string    `gorm:"index;not null" json:"hostname"`
	LocalIP      string    `gorm:"index" json:"local_ip"`
	MacAddress   string    `gorm:"index" json:"mac_address"`
	OSVersion    string    `json:"os_version"`
	SetupScript  string    `json:"setup_script"`
	RegisteredAt time.Time `gorm:"index" json:"registered_at"`
	LastSeen     *time.Time `gorm:"index" json:"last_seen,omitempty"`
	Status       string    `gorm:"default:'active';index" json:"status"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// DeviceHeartbeat representa um heartbeat de dispositivo
type DeviceHeartbeat struct {
	ID           uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	DeviceID     string    `gorm:"index;not null" json:"device_id"`
	VpnStatus    string    `gorm:"index" json:"vpn_status"`
	VpnIP        string    `gorm:"index" json:"vpn_ip,omitempty"`
	LocalIP      string    `gorm:"index" json:"local_ip"`
	Uptime       string    `json:"uptime"`
	LoadAverage  string    `json:"load_average"`
	ReceivedAt   time.Time `gorm:"index" json:"received_at"`
	CreatedAt    time.Time `json:"created_at"`
}

// BeforeCreate hooks para novos modelos
func (c *VPNConnection) BeforeCreate(tx *gorm.DB) error {
	if c.ID == uuid.Nil {
		c.ID = uuid.New()
	}
	return nil
}

func (d *RegisteredDevice) BeforeCreate(tx *gorm.DB) error {
	if d.ID == uuid.Nil {
		d.ID = uuid.New()
	}
	return nil
}

func (h *DeviceHeartbeat) BeforeCreate(tx *gorm.DB) error {
	if h.ID == uuid.Nil {
		h.ID = uuid.New()
	}
	return nil
}

// Métodos auxiliares para VPNConnection
func (c *VPNConnection) IsActive() bool {
	return c.Status == "connected" && c.DisconnectedAt == nil
}

func (c *VPNConnection) GetDuration() time.Duration {
	if c.DisconnectedAt != nil {
		return c.DisconnectedAt.Sub(c.ConnectedAt)
	}
	return time.Since(c.ConnectedAt)
}

// Constantes para tipos de eventos
const (
	// Tipos de eventos do sistema
	EventTypeConnect     = "CONNECT"
	EventTypeDisconnect  = "DISCONNECT"
	EventTypeAuth        = "AUTH"
	EventTypeAuthFail    = "AUTH_FAIL"
	EventTypeReboot      = "REBOOT"
	EventTypeServiceOp   = "SERVICE"

	// Tipos de eventos de rede
	EventTypeSSH         = "SSH"
	EventTypeSudo        = "SUDO"
	EventTypeHTTP        = "HTTP"
	EventTypeFTP         = "FTP"
	EventTypeVNC         = "VNC"

	// Severidades
	SeverityInfo         = "INFO"
	SeverityWarning      = "WARNING"
	SeverityError        = "ERROR"
	SeverityCritical     = "CRITICAL"

	// Status
	StatusActive         = "active"
	StatusInactive       = "inactive"
	StatusDisconnected   = "disconnected"
	StatusBlocked        = "blocked"
	StatusConnected      = "connected"
)
