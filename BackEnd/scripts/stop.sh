#!/bin/bash

# Script para parar o VPN Backend Server

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🛑 Parando VPN Backend Server..."

# Encontrar processo do servidor
VPN_BACKEND_PID=$(pgrep -f "vpn-backend" || true)

if [[ -n "$VPN_BACKEND_PID" ]]; then
    log_info "Encontrado processo VPN Backend com PID: $VPN_BACKEND_PID"
    
    # Tentar parar graciosamente
    log_info "Enviando SIGTERM para parada graciosa..."
    kill -TERM $VPN_BACKEND_PID
    
    # Aguardar até 30 segundos para parada graciosa
    for i in {1..30}; do
        if ! kill -0 $VPN_BACKEND_PID 2>/dev/null; then
            log_success "Servidor parado graciosamente"
            break
        fi
        sleep 1
        if [[ $i -eq 30 ]]; then
            log_warning "Timeout na parada graciosa, forçando parada..."
            kill -KILL $VPN_BACKEND_PID 2>/dev/null || true
            sleep 2
            if ! kill -0 $VPN_BACKEND_PID 2>/dev/null; then
                log_success "Servidor forçado a parar"
            else
                log_error "Falha ao parar servidor"
                exit 1
            fi
        fi
    done
else
    log_warning "Nenhum processo VPN Backend encontrado"
fi

# Verificar se ainda há processos relacionados
REMAINING_PROCESSES=$(pgrep -f "vpn-backend" || true)
if [[ -n "$REMAINING_PROCESSES" ]]; then
    log_warning "Ainda há processos relacionados rodando:"
    ps aux | grep vpn-backend | grep -v grep
    log_info "Para forçar a parada de todos os processos, execute:"
    log_info "pkill -f vpn-backend"
else
    log_success "Todos os processos VPN Backend foram parados"
fi

echo ""
log_info "Status dos serviços relacionados:"
echo "  PostgreSQL: $(systemctl is-active postgresql 2>/dev/null || echo 'não disponível')"
echo "  OpenVPN: $(systemctl is-active openvpn@server 2>/dev/null || echo 'não disponível')"
echo "  VPN Backend: Parado"

log_success "VPN Backend Server parado com sucesso"
