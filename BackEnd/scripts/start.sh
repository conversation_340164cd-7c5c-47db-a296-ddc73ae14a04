#!/bin/bash

# Script de inicialização do VPN Backend
# Baseado na implementação da Fase 4

set -e

echo "🚀 Iniciando VPN Backend Server..."

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar se está rodando como root (necessário para acessar logs do sistema)
if [[ $EUID -ne 0 ]]; then
   log_error "Este script deve ser executado como root para acessar logs do sistema"
   exit 1
fi

# Diretório do projeto
PROJECT_DIR="/root/VPN/BackEnd"
cd "$PROJECT_DIR"

# Verificar se PostgreSQL está rodando
log_info "Verificando PostgreSQL..."
if ! systemctl is-active --quiet postgresql; then
    log_warning "PostgreSQL não está rodando. Tentando iniciar..."
    systemctl start postgresql
    sleep 2
    if ! systemctl is-active --quiet postgresql; then
        log_error "Falha ao iniciar PostgreSQL"
        exit 1
    fi
fi
log_success "PostgreSQL está rodando"

# Verificar se OpenVPN está rodando
log_info "Verificando OpenVPN..."
if ! systemctl is-active --quiet openvpn@server; then
    log_warning "OpenVPN não está rodando. Tentando iniciar..."
    systemctl start openvpn@server
    sleep 2
    if ! systemctl is-active --quiet openvpn@server; then
        log_error "Falha ao iniciar OpenVPN"
        exit 1
    fi
fi
log_success "OpenVPN está rodando"

# Verificar se arquivos de log existem
log_info "Verificando arquivos de log..."

OPENVPN_LOG="/root/VPN/OpenVPN/logs/openvpn.log"
OPENVPN_STATUS="/root/VPN/OpenVPN/logs/status.log"

if [[ ! -f "$OPENVPN_LOG" ]]; then
    log_warning "Arquivo de log OpenVPN não encontrado: $OPENVPN_LOG"
    mkdir -p "$(dirname "$OPENVPN_LOG")"
    touch "$OPENVPN_LOG"
    log_info "Arquivo de log criado: $OPENVPN_LOG"
fi

if [[ ! -f "$OPENVPN_STATUS" ]]; then
    log_warning "Arquivo de status OpenVPN não encontrado: $OPENVPN_STATUS"
    mkdir -p "$(dirname "$OPENVPN_STATUS")"
    touch "$OPENVPN_STATUS"
    log_info "Arquivo de status criado: $OPENVPN_STATUS"
fi

# Verificar permissões dos arquivos de log do sistema
log_info "Verificando permissões dos logs do sistema..."
chmod 644 /var/log/syslog 2>/dev/null || log_warning "Não foi possível ajustar permissões do syslog"
chmod 644 /var/log/auth.log 2>/dev/null || log_warning "Não foi possível ajustar permissões do auth.log"

# Verificar se arquivo .env existe
if [[ ! -f "configs/.env" ]]; then
    log_error "Arquivo .env não encontrado em configs/.env"
    log_info "Criando arquivo .env de exemplo..."
    
    cat > configs/.env << EOF
# Configurações do Backend Go - VPN Monitoring
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
GIN_MODE=release

# Banco de Dados PostgreSQL
DB_HOST=localhost
DB_PORT=5432
DB_USER=vpn_user
DB_PASSWORD=vpn_secure_password_2024
DB_NAME=vpn_monitoring
DB_SSLMODE=disable

# JWT
JWT_SECRET=vpn_jwt_super_secret_key_2024_secure_token
JWT_EXPIRY=24h

# OpenVPN Logs
OPENVPN_LOG_FILE=/root/VPN/OpenVPN/logs/openvpn.log
OPENVPN_STATUS_FILE=/root/VPN/OpenVPN/logs/status.log
OPENVPN_CONFIG_DIR=/root/VPN/OpenVPN

# Sistema de Logs
SYSTEM_LOG_FILE=/var/log/syslog
AUTH_LOG_FILE=/var/log/auth.log
KERN_LOG_FILE=/var/log/kern.log

# Performance
COLLECTION_INTERVAL=30s
BATCH_SIZE=100
MAX_CACHE_SIZE=1000
WORKER_POOL_SIZE=10

# Segurança VPN
VPN_NETWORK=********/24
ALLOWED_IPS=********/24,127.0.0.1/32
RATE_LIMIT=1000

# WebSocket
WS_READ_BUFFER=1024
WS_WRITE_BUFFER=1024
WS_MAX_CONNECTIONS=100

# Monitoramento
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=30s
LOG_LEVEL=info
EOF
    
    log_warning "Arquivo .env criado. Ajuste as configurações conforme necessário."
fi

# Compilar aplicação
log_info "Compilando aplicação Go..."
if ! go build -o bin/vpn-backend cmd/server/main.go; then
    log_error "Falha ao compilar aplicação"
    exit 1
fi
log_success "Aplicação compilada com sucesso"

# Criar diretório de logs se não existir
mkdir -p logs

# Função para parar o servidor
cleanup() {
    log_info "Parando servidor..."
    if [[ -n $SERVER_PID ]]; then
        kill $SERVER_PID 2>/dev/null || true
        wait $SERVER_PID 2>/dev/null || true
    fi
    log_success "Servidor parado"
    exit 0
}

# Capturar sinais para cleanup
trap cleanup SIGINT SIGTERM

# Iniciar servidor
log_info "Iniciando servidor VPN Backend..."
./bin/vpn-backend > logs/server.log 2>&1 &
SERVER_PID=$!

# Aguardar um pouco para verificar se o servidor iniciou
sleep 3

if ! kill -0 $SERVER_PID 2>/dev/null; then
    log_error "Falha ao iniciar servidor. Verificar logs/server.log"
    cat logs/server.log
    exit 1
fi

log_success "Servidor VPN Backend iniciado com PID $SERVER_PID"
log_info "Logs disponíveis em: logs/server.log"
log_info "API disponível em: http://localhost:8080/api/v1"
log_info "Health check: http://localhost:8080/api/v1/health"

# Mostrar status dos serviços
echo ""
log_info "Status dos serviços:"
echo "  PostgreSQL: $(systemctl is-active postgresql)"
echo "  OpenVPN: $(systemctl is-active openvpn@server)"
echo "  VPN Backend: Rodando (PID $SERVER_PID)"

echo ""
log_info "Para parar o servidor, pressione Ctrl+C"

# Aguardar indefinidamente
wait $SERVER_PID
