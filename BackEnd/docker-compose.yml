version: "3.7"

services:
  vpn-backend:
    image: vpn-backend:latest
    networks:
      - redeinterna
    environment:
      # Servidor
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8080
      - GIN_MODE=release
      
      # Banco de Dados PostgreSQL (usar serviço existente)
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=vpn_user
      - DB_PASSWORD=vpn_secure_password_2024
      - DB_NAME=vpn_monitoring
      - DB_SSLMODE=disable
      
      # JWT
      - JWT_SECRET=vpn_jwt_super_secret_key_2024_secure_token_production
      - JWT_EXPIRY=24h
      
      # OpenVPN Logs (volumes montados)
      - OPENVPN_LOG_FILE=/openvpn/logs/openvpn.log
      - OPENVPN_STATUS_FILE=/openvpn/logs/status.log
      - OPENVPN_CONFIG_DIR=/openvpn
      
      # Sistema de Logs (volumes montados)
      - SYSTEM_LOG_FILE=/var/log/syslog
      - AUTH_LOG_FILE=/var/log/auth.log
      - KERN_LOG_FILE=/var/log/kern.log
      
      # Performance
      - COLLECTION_INTERVAL=30s
      - BATCH_SIZE=100
      - MAX_CACHE_SIZE=1000
      - WORKER_POOL_SIZE=10
      
      # Segurança VPN
      - VPN_NETWORK=*********/24
      - ALLOWED_IPS=*********/24,127.0.0.1/32,**********/12
      - RATE_LIMIT=1000
      
      # WebSocket
      - WS_READ_BUFFER=1024
      - WS_WRITE_BUFFER=1024
      - WS_MAX_CONNECTIONS=100
      
      # Monitoramento
      - METRICS_ENABLED=true
      - HEALTH_CHECK_INTERVAL=30s
      - LOG_LEVEL=info
    
    volumes:
      # Logs OpenVPN
      - /root/VPN/OpenVPN/logs:/openvpn/logs:ro
      - /root/VPN/OpenVPN:/openvpn:ro
      
      # Logs do sistema (somente leitura)
      - /var/log/syslog:/var/log/syslog:ro
      - /var/log/auth.log:/var/log/auth.log:ro
      - /var/log/kern.log:/var/log/kern.log:ro
      
      # Logs da aplicação
      - vpn-backend-logs:/app/logs
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    deploy:
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
      labels:
        # Traefik configuration
        - "traefik.enable=true"
        
        # API routes
        - "traefik.http.routers.vpn-backend-api.rule=Host(`vpn.evo-eden.site`) && PathPrefix(`/api`)"
        - "traefik.http.routers.vpn-backend-api.entrypoints=web"
        - "traefik.http.routers.vpn-backend-api.middlewares=vpn-backend-redirect"
        
        - "traefik.http.routers.vpn-backend-api-secure.rule=Host(`vpn.evo-eden.site`) && PathPrefix(`/api`)"
        - "traefik.http.routers.vpn-backend-api-secure.entrypoints=websecure"
        - "traefik.http.routers.vpn-backend-api-secure.tls=true"
        - "traefik.http.routers.vpn-backend-api-secure.tls.certresolver=letsencryptresolver"
        - "traefik.http.routers.vpn-backend-api-secure.middlewares=vpn-backend-headers"
        
        # WebSocket routes
        - "traefik.http.routers.vpn-backend-ws.rule=Host(`vpn.evo-eden.site`) && PathPrefix(`/ws`)"
        - "traefik.http.routers.vpn-backend-ws.entrypoints=web"
        - "traefik.http.routers.vpn-backend-ws.middlewares=vpn-backend-redirect"
        
        - "traefik.http.routers.vpn-backend-ws-secure.rule=Host(`vpn.evo-eden.site`) && PathPrefix(`/ws`)"
        - "traefik.http.routers.vpn-backend-ws-secure.entrypoints=websecure"
        - "traefik.http.routers.vpn-backend-ws-secure.tls=true"
        - "traefik.http.routers.vpn-backend-ws-secure.tls.certresolver=letsencryptresolver"
        - "traefik.http.routers.vpn-backend-ws-secure.middlewares=vpn-backend-headers"
        
        # Middlewares
        - "traefik.http.middlewares.vpn-backend-redirect.redirectscheme.scheme=https"
        - "traefik.http.middlewares.vpn-backend-redirect.redirectscheme.permanent=true"
        
        - "traefik.http.middlewares.vpn-backend-headers.headers.frameDeny=true"
        - "traefik.http.middlewares.vpn-backend-headers.headers.sslRedirect=true"
        - "traefik.http.middlewares.vpn-backend-headers.headers.browserXssFilter=true"
        - "traefik.http.middlewares.vpn-backend-headers.headers.contentTypeNosniff=true"
        - "traefik.http.middlewares.vpn-backend-headers.headers.forceSTSHeader=true"
        - "traefik.http.middlewares.vpn-backend-headers.headers.stsSeconds=31536000"
        - "traefik.http.middlewares.vpn-backend-headers.headers.stsIncludeSubdomains=true"
        - "traefik.http.middlewares.vpn-backend-headers.headers.stsPreload=true"
        - "traefik.http.middlewares.vpn-backend-headers.headers.accessControlAllowOriginList=https://vpn.evo-eden.site"
        - "traefik.http.middlewares.vpn-backend-headers.headers.accessControlAllowMethods=GET,POST,PUT,DELETE,OPTIONS"
        - "traefik.http.middlewares.vpn-backend-headers.headers.accessControlAllowHeaders=Origin,Content-Type,Accept,Authorization"
        
        # Service configuration
        - "traefik.http.services.vpn-backend.loadbalancer.server.port=8080"

volumes:
  vpn-backend-logs:
    driver: local

networks:
  redeinterna:
    external: true
    name: redeinterna
