# Implementação OpenVPN - Fase 1: VPN Básica

## 📋 Visão Geral

Este documento detalha a **Fase 1** da implementação de uma VPN corporativa usando OpenVPN para conectar máquinas Windows e Raspberry Pi através de uma VPS Ubuntu 24.04.

### 🚀 Divisão em Fases

**📍 FASE 1 (Este documento)**: Implementação VPN Básica
- Servidor OpenVPN funcional
- Scripts de criação de clientes
- Conectividade entre máquinas
- Monitoramento básico

**📍 FASE 2 (Futuro)**: Sistema de Controle PostgreSQL
- Banco de dados para histórico
- Dashboard Node.js avançado
- Métricas detalhadas

**📍 FASE 3 (Futuro)**: Restrição de Acesso Web
- Integração com monitor.evo-eden.site
- Controle de acesso por IP VPN
- Middleware Traefik

### Informações do Servidor
- **SO**: Ubuntu 24.04.2 LTS (Noble Numbat)
- **Domínio**: vpn.evo-eden.site (Cloudflare Proxy)
- **IP Público**: ************ (backend)
- **Arquitetura**: x86_64
- **Localização**: /root/VPN/OpenVPN

### ⚠️ Considerações Importantes de Rede
- **Rede VPN Escolhida**: *********/24 (evita conflito com 10.8.0 e 10.10.0 já em uso)
- **Estrutura de Arquivos**: Todos os arquivos OpenVPN organizados em `/root/VPN/OpenVPN/`
- **Segregação de Rede**: Esta VPN é independente das outras VPNs existentes

### 🌐 Configuração Cloudflare
- **Domínio**: vpn.evo-eden.site
- **Proxy**: Ativado (Orange Cloud)
- **Portas Suportadas**:
  - ✅ **8080** (HTTP) - API VPN funcionará normalmente
  - ✅ **1194** (UDP) - OpenVPN funcionará normalmente (Cloudflare não faz proxy UDP)
- **SSL/TLS**: Flexible ou Full (recomendado Full)
- **Considerações**:
  - API HTTP na porta 8080 passará pelo proxy Cloudflare
  - Conexões OpenVPN UDP na porta 1194 irão direto para o servidor
  - Logs do Cloudflare mostrarão requisições da API

## 🎯 Objetivos da Fase 1

1. ✅ Criar servidor OpenVPN centralizado na VPS
2. ✅ Permitir conexão segura de clientes Windows
3. ✅ Permitir conexão segura de Raspberry Pi (Linux)
4. ✅ Implementar comunicação direta entre clientes (sem NAT para internet)
5. ✅ Configurar acesso SSH e VNC (porta 9145) através da VPN
6. ✅ Scripts automatizados para criação de clientes
7. ✅ Monitoramento básico e logs
8. ✅ Evitar conflitos com redes VPN existentes (10.8.0 e 10.10.0)

**IMPORTANTE**: Esta configuração NÃO redireciona tráfego de internet através da VPN. O objetivo é apenas conectividade interna entre suas máquinas.

## 🏗️ Arquitetura da Solução - Fase 1

```text
VPS Ubuntu (Servidor OpenVPN) - FASE 1
├── Domínio: vpn.evo-eden.site (Cloudflare Proxy)
├── IP Backend: ************
├── Rede VPN Interna: *********/24 (NOVA REDE - sem conflitos)
├── IP VPN Servidor: *********
└── Porta OpenVPN: 1194/UDP

Estrutura de Diretórios Organizada:
/root/VPN/
└── OpenVPN/
    ├── easy-rsa/          # Certificados e PKI
    ├── clients/           # Arquivos .ovpn dos clientes
    ├── scripts/           # Scripts de automação
    ├── logs/              # Logs da VPN
    └── configs/           # Configurações do servidor

Clientes VPN (Fase 1):
├── Windows Machines
│   ├── IPs VPN: *********0-50
│   ├── Acesso: SSH (22) + VNC (9145)
│   └── Script PowerShell para configuração
│
└── Raspberry Pi / Linux
    ├── IPs VPN: *********00-150
    ├── Acesso: SSH (22) + VNC (9145)
    └── Script Bash para configuração

Funcionalidades da Fase 1:
✅ Comunicação direta entre todas as máquinas via VPN
✅ Sem redirecionamento de tráfego de internet
✅ Acesso remoto via SSH e VNC através da VPN
✅ Scripts automatizados para criação de clientes
✅ Monitoramento básico com logs
✅ Isolamento completo das outras VPNs (10.8.0 e 10.10.0)
✅ Senha padrão "etenopenvpn" para controle de acesso
✅ Nomes únicos obrigatórios para cada dispositivo
```

## 🔧 Etapa 1: Preparação do Servidor

### 1.1 Criação da Estrutura de Diretórios
```bash
# Criar estrutura organizada para a VPN
mkdir -p /root/VPN/OpenVPN/{easy-rsa,clients,scripts,logs,configs}
cd /root/VPN/OpenVPN
```

### 1.2 Atualização do Sistema
```bash
apt update && apt upgrade -y
apt install -y openvpn easy-rsa iptables-persistent curl wget
```

### 1.3 Configuração de Firewall

```bash
# Permitir tráfego OpenVPN e SSH
ufw allow 1194/udp
ufw allow ssh
ufw allow 9145/tcp  # Porta VNC
ufw --force enable

# Habilitar roteamento apenas para comunicação interna VPN
echo 'net.ipv4.ip_forward=1' >> /etc/sysctl.conf
sysctl -p
```

## 🔐 Etapa 2: Configuração PKI (Infraestrutura de Chaves)

### 2.1 Inicialização do Easy-RSA
```bash
cd /root/VPN/OpenVPN
cp -r /usr/share/easy-rsa .
cd easy-rsa
```

### 2.2 Configuração de Variáveis
```bash
cat > vars << 'EOF'
export KEY_COUNTRY="BR"
export KEY_PROVINCE="PE"
export KEY_CITY="IGARASSU"
export KEY_ORG="EVOLUTION"
export KEY_EMAIL="<EMAIL>"
export KEY_OU="MONITORAMENTO"
export KEY_NAME="EvoEden-CA"
EOF

source vars
./clean-all
```

### 2.3 Criação da Autoridade Certificadora
```bash
./build-ca
./build-key-server server
./build-dh
openvpn --genkey --secret keys/ta.key
```

## ⚙️ Etapa 3: Configuração do Servidor OpenVPN

### 3.1 Arquivo de Configuração Principal

```bash
cat > /root/VPN/OpenVPN/configs/server.conf << 'EOF'
# Configuração do Servidor OpenVPN - Rede Interna Apenas
# Rede: *********/24 (evita conflito com 10.8.0 e 10.10.0)
port 1194
proto udp
dev tun

# Certificados e chaves (organizados na estrutura OpenVPN)
ca /root/VPN/OpenVPN/easy-rsa/keys/ca.crt
cert /root/VPN/OpenVPN/easy-rsa/keys/server.crt
key /root/VPN/OpenVPN/easy-rsa/keys/server.key
dh /root/VPN/OpenVPN/easy-rsa/keys/dh2048.pem
tls-auth /root/VPN/OpenVPN/easy-rsa/keys/ta.key 0

# Rede VPN - Nova rede *********/24 para evitar conflitos
server ********* *************
ifconfig-pool-persist /root/VPN/OpenVPN/logs/ipp.txt

# IMPORTANTE: SEM redirecionamento de gateway para internet
# push "redirect-gateway def1 bypass-dhcp"  # REMOVIDO
# push "dhcp-option DNS *******"           # REMOVIDO
# push "dhcp-option DNS *******"           # REMOVIDO

# Permitir comunicação entre clientes (essencial para VNC/SSH)
client-to-client

# Configurações de conexão
keepalive 10 120
comp-lzo
persist-key
persist-tun

# Segurança
user nobody
group nogroup

# Logs organizados na estrutura OpenVPN
status /root/VPN/OpenVPN/logs/openvpn-status.log
log-append /root/VPN/OpenVPN/logs/openvpn.log
verb 3
mute 20

# Configurações específicas para acesso remoto
# Permitir múltiplas conexões do mesmo certificado (opcional)
# duplicate-cn
EOF

# Criar link simbólico para o diretório padrão do OpenVPN
ln -sf /root/VPN/OpenVPN/configs/server.conf /etc/openvpn/server.conf
```

### 3.2 Configuração de Roteamento (Apenas Comunicação Interna)

```bash
# IMPORTANTE: Configuração APENAS para comunicação interna VPN
# SEM NAT para internet - apenas roteamento entre clientes VPN
# Rede: *********/24 (nova rede sem conflitos)

# Permitir tráfego entre clientes VPN na nova rede
iptables -A FORWARD -i tun0 -o tun0 -j ACCEPT
iptables -A FORWARD -s *********/24 -d *********/24 -j ACCEPT

# Permitir tráfego VPN para portas específicas (SSH e VNC)
iptables -A INPUT -i tun0 -p tcp --dport 22 -j ACCEPT   # SSH
iptables -A INPUT -i tun0 -p tcp --dport 9145 -j ACCEPT # VNC
iptables -A INPUT -s *********/24 -p tcp --dport 22 -j ACCEPT   # SSH da rede VPN
iptables -A INPUT -s *********/24 -p tcp --dport 9145 -j ACCEPT # VNC da rede VPN

# Salvar regras
iptables-save > /etc/iptables/rules.v4

echo "ATENÇÃO: Esta configuração NÃO permite acesso à internet via VPN"
echo "Apenas comunicação interna entre clientes VPN na rede *********/24"
echo "Esta rede é independente das VPNs 10.8.0 e 10.10.0 existentes"
```

## 👥 Etapa 4: Criação de Certificados para Clientes

### 4.1 Script Interativo de Geração de Clientes (Administrador) - Fase 1

```bash
cat > /root/VPN/OpenVPN/scripts/create-client-interactive.sh << 'EOF'
#!/bin/bash

# Script Interativo para Criação de Clientes VPN - Administrador
# Versão: Fase 1 (VPN Básica)

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Função para exibir mensagens coloridas
print_message() {
    local color="$1"
    local message="$2"
    echo -e "${color}${message}${NC}"
}

# Função para verificar pré-requisitos
check_prerequisites() {
    print_message "$BLUE" "🔍 Verificando pré-requisitos..."

    # Verificar se está executando como root
    if [ "$EUID" -ne 0 ]; then
        print_message "$RED" "❌ Este script deve ser executado como root!"
        print_message "$YELLOW" "💡 Execute: sudo $0"
        exit 1
    fi

    # Verificar se easy-rsa existe
    if [ ! -d "/root/VPN/OpenVPN/easy-rsa" ]; then
        print_message "$RED" "❌ Diretório easy-rsa não encontrado!"
        print_message "$YELLOW" "💡 Execute primeiro as etapas 1 e 2 da implementação"
        exit 1
    fi

    # Verificar se CA existe
    if [ ! -f "/root/VPN/OpenVPN/easy-rsa/keys/ca.crt" ]; then
        print_message "$RED" "❌ Certificado CA não encontrado!"
        print_message "$YELLOW" "💡 Execute primeiro a configuração PKI (Etapa 2)"
        exit 1
    fi

    # Verificar se OpenVPN está rodando
    if ! systemctl is-active --quiet openvpn@server; then
        print_message "$YELLOW" "⚠️ Servidor OpenVPN não está rodando"
        print_message "$BLUE" "🔄 Iniciando servidor OpenVPN..."
        systemctl start openvpn@server
        sleep 2

        if systemctl is-active --quiet openvpn@server; then
            print_message "$GREEN" "✅ Servidor OpenVPN iniciado!"
        else
            print_message "$RED" "❌ Falha ao iniciar servidor OpenVPN"
            print_message "$YELLOW" "💡 Verifique a configuração do servidor"
            exit 1
        fi
    else
        print_message "$GREEN" "✅ Servidor OpenVPN está rodando"
    fi

    print_message "$GREEN" "✅ Todos os pré-requisitos atendidos!"
}

# Função para listar clientes existentes
list_existing_clients() {
    print_message "$BLUE" "📋 Clientes existentes:"

    if ls /root/VPN/OpenVPN/easy-rsa/keys/*.crt 2>/dev/null | grep -v server.crt >/dev/null; then
        ls /root/VPN/OpenVPN/easy-rsa/keys/*.crt 2>/dev/null | grep -v server.crt | while read cert; do
            client_name=$(basename "$cert" .crt)
            creation_date=$(stat -c %y "$cert" | cut -d' ' -f1)
            print_message "$NC" "  • $client_name (criado em: $creation_date)"
        done
    else
        print_message "$YELLOW" "  Nenhum cliente criado ainda"
    fi
    echo ""
}

# INÍCIO DO SCRIPT PRINCIPAL
clear
print_message "$CYAN" "🔐 CRIADOR DE CLIENTE OPENVPN - ADMINISTRADOR"
print_message "$CYAN" "=============================================="
print_message "$YELLOW" "Rede VPN: *********/24 (sem conflitos)"
print_message "$YELLOW" "Fase: VPN Básica (sem banco de dados)"
echo ""

# Verificar pré-requisitos
check_prerequisites

echo ""

# Listar clientes existentes
list_existing_clients

# Solicitar senha de administração
print_message "$YELLOW" "🔐 AUTENTICAÇÃO"
echo -n "Digite a senha de administração: "
read -s ADMIN_PASSWORD
echo ""

if [ "$ADMIN_PASSWORD" != "etenopenvpn" ]; then
    print_message "$RED" "❌ Senha incorreta!"
    exit 1
fi

print_message "$GREEN" "✅ Senha correta!"

# Solicitar nome do dispositivo
print_message "$YELLOW" "📝 IDENTIFICAÇÃO DO DISPOSITIVO"
while true; do
    echo -n "Digite o nome único do dispositivo (ex: notebook-mauricio): "
    read CLIENT_NAME

    # Validar nome (apenas letras, números, hífen e underscore)
    if [[ ! "$CLIENT_NAME" =~ ^[a-zA-Z0-9_-]+$ ]]; then
        print_message "$RED" "❌ Nome inválido! Use apenas letras, números, _ ou -"
        continue
    fi

    # Verificar tamanho mínimo
    if [ ${#CLIENT_NAME} -lt 3 ]; then
        print_message "$RED" "❌ Nome muito curto! Use pelo menos 3 caracteres"
        continue
    fi

    # Verificar se já existe
    if [ -f "/root/VPN/OpenVPN/easy-rsa/keys/${CLIENT_NAME}.crt" ]; then
        print_message "$RED" "❌ Cliente '$CLIENT_NAME' já existe!"
        print_message "$YELLOW" "💡 Escolha um nome diferente ou use o comando de listagem"
        continue
    fi

    break
done

# Solicitar tipo de plataforma
print_message "$YELLOW" "🖥️ PLATAFORMA DO DISPOSITIVO"
echo "Selecione a plataforma:"
echo "1) Windows"
echo "2) Raspberry Pi"
echo "3) Linux/Ubuntu"
echo "4) Android"
echo "5) iOS"
echo "6) Outro"
read -p "Opção (1-6): " PLATFORM_OPTION

case $PLATFORM_OPTION in
    1) PLATFORM="Windows" ;;
    2) PLATFORM="Raspberry Pi" ;;
    3) PLATFORM="Linux/Ubuntu" ;;
    4) PLATFORM="Android" ;;
    5) PLATFORM="iOS" ;;
    6) PLATFORM="Outro" ;;
    *) PLATFORM="Não especificado" ;;
esac

print_message "$GREEN" "✅ Dispositivo: $CLIENT_NAME ($PLATFORM)"

# Confirmação final
echo ""
print_message "$YELLOW" "📋 RESUMO DA CRIAÇÃO:"
print_message "$NC" "• Nome do cliente: $CLIENT_NAME"
print_message "$NC" "• Plataforma: $PLATFORM"
print_message "$NC" "• Rede VPN: *********/24"
echo ""
read -p "Confirma a criação do cliente? (s/N): " CONFIRM

if [[ ! "$CONFIRM" =~ ^[Ss]$ ]]; then
    print_message "$YELLOW" "❌ Operação cancelada pelo usuário"
    exit 0
fi

# Criar certificado
print_message "$BLUE" "🔧 Criando certificado para $CLIENT_NAME..."

cd /root/VPN/OpenVPN/easy-rsa

# Verificar se vars existe
if [ ! -f "vars" ]; then
    print_message "$RED" "❌ Arquivo 'vars' não encontrado!"
    print_message "$YELLOW" "💡 Execute a configuração PKI primeiro"
    exit 1
fi

# Gerar certificado do cliente (modo não-interativo)
source vars
print_message "$BLUE" "🔄 Gerando certificado (aguarde)..."

# Usar expect se disponível, senão usar echo
if command -v expect >/dev/null 2>&1; then
    expect << EXPECTEOF
spawn ./build-key $CLIENT_NAME
expect "Country Name" { send "\r" }
expect "State or Province Name" { send "\r" }
expect "City Name" { send "\r" }
expect "Organization Name" { send "\r" }
expect "Organizational Unit Name" { send "\r" }
expect "Common Name" { send "\r" }
expect "Name" { send "\r" }
expect "Email Address" { send "\r" }
expect "challenge password" { send "\r" }
expect "optional company name" { send "\r" }
expect "Sign the certificate" { send "y\r" }
expect "commit" { send "y\r" }
expect eof
EXPECTEOF
else
    # Fallback para echo
    echo -e "\n\n\n\n\n\n\ny\ny\n" | ./build-key "$CLIENT_NAME" >/dev/null 2>&1
fi

# Verificar se certificado foi criado
if [ ! -f "keys/${CLIENT_NAME}.crt" ]; then
    print_message "$RED" "❌ Erro ao criar certificado!"
    print_message "$YELLOW" "💡 Verifique os logs e tente novamente"
    exit 1
fi

print_message "$GREEN" "✅ Certificado criado com sucesso!"

# Criar arquivo .ovpn
cat > /root/VPN/OpenVPN/clients/${CLIENT_NAME}.ovpn << EOL
# Cliente OpenVPN - Fase 1
# Nome: $CLIENT_NAME
# Plataforma: $PLATFORM
# Criado em: $(date)
# Rede VPN: *********/24

client
dev tun
proto udp
remote vpn.evo-eden.site 1194
resolv-retry infinite
nobind
persist-key
persist-tun
comp-lzo
verb 3

<ca>
$(cat keys/ca.crt)
</ca>

<cert>
$(cat keys/${CLIENT_NAME}.crt)
</cert>

<key>
$(cat keys/${CLIENT_NAME}.key)
</key>

<tls-auth>
$(cat keys/ta.key)
</tls-auth>
key-direction 1
EOL

# Verificar se arquivo .ovpn foi criado
if [ ! -f "/root/VPN/OpenVPN/clients/${CLIENT_NAME}.ovpn" ]; then
    print_message "$RED" "❌ Erro ao criar arquivo .ovpn!"
    exit 1
fi

print_message "$GREEN" "✅ Arquivo .ovpn criado com sucesso!"

# Registrar cliente em arquivo de controle simples (Fase 1)
echo "$(date '+%Y-%m-%d %H:%M:%S'),$CLIENT_NAME,$PLATFORM,created" >> /root/VPN/OpenVPN/logs/clients-registry.log

# Verificar tamanho do arquivo
file_size=$(stat -c%s "/root/VPN/OpenVPN/clients/${CLIENT_NAME}.ovpn")
if [ "$file_size" -lt 1000 ]; then
    print_message "$YELLOW" "⚠️ Arquivo .ovpn parece muito pequeno ($file_size bytes)"
    print_message "$YELLOW" "� Verifique se todos os certificados foram incluídos"
fi

# Mostrar próximo IP provável
CONNECTED_CLIENTS=$(grep -c "CLIENT_LIST" /root/VPN/OpenVPN/logs/openvpn-status.log 2>/dev/null || echo "0")
NEXT_IP=$((CONNECTED_CLIENTS + 2))

# Estatísticas finais
echo ""
print_message "$GREEN" "🎉 CLIENTE CRIADO COM SUCESSO!"
print_message "$GREEN" "================================"
echo ""
print_message "$BLUE" "📋 INFORMAÇÕES DO CLIENTE:"
print_message "$NC" "• Nome: $CLIENT_NAME"
print_message "$NC" "• Plataforma: $PLATFORM"
print_message "$NC" "• Arquivo: /root/VPN/OpenVPN/clients/${CLIENT_NAME}.ovpn"
print_message "$NC" "• Tamanho: $file_size bytes"
print_message "$NC" "• Rede VPN: *********/24"
print_message "$NC" "• IP provável: 10.12.0.$NEXT_IP"
echo ""

print_message "$YELLOW" "� TRANSFERÊNCIA DO ARQUIVO:"
print_message "$NC" "• SCP: scp <EMAIL>:/root/VPN/OpenVPN/clients/${CLIENT_NAME}.ovpn ."
print_message "$NC" "• HTTP: Disponível via API na porta 8080"
echo ""

print_message "$YELLOW" "📋 PRÓXIMOS PASSOS PARA O CLIENTE:"
case $PLATFORM in
    "Windows")
        print_message "$NC" "1. Baixar e instalar OpenVPN GUI"
        print_message "$NC" "2. Copiar ${CLIENT_NAME}.ovpn para C:\\Program Files\\OpenVPN\\config\\"
        print_message "$NC" "3. Conectar via OpenVPN GUI"
        print_message "$NC" "4. Configurar TightVNC para acesso remoto"
        ;;
    "Raspberry Pi"|"Linux/Ubuntu")
        print_message "$NC" "1. Baixar ${CLIENT_NAME}.ovpn"
        print_message "$NC" "2. sudo cp ${CLIENT_NAME}.ovpn /etc/openvpn/client/"
        print_message "$NC" "3. sudo systemctl enable openvpn-client@${CLIENT_NAME}"
        print_message "$NC" "4. sudo systemctl start openvpn-client@${CLIENT_NAME}"
        ;;
    *)
        print_message "$NC" "1. Baixar o arquivo ${CLIENT_NAME}.ovpn"
        print_message "$NC" "2. Instalar OpenVPN no dispositivo"
        print_message "$NC" "3. Importar o arquivo .ovpn"
        print_message "$NC" "4. Conectar à VPN"
        ;;
esac

echo ""
print_message "$CYAN" "✨ Cliente $CLIENT_NAME pronto para uso!"

# Perguntar se deseja criar outro cliente
echo ""
read -p "Deseja criar outro cliente? (s/N): " CREATE_ANOTHER

if [[ "$CREATE_ANOTHER" =~ ^[Ss]$ ]]; then
    print_message "$BLUE" "🔄 Reiniciando script..."
    exec "$0"
fi

print_message "$GREEN" "👋 Script finalizado!"
EOF

chmod +x /root/VPN/OpenVPN/scripts/create-client-interactive.sh
```

### 4.2 API VPN no Docker

```bash
# Criar Dockerfile para API VPN
cat > /root/VPN/OpenVPN/api/Dockerfile << 'EOF'
FROM node:18-alpine

# Instalar dependências do sistema
RUN apk add --no-cache bash curl openssh-client

# Criar diretório da aplicação
WORKDIR /app

# Copiar arquivos da aplicação
COPY package*.json ./
RUN npm install

COPY . .

# Expor porta da API
EXPOSE 8080

# Comando para iniciar a aplicação
CMD ["npm", "start"]
EOF

# Criar package.json para API
cat > /root/VPN/OpenVPN/api/package.json << 'EOF'
{
  "name": "vpn-api",
  "version": "1.0.0",
  "description": "API para automação de criação de clientes VPN",
  "main": "server.js",
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "morgan": "^1.10.0",
    "multer": "^1.4.5"
  },
  "devDependencies": {
    "nodemon": "^3.0.1"
  }
}
EOF

# Criar servidor da API
cat > /root/VPN/OpenVPN/api/server.js << 'EOF'
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 8080;

// Middlewares
app.use(helmet({
    contentSecurityPolicy: false, // Permitir Cloudflare
    crossOriginEmbedderPolicy: false
}));
app.use(cors({
    origin: ['https://vpn.evo-eden.site', 'http://vpn.evo-eden.site'],
    credentials: true
}));
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Middleware para detectar Cloudflare
app.use((req, res, next) => {
    // Headers do Cloudflare
    const cfRay = req.headers['cf-ray'];
    const cfConnectingIp = req.headers['cf-connecting-ip'];

    if (cfRay) {
        console.log(`Requisição via Cloudflare - Ray: ${cfRay}, IP Real: ${cfConnectingIp}`);
    }

    next();
});

// Função para criar cliente VPN
function createVPNClient(password, clientName, platform) {
    return new Promise((resolve, reject) => {
        // Verificar senha
        if (password !== 'etenopenvpn') {
            return reject(new Error('Senha incorreta'));
        }

        // Validar nome do cliente
        if (!/^[a-zA-Z0-9_-]+$/.test(clientName)) {
            return reject(new Error('Nome do cliente inválido'));
        }

        // Verificar se cliente já existe
        const certPath = `/vpn-data/easy-rsa/keys/${clientName}.crt`;
        if (fs.existsSync(certPath)) {
            return reject(new Error('Cliente já existe'));
        }

        // Script para criar cliente
        const script = `
            cd /vpn-data/easy-rsa
            source vars
            echo -e "\\n\\n\\n\\n\\n\\n\\ny\\ny\\n" | ./build-key "${clientName}" >/dev/null 2>&1

            if [ $? -ne 0 ]; then
                echo "ERROR:Certificate generation failed"
                exit 1
            fi

            # Criar arquivo .ovpn
            cat > "/vpn-data/clients/${clientName}.ovpn" << EOL
# Cliente OpenVPN - Criado automaticamente via API Docker
# Nome: ${clientName}
# Plataforma: ${platform}
# Criado em: $(date)
# Rede VPN: *********/24

client
dev tun
proto udp
remote vpn.evo-eden.site 1194
resolv-retry infinite
nobind
persist-key
persist-tun
comp-lzo
verb 3

<ca>
$(cat keys/ca.crt)
</ca>

<cert>
$(cat keys/${clientName}.crt)
</cert>

<key>
$(cat keys/${clientName}.key)
</key>

<tls-auth>
$(cat keys/ta.key)
</tls-auth>
key-direction 1
EOL

            # Registrar no log
            echo "$(date '+%Y-%m-%d %H:%M:%S'),${clientName},${platform},created_auto_api" >> /vpn-data/logs/clients-registry.log

            echo "SUCCESS:Client created successfully"
        `;

        exec(script, (error, stdout, stderr) => {
            if (error) {
                console.error('Erro ao criar cliente:', error);
                return reject(new Error('Erro ao gerar certificado'));
            }

            if (stdout.includes('SUCCESS')) {
                console.log(`Cliente ${clientName} criado com sucesso`);
                resolve({
                    success: true,
                    message: 'Cliente criado com sucesso',
                    clientName: clientName,
                    platform: platform
                });
            } else {
                reject(new Error('Falha na criação do certificado'));
            }
        });
    });
}

// Rota para criar cliente
app.post('/api/create-client', async (req, res) => {
    try {
        const { password, clientName, platform } = req.body;

        console.log(`Requisição para criar cliente: ${clientName} (${platform})`);

        if (!password || !clientName || !platform) {
            return res.status(400).json({
                success: false,
                error: 'Parâmetros obrigatórios: password, clientName, platform'
            });
        }

        const result = await createVPNClient(password, clientName, platform);

        res.json(result);
    } catch (error) {
        console.error('Erro na API:', error.message);
        res.status(400).json({
            success: false,
            error: error.message
        });
    }
});

// Rota para download do arquivo .ovpn
app.get('/api/download/:clientName', (req, res) => {
    const { clientName } = req.params;
    const filePath = `/vpn-data/clients/${clientName}.ovpn`;

    if (!fs.existsSync(filePath)) {
        return res.status(404).json({
            success: false,
            error: 'Arquivo não encontrado'
        });
    }

    res.download(filePath, `${clientName}.ovpn`);
});

// Rota de health check
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        message: 'API VPN funcionando',
        timestamp: new Date().toISOString()
    });
});

// Iniciar servidor
app.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 API VPN rodando na porta ${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
});
EOF

# Criar docker-compose para API VPN
cat > /root/VPN/OpenVPN/api/docker-compose.yml << 'EOF'
version: '3.8'

services:
  api-vpn:
    build: .
    container_name: API-VPN
    ports:
      - "8080:8080"
    volumes:
      - /root/VPN/OpenVPN:/vpn-data
    environment:
      - NODE_ENV=production
      - PORT=8080
    restart: unless-stopped
    networks:
      - vpn-network
    labels:
      - "traefik.enable=false"  # Não expor via Traefik por enquanto
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  vpn-network:
    driver: bridge
EOF

# Criar diretório da API
mkdir -p /root/VPN/OpenVPN/api

echo "✅ Arquivos da API Docker criados em /root/VPN/OpenVPN/api/"
echo "📋 Para iniciar a API:"
echo "   cd /root/VPN/OpenVPN/api"
echo "   docker-compose up -d"
echo ""
echo "🌐 Teste da API via Cloudflare:"
echo "   curl https://vpn.evo-eden.site:8080/api/health"
echo "   curl http://vpn.evo-eden.site:8080/api/health"
```

### 4.3 Script Automático para Windows (PowerShell)

```bash
cat > /root/VPN/OpenVPN/scripts/windows-setup.ps1 << 'EOF'
# Script de Configuração OpenVPN para Windows - AUTOMÁTICO
# Executar como Administrador

param(
    [string]$ServerHost = "vpn.evo-eden.site",
    [int]$ApiPort = 8080
)

# Cores para output
$Host.UI.RawUI.ForegroundColor = "Cyan"
Write-Host "=== Configurador OpenVPN Windows - AUTOMÁTICO ==="
Write-Host "Rede VPN: *********/24"
Write-Host ""

# Solicitar senha
$Host.UI.RawUI.ForegroundColor = "Yellow"
$Password = Read-Host "Digite a senha de administração" -AsSecureString
$PlainPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($Password))

if ($PlainPassword -ne "etenopenvpn") {
    $Host.UI.RawUI.ForegroundColor = "Red"
    Write-Host "❌ Senha incorreta!"
    exit 1
}

$Host.UI.RawUI.ForegroundColor = "Green"
Write-Host "✅ Senha correta!"
Write-Host ""

# Solicitar nome do dispositivo
do {
    $Host.UI.RawUI.ForegroundColor = "White"
    $DeviceName = Read-Host "Digite o nome único do dispositivo (CN)"

    if ($DeviceName -notmatch "^[a-zA-Z0-9_-]+$") {
        $Host.UI.RawUI.ForegroundColor = "Red"
        Write-Host "❌ Nome inválido! Use apenas letras, números, _ ou -"
        continue
    }

    break
} while ($true)

$Host.UI.RawUI.ForegroundColor = "Blue"
Write-Host "📋 Criando cliente automaticamente: $DeviceName"
Write-Host ""

# Verificar conectividade com a API
Write-Host "🔍 Verificando conectividade com servidor..." -ForegroundColor Blue
try {
    $testUrl = "http://${ServerHost}:${ApiPort}/api/health"
    $response = Invoke-RestMethod -Uri $testUrl -TimeoutSec 10
    if ($response.success) {
        Write-Host "✅ API VPN acessível!" -ForegroundColor Green
    } else {
        throw "API não respondeu corretamente"
    }
} catch {
    Write-Host "❌ Erro: API VPN não está acessível" -ForegroundColor Red
    Write-Host "💡 Verifique se a API está rodando no servidor" -ForegroundColor Yellow
    Read-Host "Pressione ENTER para sair"
    exit 1
}

# Instalar OpenVPN automaticamente
Write-Host "`n📦 Verificando OpenVPN..." -ForegroundColor Yellow
$openVpnPath = "C:\Program Files\OpenVPN"
if (-not (Test-Path $openVpnPath)) {
    Write-Host "📥 Baixando e instalando OpenVPN..." -ForegroundColor Blue
    try {
        $downloadUrl = "https://swupdate.openvpn.org/community/releases/OpenVPN-2.6.8-I001-amd64.msi"
        $downloadPath = "$env:TEMP\OpenVPN-installer.msi"

        Invoke-WebRequest -Uri $downloadUrl -OutFile $downloadPath -UseBasicParsing
        Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"$downloadPath`" /quiet /norestart" -Wait

        if (Test-Path $openVpnPath) {
            Write-Host "✅ OpenVPN instalado com sucesso!" -ForegroundColor Green
            Remove-Item $downloadPath -Force
        } else {
            throw "Falha na instalação"
        }
    } catch {
        Write-Host "❌ Erro ao instalar OpenVPN: $_" -ForegroundColor Red
        Write-Host "💡 Baixe manualmente de: https://openvpn.net/community-downloads/" -ForegroundColor Yellow
        Read-Host "Pressione ENTER para sair"
        exit 1
    }
} else {
    Write-Host "✅ OpenVPN já está instalado!" -ForegroundColor Green
}

# Criar cliente via API
Write-Host "`n🔧 Criando certificado automaticamente..." -ForegroundColor Yellow
try {
    $apiUrl = "http://${ServerHost}:${ApiPort}/api/create-client"
    $body = @{
        password = $PlainPassword
        clientName = $DeviceName
        platform = "Windows"
    } | ConvertTo-Json

    $response = Invoke-RestMethod -Uri $apiUrl -Method POST -Body $body -ContentType "application/json" -TimeoutSec 30

    if ($response.success) {
        Write-Host "✅ Certificado criado com sucesso!" -ForegroundColor Green
    } else {
        throw $response.error
    }
} catch {
    Write-Host "❌ Erro ao criar certificado: $_" -ForegroundColor Red
    Read-Host "Pressione ENTER para sair"
    exit 1
}

# Baixar configuração automaticamente
Write-Host "`n📥 Baixando configuração VPN..." -ForegroundColor Blue
try {
    $downloadUrl = "http://${ServerHost}:${ApiPort}/api/download/${DeviceName}"
    $configPath = "C:\Program Files\OpenVPN\config\${DeviceName}.ovpn"

    Invoke-WebRequest -Uri $downloadUrl -OutFile $configPath -UseBasicParsing

    if (Test-Path $configPath) {
        Write-Host "✅ Configuração baixada: $configPath" -ForegroundColor Green
    } else {
        throw "Arquivo não foi criado"
    }
} catch {
    Write-Host "❌ Erro ao baixar configuração: $_" -ForegroundColor Red
    Read-Host "Pressione ENTER para sair"
    exit 1
}

# Instruções do TightVNC
Write-Host "`n🖥️ CONFIGURAÇÃO DO TIGHTVNC" -ForegroundColor Cyan
Write-Host "=" * 40 -ForegroundColor Cyan
Write-Host "`n📥 1. DOWNLOAD:" -ForegroundColor Yellow
Write-Host "   • Acesse: https://www.tightvnc.com/download.php" -ForegroundColor White
Write-Host "   • Baixe: TightVNC 2.8.x (Windows Installer)" -ForegroundColor White
Write-Host "`n🔧 2. CONFIGURAÇÃO:" -ForegroundColor Yellow
Write-Host "   • Instale marcando 'TightVNC Server'" -ForegroundColor White
Write-Host "   • Defina senha para conexões VNC" -ForegroundColor White
Write-Host "   • Configure porta 9145 (recomendado)" -ForegroundColor White
Write-Host "`n🔥 3. FIREWALL:" -ForegroundColor Yellow
Write-Host "   • Permita TightVNC no Windows Firewall" -ForegroundColor White

Write-Host "`n✅ CONFIGURAÇÃO CONCLUÍDA!" -ForegroundColor Green
Write-Host "🌐 Para conectar: Abra OpenVPN GUI > Conectar '$DeviceName'" -ForegroundColor Yellow

$Host.UI.RawUI.ForegroundColor = "White"
EOF

chmod +x /root/VPN/OpenVPN/scripts/windows-setup.ps1
```

### 4.4 Script Interativo para Raspberry Pi/Ubuntu

```bash
cat > /root/VPN/OpenVPN/scripts/raspberry-setup.sh << 'EOF'
#!/bin/bash

# Script de Configuração OpenVPN para Raspberry Pi/Ubuntu - TOTALMENTE AUTOMÁTICO
# Inclui instalação completa do OpenVPN e configuração VNC

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

SERVER_HOST="vpn.evo-eden.site"
API_PORT="8080"

# Função para exibir mensagens coloridas
print_message() {
    local color="$1"
    local message="$2"
    echo -e "${color}${message}${NC}"
}

# Função para verificar se está executando como root
check_root() {
    if [ "$EUID" -eq 0 ]; then
        print_message "$RED" "ERRO: Este script NÃO deve ser executado como root!"
        print_message "$YELLOW" "Execute como usuário normal (o script pedirá sudo quando necessário)"
        exit 1
    fi
}

# Função para verificar conectividade
check_connectivity() {
    print_message "$BLUE" "Verificando conectividade com servidor..."

    if ! ping -c 1 "$SERVER_IP" >/dev/null 2>&1; then
        print_message "$RED" "ERRO: Não foi possível conectar ao servidor $SERVER_IP"
        print_message "$YELLOW" "Verifique sua conexão com a internet"
        exit 1
    fi

    # Verificar API
    if ! curl -s --connect-timeout 5 "http://$SERVER_HOST:$API_PORT/api/health" >/dev/null; then
        print_message "$RED" "ERRO: API VPN não está acessível na porta $API_PORT"
        print_message "$YELLOW" "Verifique se a API está rodando no servidor"
        exit 1
    fi

    print_message "$GREEN" "Servidor acessível!"
}

# Função para instalar dependências
install_dependencies() {
    print_message "$YELLOW" "Verificando e instalando dependências..."

    # Atualizar repositórios
    print_message "$BLUE" "Atualizando repositórios..."
    sudo apt update >/dev/null 2>&1

    # Lista de pacotes necessários
    local packages=("openvpn" "curl" "jq" "systemd")
    local to_install=()

    # Verificar quais pacotes precisam ser instalados
    for package in "${packages[@]}"; do
        if ! dpkg -l | grep -q "^ii  $package "; then
            to_install+=("$package")
        fi
    done

    # Instalar pacotes necessários
    if [ ${#to_install[@]} -gt 0 ]; then
        print_message "$BLUE" "Instalando: ${to_install[*]}"
        sudo apt install -y "${to_install[@]}" >/dev/null 2>&1

        if [ $? -eq 0 ]; then
            print_message "$GREEN" "Dependências instaladas com sucesso!"
        else
            print_message "$RED" "Erro ao instalar dependências"
            exit 1
        fi
    else
        print_message "$GREEN" "Todas as dependências já estão instaladas!"
    fi
}

# Função para criar cliente automaticamente via SSH
create_client_remote() {
    local client_name="$1"
    local platform="Raspberry Pi"

    # Script para executar no servidor
    local remote_script="
    cd /root/VPN/OpenVPN/easy-rsa
    source vars

    # Verificar se cliente já existe
    if [ -f \"keys/${client_name}.crt\" ]; then
        echo \"ERROR:Client already exists\"
        exit 1
    fi

    # Gerar certificado
    echo -e \"\n\n\n\n\n\n\ny\ny\n\" | ./build-key \"$client_name\" >/dev/null 2>&1

    if [ \$? -ne 0 ]; then
        echo \"ERROR:Certificate generation failed\"
        exit 1
    fi

    # Criar arquivo .ovpn
    cat > \"/root/VPN/OpenVPN/clients/${client_name}.ovpn\" << EOL
# Cliente OpenVPN - Criado automaticamente
# Nome: $client_name
# Plataforma: $platform
# Criado em: \$(date)
# Rede VPN: *********/24

client
dev tun
proto udp
remote $SERVER_IP 1194
resolv-retry infinite
nobind
persist-key
persist-tun
comp-lzo
verb 3

<ca>
\$(cat keys/ca.crt)
</ca>

<cert>
\$(cat keys/${client_name}.crt)
</cert>

<key>
\$(cat keys/${client_name}.key)
</key>

<tls-auth>
\$(cat keys/ta.key)
</tls-auth>
key-direction 1
EOL

    # Registrar no log
    echo \"\$(date '+%Y-%m-%d %H:%M:%S'),$client_name,$platform,created_auto\" >> /root/VPN/OpenVPN/logs/clients-registry.log

    echo \"SUCCESS:Client created successfully\"
    "

    # Executar no servidor via SSH
    echo -e "${BLUE}� Conectando ao servidor...${NC}"
    echo "💡 Será solicitada a senha do servidor SSH"

    result=$(ssh -o StrictHostKeyChecking=no root@$SERVER_IP "$remote_script" 2>/dev/null)

    if echo "$result" | grep -q "SUCCESS"; then
        echo -e "${GREEN}✅ Certificado criado com sucesso!${NC}"
        return 0
    else
        echo -e "${RED}❌ Erro ao criar certificado: $result${NC}"
        return 1
    fi
}

# Criar cliente no servidor
if create_client_remote "$DEVICE_NAME"; then
    echo ""
    echo -e "${BLUE}📥 Baixando arquivo de configuração...${NC}"

    # Baixar arquivo .ovpn
    if scp -o StrictHostKeyChecking=no root@$SERVER_IP:/root/VPN/OpenVPN/clients/${DEVICE_NAME}.ovpn ./ 2>/dev/null; then
        echo -e "${GREEN}✅ Arquivo baixado: ${DEVICE_NAME}.ovpn${NC}"

        # Configurar OpenVPN automaticamente
        echo ""
        echo -e "${BLUE}🔧 Configurando OpenVPN...${NC}"

        sudo cp "${DEVICE_NAME}.ovpn" /etc/openvpn/client.conf
        sudo systemctl enable openvpn@client

        echo ""
# INÍCIO DO SCRIPT PRINCIPAL
clear
print_message "$CYAN" "CONFIGURADOR AUTOMÁTICO OPENVPN + VNC - RASPBERRY PI/UBUNTU"
print_message "$CYAN" "=============================================================="
print_message "$YELLOW" "Rede VPN: *********/24"
echo ""

# Verificar se não está executando como root
check_root

# Verificar conectividade
check_connectivity

# Instalar dependências
install_dependencies

# Solicitar senha de administração
print_message "$YELLOW" "AUTENTICAÇÃO"
echo -n "Digite a senha de administração: "
read -s PASSWORD
echo ""

if [ "$PASSWORD" != "etenopenvpn" ]; then
    print_message "$RED" "Senha incorreta!"
    exit 1
fi

print_message "$GREEN" "Senha correta!"

# Solicitar nome do dispositivo
print_message "$YELLOW" "IDENTIFICAÇÃO DO DISPOSITIVO"
while true; do
    echo -n "Digite o nome único do dispositivo (ex: raspberry-mauricio): "
    read DEVICE_NAME

    if [[ ! "$DEVICE_NAME" =~ ^[a-zA-Z0-9_-]+$ ]]; then
        print_message "$RED" "Nome inválido! Use apenas letras, números, _ ou -"
        continue
    fi

    if [ ${#DEVICE_NAME} -lt 3 ]; then
        print_message "$RED" "Nome muito curto! Use pelo menos 3 caracteres"
        continue
    fi

    break
done

print_message "$GREEN" "Nome do dispositivo: $DEVICE_NAME"

# Criar cliente VPN via API
print_message "$YELLOW" "CRIAÇÃO DO CERTIFICADO VPN"
api_url="http://$SERVER_HOST:$API_PORT/api/create-client"
json_data="{\"password\":\"$PASSWORD\",\"clientName\":\"$DEVICE_NAME\",\"platform\":\"Raspberry Pi\"}"

response=$(curl -s -X POST -H "Content-Type: application/json" -d "$json_data" "$api_url" 2>/dev/null)

if [ $? -ne 0 ]; then
    print_message "$RED" "Erro de conexão com a API"
    exit 1
fi

success=$(echo "$response" | jq -r '.success' 2>/dev/null)

if [ "$success" != "true" ]; then
    error=$(echo "$response" | jq -r '.error' 2>/dev/null)
    print_message "$RED" "Erro ao criar certificado: $error"
    exit 1
fi

print_message "$GREEN" "Certificado criado com sucesso!"

# Baixar configuração
print_message "$YELLOW" "DOWNLOAD DA CONFIGURAÇÃO"
config_path="/tmp/${DEVICE_NAME}.ovpn"
download_url="http://$SERVER_HOST:$API_PORT/api/download/$DEVICE_NAME"

if ! curl -s -o "$config_path" "$download_url"; then
    print_message "$RED" "Erro ao baixar configuração"
    exit 1
fi

if [ ! -f "$config_path" ] || [ ! -s "$config_path" ]; then
    print_message "$RED" "Arquivo de configuração inválido"
    exit 1
fi

print_message "$GREEN" "Configuração baixada: $config_path"

# Configurar OpenVPN
print_message "$YELLOW" "CONFIGURAÇÃO DO OPENVPN"
sudo mkdir -p /etc/openvpn/client
sudo cp "$config_path" "/etc/openvpn/client/${DEVICE_NAME}.conf"

if [ $? -eq 0 ]; then
    print_message "$GREEN" "Configuração instalada!"
    sudo systemctl enable "openvpn-client@${DEVICE_NAME}"
    print_message "$GREEN" "Serviço OpenVPN configurado!"
else
    print_message "$RED" "Erro ao configurar OpenVPN"
    exit 1
fi

# Configurar VNC
print_message "$CYAN" "CONFIGURAÇÃO VNC"
print_message "$CYAN" "==============================="

if command -v vncserver >/dev/null 2>&1; then
    print_message "$GREEN" "VNC Server já está disponível!"
    print_message "$YELLOW" "CONFIGURAÇÃO RECOMENDADA:"
    print_message "$NC" "1. Execute: vncserver"
    print_message "$NC" "2. Defina uma senha quando solicitado"
    print_message "$NC" "3. Para usar porta 9145: vncserver :45"
    print_message "$NC" "4. Para parar: vncserver -kill :45"
else
    print_message "$YELLOW" "VNC não encontrado. Instalando TightVNC..."
    sudo apt install -y tightvncserver >/dev/null 2>&1

    if [ $? -eq 0 ]; then
        print_message "$GREEN" "TightVNC instalado!"
        print_message "$YELLOW" "Para configurar:"
        print_message "$NC" "1. Execute: vncserver"
        print_message "$NC" "2. Defina senha quando solicitado"
    else
        print_message "$YELLOW" "Não foi possível instalar VNC automaticamente"
        print_message "$NC" "Execute manualmente: sudo apt install tightvncserver"
    fi
fi

print_message "$YELLOW" "FIREWALL:"
print_message "$NC" "• Para porta 9145: sudo ufw allow 9145"
print_message "$NC" "• Para porta padrão: sudo ufw allow 5901"

# Instruções finais
print_message "$GREEN" "CONFIGURAÇÃO CONCLUÍDA COM SUCESSO!"
print_message "$GREEN" "============================================"

print_message "$YELLOW" "PRÓXIMOS PASSOS:"
print_message "$GREEN" "1. OpenVPN instalado e configurado"
print_message "$GREEN" "2. Certificado VPN criado: $DEVICE_NAME"
print_message "$GREEN" "3. Serviço configurado: openvpn-client@$DEVICE_NAME"
print_message "$YELLOW" "4. Configure o VNC conforme instruções acima"

print_message "$YELLOW" "COMO CONECTAR À VPN:"
print_message "$NC" "1. sudo systemctl start openvpn-client@$DEVICE_NAME"
print_message "$NC" "2. sudo systemctl status openvpn-client@$DEVICE_NAME"
print_message "$NC" "3. Para logs: sudo journalctl -u openvpn-client@$DEVICE_NAME -f"

print_message "$YELLOW" "APÓS CONECTAR:"
print_message "$NC" "• Seu IP na VPN será: 10.12.0.X"
print_message "$NC" "• Outras máquinas poderão acessar via VNC: 10.12.0.X:9145"
print_message "$NC" "• Você poderá acessar outras máquinas na rede *********/24"

print_message "$CYAN" "Configuração automática concluída!"
read -p "Pressione ENTER para finalizar"
EOF

chmod +x /root/VPN/OpenVPN/scripts/raspberry-setup.sh
```

### 4.5 Script de Inicialização da API Docker

```bash
cat > /root/VPN/OpenVPN/scripts/start-vpn-automation.sh << 'EOF'
#!/bin/bash

# Script para inicializar automação da VPN com Docker

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 Iniciando sistema de automação VPN com Docker...${NC}"

# Verificar se Docker está instalado
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker não está instalado!${NC}"
    echo -e "${YELLOW}💡 Instale o Docker primeiro: curl -fsSL https://get.docker.com | sh${NC}"
    exit 1
fi

# Verificar se Docker Compose está instalado
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose não está instalado!${NC}"
    echo -e "${YELLOW}💡 Instale o Docker Compose primeiro${NC}"
    exit 1
fi

# Verificar se OpenVPN está rodando
if ! systemctl is-active --quiet openvpn@server; then
    echo -e "${YELLOW}🔄 OpenVPN não está rodando. Iniciando...${NC}"
    systemctl start openvpn@server
    sleep 3

    if systemctl is-active --quiet openvpn@server; then
        echo -e "${GREEN}✅ OpenVPN iniciado com sucesso!${NC}"
    else
        echo -e "${RED}❌ Falha ao iniciar OpenVPN${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ OpenVPN já está rodando${NC}"
fi

# Verificar se API Docker já está rodando
if docker ps | grep -q "API-VPN"; then
    echo -e "${GREEN}✅ API VPN (Docker) já está rodando${NC}"
else
    echo -e "${BLUE}🔄 Iniciando API VPN no Docker...${NC}"

    # Ir para diretório da API
    cd /root/VPN/OpenVPN/api

    # Verificar se arquivos da API existem
    if [ ! -f "docker-compose.yml" ]; then
        echo -e "${RED}❌ Arquivos da API não encontrados!${NC}"
        echo -e "${YELLOW}💡 Execute primeiro a seção 4.2 para criar os arquivos da API${NC}"
        exit 1
    fi

    # Construir e iniciar container
    docker-compose up -d --build

    # Aguardar inicialização
    sleep 5

    # Verificar se container está rodando
    if docker ps | grep -q "API-VPN"; then
        echo -e "${GREEN}✅ API VPN iniciada no Docker!${NC}"
    else
        echo -e "${RED}❌ Falha ao iniciar API VPN${NC}"
        echo -e "${YELLOW}💡 Verifique os logs: docker-compose logs${NC}"
        exit 1
    fi
fi

# Configurar firewall para API (se necessário)
if ! ufw status | grep -q "8080"; then
    echo -e "${BLUE}🔧 Configurando firewall para API...${NC}"
    ufw allow 8080/tcp
fi

# Testar API
echo -e "${BLUE}🔍 Testando API...${NC}"
if curl -s --connect-timeout 5 "http://localhost:8080/api/health" >/dev/null; then
    echo -e "${GREEN}✅ API respondendo corretamente!${NC}"
else
    echo -e "${YELLOW}⚠️ API pode estar iniciando ainda...${NC}"
fi

echo ""
echo -e "${GREEN}✅ Sistema de automação VPN iniciado com sucesso!${NC}"
echo ""
echo -e "${BLUE}📋 Status dos serviços:${NC}"
echo -e "   - OpenVPN: ${GREEN}$(systemctl is-active openvpn@server)${NC}"
echo -e "   - API VPN (Docker): ${GREEN}$(docker ps | grep -q "API-VPN" && echo "ativo" || echo "inativo")${NC}"
echo -e "   - Container: ${GREEN}$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep API-VPN || echo "não encontrado")${NC}"
echo ""
echo -e "${YELLOW}🌐 Clientes podem agora usar os scripts automáticos:${NC}"
echo -e "   - Windows: windows-setup.ps1"
echo -e "   - Raspberry Pi: raspberry-setup.sh"
echo ""
echo -e "${BLUE}📊 Para monitorar:${NC}"
echo -e "   - Logs da API: docker-compose logs -f"
echo -e "   - Status: docker ps"
echo -e "   - Health check: curl http://localhost:8080/api/health"
EOF

chmod +x /root/VPN/OpenVPN/scripts/start-vpn-automation.sh
```

## 🖥️ Etapa 5: Configuração para Windows

### 5.1 Instalação do Cliente

1. Baixar OpenVPN GUI: <https://openvpn.net/community-downloads/>
2. Instalar como administrador
3. Executar script PowerShell para configuração automática
4. Copiar arquivo .ovpn para: `C:\Program Files\OpenVPN\config\`

## 🍓 Etapa 6: Configuração para Raspberry Pi

### 6.1 Instalação no Raspberry Pi

```bash
# No Raspberry Pi
sudo apt update
sudo apt install -y openvpn

# Copiar arquivo de configuração
sudo cp cliente-raspberry.ovpn /etc/openvpn/client.conf

# Configurar inicialização automática
sudo systemctl enable openvpn@client
sudo systemctl start openvpn@client
```

### 6.2 Configuração VNC no Raspberry Pi

```bash
# Instalar VNC Server
sudo apt install -y tightvncserver

# Configurar VNC na porta 9145
vncserver :45 -geometry 1920x1080 -depth 24

# Configurar inicialização automática do VNC
cat > /home/<USER>/start-vnc.sh << 'EOF'
#!/bin/bash
vncserver :45 -geometry 1920x1080 -depth 24
EOF

chmod +x /home/<USER>/start-vnc.sh

# Adicionar ao crontab para iniciar com o sistema
echo "@reboot /home/<USER>/start-vnc.sh" | crontab -
```

### 6.3 Script de Monitoramento (Raspberry Pi)

```bash
cat > /home/<USER>/vpn-monitor.sh << 'EOF'
#!/bin/bash
while true; do
    if ! pgrep openvpn > /dev/null; then
        echo "$(date): Reconectando VPN..."
        sudo systemctl restart openvpn@client
    fi

    # Verificar se VNC está rodando
    if ! pgrep Xvnc > /dev/null; then
        echo "$(date): Reiniciando VNC..."
        /home/<USER>/start-vnc.sh
    fi

    sleep 60
done
EOF

chmod +x /home/<USER>/vpn-monitor.sh
```

## 🚀 Etapa 7: Inicialização e Testes

### 7.1 Iniciar Servidor OpenVPN
```bash
systemctl enable openvpn@server
systemctl start openvpn@server
systemctl status openvpn@server
```

### 7.2 Criar Clientes de Teste
```bash
cd /root/VPN/OpenVPN/scripts
./create-client-interactive.sh
# Usar senha: etenopenvpn
# Nomes sugeridos: windows-01, raspberry-01, etc.
```

### 7.3 Script para Visualizar IPs Atribuídos
```bash
cat > /root/VPN/OpenVPN/scripts/show-clients.sh << 'EOF'
#!/bin/bash

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== Status dos Clientes OpenVPN ===${NC}"
echo -e "${YELLOW}Rede VPN: *********/24${NC}"
echo ""

# Verificar se o servidor está rodando
if ! systemctl is-active --quiet openvpn@server; then
    echo -e "${RED}❌ Servidor OpenVPN não está rodando!${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Servidor OpenVPN ativo${NC}"
echo ""

# Mostrar clientes conectados atualmente
echo -e "${BLUE}📱 Clientes Conectados Atualmente:${NC}"
if [ -f "/root/VPN/OpenVPN/logs/openvpn-status.log" ]; then
    grep "CLIENT_LIST" /root/VPN/OpenVPN/logs/openvpn-status.log | while read line; do
        CLIENT_NAME=$(echo $line | cut -d',' -f2)
        CLIENT_IP=$(echo $line | cut -d',' -f3)
        CONNECTED_SINCE=$(echo $line | cut -d',' -f4)
        echo -e "  ${GREEN}●${NC} $CLIENT_NAME - IP: $CLIENT_IP - Conectado desde: $CONNECTED_SINCE"
    done
else
    echo -e "  ${YELLOW}Nenhum cliente conectado no momento${NC}"
fi

echo ""

# Mostrar IPs persistentes (histórico)
echo -e "${BLUE}📋 IPs Atribuídos (Persistentes):${NC}"
if [ -f "/root/VPN/OpenVPN/logs/ipp.txt" ]; then
    while read line; do
        if [ ! -z "$line" ]; then
            CLIENT_NAME=$(echo $line | cut -d',' -f1)
            CLIENT_IP=$(echo $line | cut -d',' -f2)
            echo -e "  ${BLUE}○${NC} $CLIENT_NAME → $CLIENT_IP"
        fi
    done < /root/VPN/OpenVPN/logs/ipp.txt
else
    echo -e "  ${YELLOW}Nenhum IP atribuído ainda${NC}"
fi

echo ""

# Mostrar certificados criados
echo -e "${BLUE}🔐 Certificados Criados:${NC}"
if [ -d "/root/VPN/OpenVPN/easy-rsa/keys" ]; then
    ls /root/VPN/OpenVPN/easy-rsa/keys/*.crt 2>/dev/null | grep -v server.crt | while read cert; do
        CLIENT_NAME=$(basename $cert .crt)
        echo -e "  ${GREEN}✓${NC} $CLIENT_NAME"
    done
else
    echo -e "  ${YELLOW}Nenhum certificado de cliente criado${NC}"
fi

echo ""

# Mostrar próximo IP disponível
LAST_IP=$(tail -1 /root/VPN/OpenVPN/logs/ipp.txt 2>/dev/null | cut -d',' -f2 | cut -d'.' -f4)
if [ -z "$LAST_IP" ]; then
    NEXT_IP=2
else
    NEXT_IP=$((LAST_IP + 1))
fi

echo -e "${BLUE}📍 Próximo IP disponível: 10.12.0.$NEXT_IP${NC}"
EOF

chmod +x /root/VPN/OpenVPN/scripts/show-clients.sh
```

### 7.4 Como Funciona a Atribuição de IPs

#### 📍 **Sistema de Atribuição Automática**

O OpenVPN usa um sistema inteligente de atribuição de IPs:

```text
🏗️ Estrutura da Rede *********/24:
├── *********     → Servidor VPN (reservado)
├── *********-254 → Pool para clientes (253 IPs disponíveis)
└── Arquivo ipp.txt → Mantém associação Cliente ↔ IP
```

#### 🔄 **Processo de Atribuição**

1. **Primeira Conexão**: Cliente recebe próximo IP disponível (ex: *********)
2. **Reconexão**: Cliente sempre recebe o mesmo IP (persistência)
3. **Arquivo ipp.txt**: Mantém histórico `nome_cliente,IP_atribuído`

#### 📋 **Exemplo Prático**

```bash
# Visualizar IPs atribuídos
./show-clients.sh

# Exemplo de saída:
# windows-01 → *********
# raspberry-01 → *********
# laptop-joao → *********
```

### 7.5 Verificação de Conectividade

```bash
# Verificar status do servidor (logs organizados)
tail -f /root/VPN/OpenVPN/logs/openvpn.log

# Verificar clientes conectados e seus IPs
./show-clients.sh

# Testar conectividade interna VPN (nova rede *********/24)
ping *********   # Primeiro cliente
ping *********   # Segundo cliente

# Verificar que não há conflito com outras VPNs
ping ********   # Deve falhar - VPN diferente
ping *********  # Deve falhar - VPN diferente

# Testar acesso SSH via VPN (usar IPs reais atribuídos)
ssh usuario@*********   # SSH para primeiro cliente
ssh pi@*********       # SSH para segundo cliente

# Testar porta VNC (usar IPs reais atribuídos)
nmap -p 9145 *********  # Verificar VNC no primeiro cliente
nmap -p 9145 *********  # Verificar VNC no segundo cliente

# Verificar isolamento da rede VPN
ip route | grep 10.12.0   # Deve mostrar apenas rotas da nova VPN
```

## 📊 Etapa 8: Monitoramento e Manutenção

### 8.1 Script de Monitoramento Básico - Fase 1

```bash
cat > /root/VPN/OpenVPN/scripts/monitor-vpn-basic.sh << 'EOF'
#!/bin/bash

# Configurações
LOG_FILE="/root/VPN/OpenVPN/logs/vpn-monitor.log"
STATUS_FILE="/root/VPN/OpenVPN/logs/openvpn-status.log"
CLIENTS_LOG="/root/VPN/OpenVPN/logs/clients-registry.log"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo "$(date): [FASE 1] Verificando status da VPN (rede *********/24)..." >> $LOG_FILE

# Verificar se o serviço está rodando
if ! systemctl is-active --quiet openvpn@server; then
    echo "$(date): ERRO - Serviço OpenVPN parado. Reiniciando..." >> $LOG_FILE
    systemctl restart openvpn@server
    sleep 5
fi

# Verificar clientes conectados
if [ -f "$STATUS_FILE" ]; then
    CLIENTS_CONNECTED=$(grep "CLIENT_LIST" "$STATUS_FILE" | wc -l)
    echo "$(date): $CLIENTS_CONNECTED clientes conectados na rede *********/24" >> $LOG_FILE

    # Listar clientes conectados
    if [ $CLIENTS_CONNECTED -gt 0 ]; then
        echo "$(date): Clientes online:" >> $LOG_FILE
        grep "CLIENT_LIST" "$STATUS_FILE" | while read line; do
            CLIENT_NAME=$(echo $line | cut -d',' -f2)
            CLIENT_IP=$(echo $line | cut -d',' -f3)
            CONNECTED_SINCE=$(echo $line | cut -d',' -f4)
            echo "$(date):   - $CLIENT_NAME ($CLIENT_IP) desde $CONNECTED_SINCE" >> $LOG_FILE
        done
    fi
else
    echo "$(date): AVISO - Arquivo de status não encontrado" >> $LOG_FILE
fi

# Verificar uso de banda (básico)
if [ -f "$STATUS_FILE" ]; then
    TOTAL_BYTES_RX=$(grep "bytes_received" "$STATUS_FILE" | tail -1 | cut -d',' -f3 2>/dev/null || echo "0")
    TOTAL_BYTES_TX=$(grep "bytes_sent" "$STATUS_FILE" | tail -1 | cut -d',' -f4 2>/dev/null || echo "0")
    echo "$(date): Tráfego total - RX: $TOTAL_BYTES_RX bytes, TX: $TOTAL_BYTES_TX bytes" >> $LOG_FILE
fi

# Verificar isolamento da rede (não deve haver conflitos)
ROUTE_CONFLICTS=$(ip route | grep -E "10\.8\.0|10\.10\.0" | wc -l)
if [ $ROUTE_CONFLICTS -gt 0 ]; then
    echo "$(date): AVISO - Possível conflito de rotas detectado com outras VPNs" >> $LOG_FILE
    ip route | grep -E "10\.8\.0|10\.10\.0" >> $LOG_FILE
fi

# Verificar conectividade da interface VPN
if ip addr show tun0 >/dev/null 2>&1; then
    VPN_IP=$(ip addr show tun0 | grep "inet " | awk '{print $2}' | cut -d'/' -f1)
    echo "$(date): Interface VPN ativa - IP: $VPN_IP" >> $LOG_FILE
else
    echo "$(date): ERRO - Interface VPN (tun0) não encontrada" >> $LOG_FILE
fi

# Estatísticas básicas de clientes registrados
if [ -f "$CLIENTS_LOG" ]; then
    TOTAL_CLIENTS=$(grep "created" "$CLIENTS_LOG" | wc -l)
    echo "$(date): Total de clientes registrados: $TOTAL_CLIENTS" >> $LOG_FILE
fi

echo "$(date): [FASE 1] Monitoramento concluído." >> $LOG_FILE
EOF

chmod +x /root/VPN/OpenVPN/scripts/monitor-vpn-basic.sh
```

### 8.2 Script de Visualização de Status - Fase 1

```bash
cat > /root/VPN/OpenVPN/scripts/show-vpn-status.sh << 'EOF'
#!/bin/bash

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== Status da VPN - Fase 1 ===${NC}"
echo -e "${YELLOW}Rede VPN: *********/24${NC}"
echo ""

# Verificar se o servidor está rodando
if systemctl is-active --quiet openvpn@server; then
    echo -e "${GREEN}✅ Servidor OpenVPN: ATIVO${NC}"
else
    echo -e "${RED}❌ Servidor OpenVPN: INATIVO${NC}"
    exit 1
fi

# Mostrar informações da interface VPN
if ip addr show tun0 >/dev/null 2>&1; then
    VPN_IP=$(ip addr show tun0 | grep "inet " | awk '{print $2}')
    echo -e "${GREEN}✅ Interface VPN: $VPN_IP${NC}"
else
    echo -e "${RED}❌ Interface VPN: NÃO ENCONTRADA${NC}"
fi

echo ""

# Mostrar clientes conectados
echo -e "${BLUE}📱 Clientes Conectados:${NC}"
if [ -f "/root/VPN/OpenVPN/logs/openvpn-status.log" ]; then
    CONNECTED=$(grep "CLIENT_LIST" /root/VPN/OpenVPN/logs/openvpn-status.log | wc -l)

    if [ $CONNECTED -gt 0 ]; then
        grep "CLIENT_LIST" /root/VPN/OpenVPN/logs/openvpn-status.log | while read line; do
            CLIENT_NAME=$(echo $line | cut -d',' -f2)
            CLIENT_IP=$(echo $line | cut -d',' -f3)
            CONNECTED_SINCE=$(echo $line | cut -d',' -f4)
            BYTES_RX=$(echo $line | cut -d',' -f5)
            BYTES_TX=$(echo $line | cut -d',' -f6)

            echo -e "  ${GREEN}●${NC} $CLIENT_NAME"
            echo -e "    IP: $CLIENT_IP"
            echo -e "    Conectado: $CONNECTED_SINCE"
            echo -e "    Tráfego: RX=$BYTES_RX, TX=$BYTES_TX"
            echo ""
        done
    else
        echo -e "  ${YELLOW}Nenhum cliente conectado${NC}"
    fi
else
    echo -e "  ${RED}Arquivo de status não encontrado${NC}"
fi

# Mostrar clientes registrados
echo -e "${BLUE}📋 Clientes Registrados:${NC}"
if [ -f "/root/VPN/OpenVPN/logs/clients-registry.log" ]; then
    TOTAL_REGISTERED=$(grep "created" /root/VPN/OpenVPN/logs/clients-registry.log | wc -l)
    echo -e "  Total: $TOTAL_REGISTERED clientes"
    echo ""

    echo -e "${BLUE}Últimos 5 clientes criados:${NC}"
    tail -5 /root/VPN/OpenVPN/logs/clients-registry.log | while read line; do
        DATE=$(echo $line | cut -d',' -f1)
        CLIENT=$(echo $line | cut -d',' -f2)
        PLATFORM=$(echo $line | cut -d',' -f3)
        echo -e "  ${GREEN}✓${NC} $CLIENT ($PLATFORM) - $DATE"
    done
else
    echo -e "  ${YELLOW}Nenhum cliente registrado ainda${NC}"
fi

echo ""

# Mostrar próximo IP disponível
LAST_IP=$(grep "CLIENT_LIST" /root/VPN/OpenVPN/logs/openvpn-status.log 2>/dev/null | tail -1 | cut -d',' -f3 | cut -d'.' -f4)
if [ -z "$LAST_IP" ]; then
    NEXT_IP=2
else
    NEXT_IP=$((LAST_IP + 1))
fi

echo -e "${BLUE}📍 Próximo IP disponível: 10.12.0.$NEXT_IP${NC}"
EOF

chmod +x /root/VPN/OpenVPN/scripts/show-vpn-status.sh
```

### 8.3 Configuração do Crontab - Fase 1

```bash
# Adicionar monitoramento automático
echo "*/5 * * * * /root/VPN/OpenVPN/scripts/monitor-vpn-basic.sh" | crontab -

# Verificar crontab
echo "Crontab configurado:"
crontab -l
```

## 🔒 Etapa 9: Configurações de Segurança Avançadas

### 9.1 Configuração de Fail2Ban
```bash
apt install -y fail2ban

cat > /etc/fail2ban/jail.local << 'EOF'
[openvpn]
enabled = true
port = 1194
protocol = udp
filter = openvpn
logpath = /var/log/openvpn.log
maxretry = 3
bantime = 3600
EOF

systemctl restart fail2ban
```

### 9.2 Rotação de Logs
```bash
cat > /etc/logrotate.d/openvpn-custom << 'EOF'
/root/VPN/OpenVPN/logs/openvpn.log {
    daily
    missingok
    rotate 30
    compress
    notifempty
    create 0640 root root
    postrotate
        systemctl reload openvpn@server
    endscript
}

/root/VPN/OpenVPN/logs/vpn-monitor.log {
    weekly
    missingok
    rotate 12
    compress
    notifempty
    create 0640 root root
}
EOF
```

## �️ Como Usar a VPN para Acesso Remoto

### Cenário de Uso: Acesso VNC via VPN (Nova Rede *********/24)

1. **Conectar à VPN**: Cliente se conecta usando arquivo .ovpn
2. **Identificar IP VPN**: Verificar IP atribuído na nova rede (ex: *********0)
3. **Conectar via VNC**: Usar VNC Viewer para IP_VPN:9145
4. **Acesso SSH**: ssh usuario@IP_VPN

### Exemplo Prático (Nova Rede Sem Conflitos)

```bash
# 1. Cliente Windows conecta na VPN e recebe IP *********5 (nova rede)
# 2. Raspberry Pi conecta na VPN e recebe IP *********05 (nova rede)

# Do Windows, acessar Raspberry Pi:
# VNC: *********05:9145
# SSH: ssh pi@*********05

# Do Raspberry Pi, acessar Windows:
# VNC: *********5:9145
# SSH: ssh usuario@*********5 (se configurado)

# IMPORTANTE: Esta VPN é isolada das outras redes:
# - ********/24 (VPN existente 1)
# - *********/24 (VPN existente 2)
# - *********/24 (NOVA VPN - sem conflitos)
```

### Vantagens desta Configuração

- ✅ **Sem exposição à internet**: Apenas comunicação interna
- ✅ **Acesso direto**: Todas as máquinas se "veem" como rede local
- ✅ **Segurança**: Certificados individuais para cada cliente
- ✅ **Flexibilidade**: Adicionar/remover clientes facilmente
- ✅ **Performance**: Sem overhead de NAT para internet

## 📝 Implementação da Fase 1 - Passo a Passo

### 🚀 Ordem de Execução Recomendada

#### **Etapa 1: Preparação do Servidor**
```bash
# 1. Criar estrutura de diretórios
mkdir -p /root/VPN/OpenVPN/{easy-rsa,clients,scripts,logs,configs}

# 2. Instalar dependências
apt update && apt upgrade -y
apt install -y openvpn easy-rsa iptables-persistent curl wget

# 3. Configurar firewall
ufw allow 1194/udp
ufw allow ssh
ufw allow 9145/tcp
ufw --force enable
```

#### **Etapa 2: Configuração PKI**
```bash
# 1. Configurar easy-rsa
cd /root/VPN/OpenVPN
cp -r /usr/share/easy-rsa .
cd easy-rsa

# 2. Configurar variáveis e gerar certificados
# (Seguir seção 2.2 e 2.3 do documento)
```

#### **Etapa 3: Configuração OpenVPN**
```bash
# 1. Criar configuração do servidor
# (Seguir seção 3.1 do documento)

# 2. Configurar roteamento
# (Seguir seção 3.2 do documento)
```

#### **Etapa 4: Scripts de Automação**
```bash
# 1. Criar scripts de criação de clientes
# (Executar comandos das seções 4.1, 4.2 e 4.3)

# 2. Tornar scripts executáveis
chmod +x /root/VPN/OpenVPN/scripts/*.sh
```

#### **Etapa 5: Inicialização e Testes**
```bash
# 1. Iniciar servidor OpenVPN
systemctl enable openvpn@server
systemctl start openvpn@server

# 2. Verificar status
systemctl status openvpn@server

# 3. Inicializar sistema de automação
cd /root/VPN/OpenVPN/scripts
./start-vpn-automation.sh

# 4. Verificar status da VPN
./show-vpn-status.sh
```

#### **Etapa 6: Uso dos Scripts Automáticos**

**Para adicionar um novo cliente Windows:**
```bash
# 1. Cliente baixa o script windows-setup.ps1
# 2. Cliente executa como Administrador:
#    .\windows-setup.ps1
# 3. Cliente informa:
#    - Senha: etenopenvpn
#    - Nome único: ex: notebook-mauricio
# 4. Script faz tudo automaticamente!
```

**Para adicionar um novo cliente Raspberry Pi:**
```bash
# 1. Cliente baixa o script raspberry-setup.sh
# 2. Cliente executa:
#    chmod +x raspberry-setup.sh
#    ./raspberry-setup.sh
# 3. Cliente informa:
#    - Senha: etenopenvpn
#    - Nome único: ex: raspberry-mauricio
# 4. Script faz tudo automaticamente!
```

**Verificar clientes conectados:**
```bash
# No servidor, verificar status:
./show-vpn-status.sh

# Ver logs em tempo real:
tail -f /root/VPN/OpenVPN/logs/openvpn.log
```

#### **Etapa 6: Configuração de Monitoramento**
```bash
# 1. Configurar monitoramento automático
echo "*/5 * * * * /root/VPN/OpenVPN/scripts/monitor-vpn-basic.sh" | crontab -

# 2. Verificar logs
tail -f /root/VPN/OpenVPN/logs/vpn-monitor.log
```

### ✅ Checklist de Verificação - Fase 1

- [ ] **Estrutura de diretórios criada** em `/root/VPN/OpenVPN/`
- [ ] **Dependências instaladas** (OpenVPN, easy-rsa)
- [ ] **Firewall configurado** (porta 1194/UDP)
- [ ] **Certificados gerados** (CA, servidor, ta.key)
- [ ] **Servidor OpenVPN ativo** (`systemctl status openvpn@server`)
- [ ] **Interface tun0 criada** (`ip addr show tun0`)
- [ ] **Scripts funcionais** (create-client-interactive.sh)
- [ ] **Cliente de teste criado** e arquivo .ovpn gerado
- [ ] **Monitoramento ativo** (crontab configurado)
- [ ] **Logs funcionando** (arquivos em `/root/VPN/OpenVPN/logs/`)

### 🎯 Resultado Esperado da Fase 1

Após completar todas as etapas:

1. **Servidor VPN funcional** na rede *********/24
2. **Scripts automatizados** para criação de clientes
3. **Monitoramento básico** com logs
4. **Isolamento completo** das outras VPNs (10.8.0 e 10.10.0)
5. **Conectividade entre clientes** via VPN
6. **Acesso SSH e VNC** através da VPN

### 📋 Comandos Úteis - Fase 1

```bash
# Verificar status geral
/root/VPN/OpenVPN/scripts/show-vpn-status.sh

# Criar novo cliente
/root/VPN/OpenVPN/scripts/create-client-interactive.sh

# Monitorar logs em tempo real
tail -f /root/VPN/OpenVPN/logs/openvpn.log

# Verificar clientes conectados
grep "CLIENT_LIST" /root/VPN/OpenVPN/logs/openvpn-status.log

# Testar conectividade VPN
ping *********  # IP do servidor VPN
```

### 🔄 Próximas Fases

**Fase 2**: Sistema de Controle PostgreSQL
- Banco de dados para histórico detalhado
- Dashboard Node.js avançado
- Métricas e estatísticas

**Fase 3**: Restrição de Acesso Web
- Integração com monitor.evo-eden.site
- Controle de acesso por IP VPN
- Middleware Traefik

---
**Documento**: Fase 1 - VPN Básica
**Versão**: 2.0
**Data**: 2025-08-12

## 🔧 Sistema de Atribuição de IPs - Resumo Completo

### 📍 **Como os IPs são Criados para Cada Máquina**

#### 1. **Configuração Automática**
```text
Rede: *********/24
├── *********     → Servidor VPN (fixo)
├── *********     → Primeiro cliente
├── *********     → Segundo cliente
├── *********     → Terceiro cliente
└── ...até *********54
```

#### 2. **Processo de Atribuição**
1. **Cliente conecta** → OpenVPN verifica arquivo `ipp.txt`
2. **Se é novo** → Atribui próximo IP disponível
3. **Se já existe** → Usa o mesmo IP anterior (persistência)
4. **Salva associação** → `nome_cliente,IP` no arquivo `ipp.txt`

#### 3. **Scripts Criados**

##### 🖥️ **No Servidor (Administrador)**
- `create-client-interactive.sh` → Cria certificados com senha "etenopenvpn"
- `show-clients.sh` → Mostra IPs atribuídos e status
- `monitor-vpn.sh` → Monitora a VPN

##### 💻 **Para Windows**
- `windows-setup.ps1` → Script PowerShell para configuração
- Solicita senha "etenopenvpn" e nome único do dispositivo

##### 🍓 **Para Raspberry Pi**
- `raspberry-setup.sh` → Script Bash para configuração
- Solicita senha "etenopenvpn" e nome único do dispositivo

#### 4. **Fluxo de Trabalho Automático - Fase 1**

```text
🚀 PROCESSO TOTALMENTE AUTOMÁTICO:

1. 📱 Cliente executa script (Windows/Raspberry)
   ├── Informa senha: "etenopenvpn"
   ├── Informa nome único: ex: "notebook-mauricio"
   └── Script conecta automaticamente ao servidor

2. 🤖 Automação no servidor (SEM intervenção manual):
   ├── Valida senha "etenopenvpn"
   ├── Verifica se nome é único
   ├── Gera certificado automaticamente
   ├── Cria arquivo notebook-mauricio.ovpn
   └── Disponibiliza para download

3. � Cliente baixa e configura automaticamente:
   ├── Download automático do arquivo .ovpn
   ├── Configuração automática do OpenVPN
   ├── Pronto para conectar à VPN
   └── IP automático atribuído (ex: *********)

4. 🌐 Cliente conecta e usa:
   ├── Conecta à VPN com um comando
   ├── Sempre recebe o mesmo IP (*********)
   ├── Acesso imediato a outras máquinas via VPN
   └── SSH e VNC funcionando automaticamente

⚡ TEMPO TOTAL: ~2-3 minutos (sem intervenção manual!)
```

#### 5. **Scripts Automáticos Disponíveis**

**🖥️ Para Windows:**
```powershell
# Baixar e executar:
.\windows-setup.ps1
# Informar: senha + nome único
# Resultado: VPN configurada automaticamente
```

**🍓 Para Raspberry Pi:**
```bash
# Baixar e executar:
./raspberry-setup.sh
# Informar: senha + nome único
# Resultado: VPN configurada e pronta para conectar
```

**🔧 No Servidor (uma vez apenas):**
```bash
# Inicializar automação:
./start-vpn-automation.sh
# Resultado: Sistema pronto para receber clientes
```

#### 6. **Vantagens do Sistema Automático**

- ✅ **Totalmente Automático**: Zero intervenção manual do administrador
- ✅ **Rápido**: 2-3 minutos do script até VPN funcionando
- ✅ **IPs Persistentes**: Cada máquina sempre tem o mesmo IP
- ✅ **Nomes Únicos**: CN impede duplicação de certificados
- ✅ **Senha Única**: "etenopenvpn" controla criação de clientes
- ✅ **Auto-configuração**: Scripts configuram OpenVPN automaticamente
- ✅ **Multiplataforma**: Windows e Raspberry Pi suportados
- ✅ **Isolado**: Rede *********/24 não conflita com outras VPNs
- ✅ **SSH Automático**: Conexão e configuração via SSH
- ✅ **Download Automático**: Arquivo .ovpn baixado automaticamente
- ✅ **Pronto para Usar**: VNC e SSH funcionam imediatamente

## 🆘 Troubleshooting

### Problemas Comuns

- **Conexão VPN recusada**: Verificar firewall e porta 1194/UDP
- **Certificados inválidos**: Regenerar com easy-rsa
- **Clientes não se comunicam**: Verificar `client-to-client` no servidor
- **VNC não conecta**: Verificar se porta 9145 está aberta e VNC rodando
- **SSH não funciona**: Verificar se SSH está habilitado nos clientes

### Comandos Úteis para Diagnóstico (Nova Rede *********/24)

```bash
# Status do serviço OpenVPN
systemctl status openvpn@server

# Logs em tempo real (organizados)
tail -f /root/VPN/OpenVPN/logs/openvpn.log

# Clientes conectados e seus IPs (nova localização)
cat /root/VPN/OpenVPN/logs/openvpn-status.log

# Testar conectividade VPN (nova rede)
ping *********  # IP do servidor VPN (nova rede)

# Verificar portas abertas em um cliente (nova rede)
nmap -p 22,9145 *********0  # SSH e VNC

# Testar conectividade VNC (nova rede)
telnet *********0 9145

# Verificar roteamento VPN (nova rede)
ip route | grep tun0
ip route | grep 10.12.0  # Verificar rotas específicas da nova VPN

# Verificar isolamento de rede (não deve haver conflitos)
ip route | grep -E "10\.8\.0|10\.10\.0"  # Deve estar vazio ou sem conflitos

# Verificar interface VPN
ifconfig tun0
ip addr show tun0

# Monitorar logs de monitoramento
tail -f /root/VPN/OpenVPN/logs/vpn-monitor.log
```

### Resolução de Problemas VNC

```bash
# No cliente (Raspberry Pi)
sudo systemctl status vncserver-x11-serviced
vncserver -list

# No cliente (Windows)
# Verificar se VNC Server está rodando
# Verificar configuração de porta 9145
```

---
**Autor**: Assistente IA Sênior  
**Data**: 2025-08-12  
**Versão**: 1.0
